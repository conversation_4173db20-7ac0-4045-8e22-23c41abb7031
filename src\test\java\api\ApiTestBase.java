package api;

import io.restassured.response.Response;
import org.slf4j.Logger;
import org.testng.Assert;
import utils.ExcelUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.util.*;

/**
 * Base class for API testing
 * Contains common functionality for all API tests
 */
public class ApiTestBase {
    protected Logger logger;
    protected String filePath;
    protected String sheetName;
    protected int url;
    protected int body;
    protected int Status;
    protected int ActualResult;
    protected int ExpectedResult;
    protected int tableName;
    protected ExcelUtils excelUtils;
    protected String authToken;

    /**
     * Constructor
     * @param logger Logger instance
     * @param filePath Excel file path
     * @param sheetName Excel sheet name
     * @param url URL column index
     * @param body Request body column index
     * @param Status Status column index
     * @param ActualResult Actual result column index
     * @param ExpectedResult Expected result column index
     * @param tableName Table name column index
     */
    public ApiTestBase(Logger logger, String filePath, String sheetName, int url, int body, int Status, int ActualResult, int ExpectedResult, int tableName) {
        this.logger = logger;
        this.filePath = filePath;
        this.sheetName = sheetName;
        this.url = url;
        this.body = body;
        this.Status = Status;
        this.ActualResult = ActualResult;
        this.ExpectedResult = ExpectedResult;
        this.tableName = tableName;
        this.excelUtils = new ExcelUtils();
    }

    /**
     * Set the authentication token
     * @param authToken Authentication token
     */
    public void setAuthToken(String authToken) {
        this.authToken = authToken;
    }

    /**
     * Get the request body from Excel
     * @param rowNum Excel row number
     * @return Request body
     * @throws Exception If any error occurs during the process
     */
    protected String getRequestBodyFromExcel(int rowNum) throws Exception {
        String requestBody = excelUtils.getCellData(filePath, sheetName, rowNum, body);
        logger.info("Request body from Excel: {}", requestBody);
        return requestBody;
    }

    /**
     * Update the Excel sheet with test results
     * @param rowNum Excel row number
     * @param status Test status (Passed/Failed)
     * @param result Test result message
     */
    protected void updateExcelSheet(int rowNum, String status, String result) {
        boolean testPassed = status.equals("Passed");
        try {
            excelUtils.setCellData(filePath, sheetName, rowNum, Status, status, testPassed);
            excelUtils.setCellData(filePath, sheetName, rowNum, ActualResult, result);
            logger.info("Updated Excel sheet with status '{}' and result '{}'", status, result);
        } catch (Exception e) {
            logger.warn("Could not update Excel sheet: {}. Test will continue.", e.getMessage());
            // Log the status and result even if we can't write to the Excel file
            logger.info("Test status: {}, Result: {}", status, result);
        }
    }

    /**
     * Get the test status from Excel
     * @param rowNum Excel row number
     * @return Test status
     */
    public String getTestStatus(int rowNum) {
        try {
            return excelUtils.getCellData(filePath, sheetName, rowNum, Status);
        } catch (Exception e) {
            logger.error("Error getting test status: {}", e.getMessage());
            return "";
        }
    }

    /**
     * Get the test status message from Excel
     * @param rowNum Excel row number
     * @return Test status message
     */
    public String getTestStatusMessage(int rowNum) {
        try {
            return excelUtils.getCellData(filePath, sheetName, rowNum, ActualResult);
        } catch (Exception e) {
            logger.error("Error getting test status message: {}", e.getMessage());
            return "";
        }
    }

    /**
     * Set the test status in Excel
     * @param rowNum Excel row number
     * @param status Test status
     */
    public void setTestStatus(int rowNum, String status) {
        try {
            boolean testPassed = status.equals("Passed");
            excelUtils.setCellData(filePath, sheetName, rowNum, Status, status, testPassed);
        } catch (Exception e) {
            logger.error("Error setting test status: {}", e.getMessage());
        }
    }

    /**
     * Set the test status message in Excel
     * @param rowNum Excel row number
     * @param message Test status message
     */
    public void setTestStatusMessage(int rowNum, String message) {
        try {
            excelUtils.setCellData(filePath, sheetName, rowNum, ActualResult, message);
        } catch (Exception e) {
            logger.error("Error setting test status message: {}", e.getMessage());
        }
    }

    /**
     * Extract ID from response body
     * @param responseBody Response body
     * @return Extracted ID
     * @throws Exception If any error occurs during the process
     */
    protected String extractIdFromResponseBody(String responseBody) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        JsonNode rootNode = mapper.readTree(responseBody);
        
        if (rootNode.has("id")) {
            return rootNode.get("id").asText();
        }
        
        // Try other common ID field names
        String[] possibleIdFields = {"ID", "Id", "_id", "uid", "uuid", "key"};
        for (String idField : possibleIdFields) {
            if (rootNode.has(idField)) {
                return rootNode.get(idField).asText();
            }
        }
        
        throw new Exception("Could not extract ID from response body");
    }

    /**
     * Compare two JSON responses
     * @param response1 First JSON response
     * @param response2 Second JSON response
     * @return True if the responses match, false otherwise
     */
    protected boolean compareResponses(String response1, String response2) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode node1 = mapper.readTree(response1);
            JsonNode node2 = mapper.readTree(response2);

            // Convert both nodes to maps for easier comparison
            Map<String, Object> map1 = convertJsonNodeToMap(node1);
            Map<String, Object> map2 = convertJsonNodeToMap(node2);

            // Fields to ignore in comparison
            Set<String> ignoreFields = new HashSet<>(Arrays.asList(
                // Standard timestamp fields
                "createdAt", "updatedAt", "createdBy", "updatedBy", "timestamp", "version",
                // Additional fields to skip as requested
                "created_by", "created_date", "last_modified_by", "last_modified_date",
                "createdBy", "createdDate", "lastModifiedBy", "lastModifiedDate"
            ));

            // Date fields that should be compared by date part only
            Set<String> dateFields = new HashSet<>(Arrays.asList(
                "date", "startDate", "endDate", "dueDate", "birthDate", "hireDate", "effectiveDate"
            ));

            // Compare the maps
            return compareMaps(map1, map2, ignoreFields, dateFields);
        } catch (Exception e) {
            logger.error("Error comparing responses: " + e.getMessage());
            return false;
        }
    }

    /**
     * Convert JsonNode to Map
     * @param node JsonNode to convert
     * @return Converted Map
     */
    protected Map<String, Object> convertJsonNodeToMap(JsonNode node) {
        Map<String, Object> map = new HashMap<>();
        Iterator<Map.Entry<String, JsonNode>> fields = node.fields();
        
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> field = fields.next();
            String key = field.getKey();
            JsonNode value = field.getValue();
            
            if (value.isObject()) {
                map.put(key, convertJsonNodeToMap(value));
            } else if (value.isArray()) {
                List<Object> list = new ArrayList<>();
                for (JsonNode item : value) {
                    if (item.isObject()) {
                        list.add(convertJsonNodeToMap(item));
                    } else {
                        list.add(item.asText());
                    }
                }
                map.put(key, list);
            } else {
                map.put(key, value.asText());
            }
        }
        
        return map;
    }

    /**
     * Compare two maps
     * @param map1 First map
     * @param map2 Second map
     * @param ignoreFields Fields to ignore
     * @param dateFields Date fields
     * @return True if the maps match, false otherwise
     */
    protected boolean compareMaps(Map<String, Object> map1, Map<String, Object> map2, Set<String> ignoreFields, Set<String> dateFields) {
        // Check if all keys in map1 are in map2
        for (String key : map1.keySet()) {
            if (ignoreFields.contains(key)) {
                continue;
            }
            
            if (!map2.containsKey(key)) {
                logger.warn("Key '{}' is in first map but not in second map", key);
                return false;
            }
            
            Object value1 = map1.get(key);
            Object value2 = map2.get(key);
            
            if (!compareValues(key, value1, value2, ignoreFields, dateFields)) {
                return false;
            }
        }
        
        // Check if all keys in map2 are in map1
        for (String key : map2.keySet()) {
            if (ignoreFields.contains(key)) {
                continue;
            }
            
            if (!map1.containsKey(key)) {
                logger.warn("Key '{}' is in second map but not in first map", key);
                return false;
            }
        }
        
        return true;
    }

    /**
     * Compare two values
     * @param key Key
     * @param value1 First value
     * @param value2 Second value
     * @param ignoreFields Fields to ignore
     * @param dateFields Date fields
     * @return True if the values match, false otherwise
     */
    @SuppressWarnings("unchecked")
    protected boolean compareValues(String key, Object value1, Object value2, Set<String> ignoreFields, Set<String> dateFields) {
        if (value1 == null && value2 == null) {
            return true;
        }
        
        if (value1 == null || value2 == null) {
            logger.warn("One value is null for key '{}': {} vs {}", key, value1, value2);
            return false;
        }
        
        if (value1 instanceof Map && value2 instanceof Map) {
            return compareMaps((Map<String, Object>) value1, (Map<String, Object>) value2, ignoreFields, dateFields);
        }
        
        if (value1 instanceof List && value2 instanceof List) {
            List<Object> list1 = (List<Object>) value1;
            List<Object> list2 = (List<Object>) value2;
            
            if (list1.size() != list2.size()) {
                logger.warn("Lists have different sizes for key '{}': {} vs {}", key, list1.size(), list2.size());
                return false;
            }
            
            for (int i = 0; i < list1.size(); i++) {
                if (!compareValues(key + "[" + i + "]", list1.get(i), list2.get(i), ignoreFields, dateFields)) {
                    return false;
                }
            }
            
            return true;
        }
        
        // Handle date fields
        if (dateFields.contains(key)) {
            // Extract date part only for comparison
            String date1 = value1.toString().split("T")[0];
            String date2 = value2.toString().split("T")[0];
            return date1.equals(date2);
        }
        
        // Handle primitive values
        if (!value1.equals(value2)) {
            logger.warn("Values don't match for key '{}': {} vs {}", key, value1, value2);
            return false;
        }
        
        return true;
    }
}
