package api;

import api.helpers.PostWithDynamicRequestBody;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.restassured.response.Response;
import org.slf4j.Logger;
import utils.TestConfiguration;
import utils.DatabaseValidationUtils;
import utils.Constants;
import utils.ConfigManager;

/**
 * Class for testing Filter API operations
 * This class handles requests where you pass a key name and value in the endpoint,
 * and the API returns filtered data based on those parameters.
 */
public class FilterApiTest extends ApiTestBase {
    private DatabaseValidationUtils dbUtils;

    /**
     * Constructor
     * @param logger Logger instance
     * @param filePath Excel file path
     * @param sheetName Excel sheet name
     * @param url URL column index
     * @param body Request body column index
     * @param Status Status column index
     * @param ActualResult Actual result column index
     * @param ExpectedResult Expected result column index
     * @param tableName Table name column index
     */
    public FilterApiTest(Logger logger, String filePath, String sheetName, int url, int body, int Status, int ActualResult, int ExpectedResult, int tableName) {
        super(logger, filePath, sheetName, url, body, Status, ActualResult, ExpectedResult, tableName);
        this.dbUtils = new DatabaseValidationUtils();
    }

    /**
     * Test the filter API by comparing the filtered response with the database record
     * @param rowNum Excel row number
     * @param accessToken Authentication token
     * @return The response from the filter API
     * @throws Exception If any error occurs during the process
     */
    public Response testFilterApi(int rowNum, String accessToken) throws Exception {
        // Step 1: Get the request body from Excel
        String originalRequestBody = getRequestBodyFromExcel(rowNum);

        // Step 2: Extract the filter key and value from the request body
        String[] filterParams = extractFilterParams(originalRequestBody);
        String filterKey = filterParams[0];
        String filterValue = filterParams[1];

        logger.info("Running Filter API test for row {} with filter key '{}' and value '{}'", rowNum, filterKey, filterValue);

        // Step 3: Create a request body for the filter API
        String requestBody = createFilterRequestBody(originalRequestBody, accessToken, filterKey, filterValue);

        // Step 4: Make the filter API request
        Response filterResponse = makeFilterApiRequest(rowNum, requestBody);

        if (filterResponse == null) {
            logger.error("Filter response is null");
            updateExcelSheet(rowNum, Constants.STATUS_FAILED, "Filter response is null");
            return null;
        }

        // Step 5: Store the filter response in the Expected Result column
        String filterResponseBody = filterResponse.getBody().asPrettyString();
        try {
            excelUtils.setCellData(filePath, sheetName, rowNum, ExpectedResult, filterResponseBody);
            logger.info("Stored filter response in Expected Result column");
        } catch (Exception e) {
            logger.warn("Could not store filter response in Expected Result column: {}. Will continue with test.", e.getMessage());
        }

        // Step 6: Validate the filter response with the database
        validateWithDatabase(rowNum, filterKey, filterValue, filterResponseBody);

        return filterResponse;
    }



    /**
     * Test the filter API by comparing the filtered response with the database record
     * @param rowNum Excel row number
     * @param accessToken Authentication token
     * @param filterKey The key to filter by
     * @param filterValue The value to filter by
     * @return The response from the filter API
     * @throws Exception If any error occurs during the process
     */
    public Response testFilterApi(int rowNum, String accessToken, String filterKey, String filterValue) throws Exception {
        logger.info("Running Filter API test for row {} with filter key '{}' and value '{}'", rowNum, filterKey, filterValue);

        // Step 1: Get the request body from Excel
        String originalRequestBody = getRequestBodyFromExcel(rowNum);

        // Step 2: Create a request body for the filter API
        String requestBody = createFilterRequestBody(originalRequestBody, accessToken, filterKey, filterValue);

        // Step 3: Make the filter API request
        Response filterResponse = makeFilterApiRequest(rowNum, requestBody);

        if (filterResponse == null) {
            logger.error("Filter response is null");
            updateExcelSheet(rowNum, Constants.STATUS_FAILED, "Filter response is null");
            return null;
        }

        // Step 4: Store the filter response in the Expected Result column
        String filterResponseBody = filterResponse.getBody().asPrettyString();
        try {
            excelUtils.setCellData(filePath, sheetName, rowNum, ExpectedResult, filterResponseBody);
            logger.info("Stored filter response in Expected Result column");
        } catch (Exception e) {
            logger.warn("Could not store filter response in Expected Result column: {}. Will continue with test.", e.getMessage());
        }

        // Step 5: Validate the filter response with the database
        validateWithDatabase(rowNum, filterKey, filterValue, filterResponseBody);

        return filterResponse;
    }

    /**
     * Extract filter key and value from the request body
     * @param requestBody The request body
     * @return Array with filter key and value
     * @throws Exception If any error occurs during the process
     */
    private String[] extractFilterParams(String requestBody) throws Exception {
        String[] result = new String[2];
        String defaultKey = "countryShortName";
        String defaultValue = "IN1";

        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readTree(requestBody);

            if (root.has(Constants.FIELD_ENDPOINT)) {
                String endpoint = root.get(Constants.FIELD_ENDPOINT).asText();

                // Check if the endpoint contains "/filter/"
                if (endpoint.contains("/filter/")) {
                    // Extract the part after "/filter/"
                    String filterPart = endpoint.substring(endpoint.indexOf("/filter/") + "/filter/".length());

                    // Split by "/" to get key and value
                    String[] parts = filterPart.split("/");

                    if (parts.length >= 2) {
                        result[0] = parts[0]; // Key
                        result[1] = parts[1]; // Value
                        logger.info("Extracted filter key '{}' and value '{}' from endpoint", result[0], result[1]);
                        return result;
                    }
                }
            }

            // If we couldn't extract the filter key and value, use defaults
            logger.warn("Could not extract filter key and value from request body, using defaults");
            result[0] = defaultKey;
            result[1] = defaultValue;
            return result;

        } catch (Exception e) {
            logger.error("Error extracting filter key and value from request body: {}", e.getMessage());
            result[0] = defaultKey;
            result[1] = defaultValue;
            return result;
        }
    }

    /**
     * Create a request body for the filter API
     * @param originalRequestBody The original request body
     * @param accessToken Authentication token
     * @param filterKey The key to filter by
     * @param filterValue The value to filter by
     * @return The request body for the filter API
     * @throws Exception If any error occurs during the process
     */
    private String createFilterRequestBody(String originalRequestBody, String accessToken, String filterKey, String filterValue) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode requestNode;

        try {
            // Try to parse the original request body as JSON
            JsonNode root = mapper.readTree(originalRequestBody);

            // Check if it already has the required structure
            if (root.has(Constants.FIELD_ENDPOINT) && root.has(Constants.FIELD_TYPE)) {
                // It already has the required structure, use it as is
                requestNode = root.deepCopy();

                // Extract the base path from the endpoint
                String endpoint = requestNode.get(Constants.FIELD_ENDPOINT).asText();
                String basePath = endpoint;

                // Remove any trailing "/list" or "/filter" from the endpoint
                if (basePath.endsWith("/list")) {
                    basePath = basePath.substring(0, basePath.length() - 5);
                    logger.info("Removed suffix '/list' from endpoint: {}", endpoint);
                } else if (basePath.contains("/filter")) {
                    basePath = basePath.substring(0, basePath.indexOf("/filter"));
                    logger.info("Removed '/filter' and anything after it from endpoint: {}", endpoint);
                }

                // Clean up the base path
                String cleanBasePath = basePath.trim();

                // Now add the /filter suffix with the key and value
                String filterEndpoint = cleanBasePath + "/filter/" + filterKey + "/" + filterValue;
                logger.info("Created filter endpoint: {}", filterEndpoint);

                // Update the request node
                requestNode.put(Constants.FIELD_ENDPOINT, filterEndpoint);
                requestNode.put(Constants.FIELD_TYPE, Constants.TYPE_GET);
                requestNode.putNull(Constants.FIELD_PAYLOAD);
            } else {
                // It doesn't have the required structure, create a new one
                requestNode = mapper.createObjectNode();
                requestNode.put(Constants.FIELD_ENDPOINT, ConfigManager.getApiBasePath() + ConfigManager.getDefaultEntityName() + "/filter/" + filterKey + "/" + filterValue);
                requestNode.put(Constants.FIELD_TYPE, Constants.TYPE_GET);
                requestNode.putNull(Constants.FIELD_PAYLOAD);
            }
        } catch (Exception e) {
            // If parsing fails, create a simple filter request
            requestNode = mapper.createObjectNode();
            requestNode.put(Constants.FIELD_ENDPOINT, ConfigManager.getApiBasePath() + ConfigManager.getDefaultEntityName() + "/filter/" + filterKey + "/" + filterValue);
            requestNode.put(Constants.FIELD_TYPE, Constants.TYPE_GET);
            requestNode.putNull(Constants.FIELD_PAYLOAD);
        }

        // Ensure the auth token is set
        requestNode.put(Constants.FIELD_AUTH, accessToken);

        // Convert to JSON string
        String filterRequestBody = mapper.writeValueAsString(requestNode);
        logger.info("Filter request body: {}", filterRequestBody);

        return filterRequestBody;
    }

    /**
     * Make a filter API request
     * @param rowNum Excel row number
     * @param requestBody Request body
     * @return Response from the filter API
     * @throws Exception If any error occurs during the process
     */
    private Response makeFilterApiRequest(int rowNum, String requestBody) throws Exception {
        PostWithDynamicRequestBody requestHandler = new PostWithDynamicRequestBody(logger, filePath, sheetName, url, body);
        Response filterResponse = requestHandler.post(rowNum, requestBody);

        if (filterResponse == null) {
            logger.error("Filter response is null");
            updateExcelSheet(rowNum, Constants.STATUS_FAILED, "Filter response is null");
            return null;
        }

        int statusCode = filterResponse.getStatusCode();
        String responseBody = filterResponse.getBody().asPrettyString();

        logger.info("Filter response status code: {}", statusCode);
        logger.info("Filter response body: {}", responseBody);

        return filterResponse;
    }

    /**
     * Validate the filter response with the database
     * @param rowNum Excel row number
     * @param filterKey The key to filter by
     * @param filterValue The value to filter by
     * @param filterResponseBody The filter response body
     * @throws Exception If any error occurs during the process
     */
    private void validateWithDatabase(int rowNum, String filterKey, String filterValue, String filterResponseBody) throws Exception {
        try {
            // Get the table name from Excel
            String tableName = excelUtils.getCellData(filePath, sheetName, rowNum, this.tableName);
            logger.info("Table name from Excel: {}", tableName);

            // Query the database with the filter key and value
            String dbRecord = dbUtils.getRecordByFilter(tableName, filterKey, filterValue);
            logger.info("Database record: {}", dbRecord);

            // Store the database record in the Actual Result column
            try {
                excelUtils.setCellData(filePath, sheetName, rowNum, ActualResult, dbRecord);
                logger.info("Stored database record in Actual Result column");
            } catch (Exception e) {
                logger.warn("Could not store database record in Actual Result column: {}. Will continue with test.", e.getMessage());
            }

            // Compare the database record with the filter response
            boolean isMatch = compareResponses(dbRecord, filterResponseBody);

            // Update the Excel sheet with the result
            if (isMatch) {
                updateExcelSheet(rowNum, Constants.STATUS_PASSED, Constants.MSG_DB_API_MATCH);
            } else {
                updateExcelSheet(rowNum, Constants.STATUS_FAILED, Constants.MSG_DB_API_MISMATCH);
            }
        } catch (Exception e) {
            logger.error("Error validating with database: {}", e.getMessage());
            updateExcelSheet(rowNum, Constants.STATUS_FAILED, "Error validating with database: " + e.getMessage());
        }
    }

    /**
     * Complete filter API test with assertions and logging
     * @param rowNum Excel row number
     * @param authToken Authentication token
     * @throws Exception If any error occurs during the process
     */
    public void filterApi(int rowNum, String authToken) throws Exception {
        logger.info("Running Filter API test");

        // Call the testFilterApi method without specifying filter key and value
        // The method will extract them from the request body in the Excel sheet
        Response response = testFilterApi(rowNum, authToken);

        // Verify that the response is not null
        org.testng.Assert.assertNotNull(response, "Filter API response should not be null");

        // Log the response status code
        logger.info("Filter API response status code: " + response.getStatusCode());

        // Check if the Excel sheet has been updated with the test results
        String testStatus = getTestStatus(rowNum);
        logger.info("Test status in Excel sheet: " + testStatus);

        // Verify that the test status is set
        org.testng.Assert.assertNotNull(testStatus, "Test status should not be null");
    }
}
