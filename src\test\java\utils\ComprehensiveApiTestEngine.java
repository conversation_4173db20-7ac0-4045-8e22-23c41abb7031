package utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;

/**
 * Comprehensive API Test Engine for complete CRUD operations
 * Handles POST, PUT, GET All, GET By ID, DELETE, PATCH with foreign key resolution
 */
public class ComprehensiveApiTestEngine {
    private static final Logger logger = LoggerFactory.getLogger(ComprehensiveApiTestEngine.class);

    private final UniversalApiHandler apiHandler;
    private final ExcelUtils excelUtils;
    private final DatabaseUtils databaseUtils;
    private final DynamicDataGenerator dataGenerator;
    private final DefectTracker defectTracker;
    private final ObjectMapper mapper;

    // Column mappings
    private final int urlCol;
    private final int bodyCol;
    private final int expectedResultCol;
    private final int actualResultCol;
    private final int statusCol;
    private final int defectIdCol;

    public ComprehensiveApiTestEngine(int urlCol, int bodyCol, int expectedResultCol,
                                    int actualResultCol, int statusCol, int defectIdCol) {
        this.apiHandler = new UniversalApiHandler();
        this.excelUtils = new ExcelUtils();
        this.databaseUtils = new DatabaseUtils(logger);
        this.dataGenerator = new DynamicDataGenerator();
        this.defectTracker = new DefectTracker();
        this.mapper = new ObjectMapper();

        this.urlCol = urlCol;
        this.bodyCol = bodyCol;
        this.expectedResultCol = expectedResultCol;
        this.actualResultCol = actualResultCol;
        this.statusCol = statusCol;
        this.defectIdCol = defectIdCol;
    }

    /**
     * Execute comprehensive API test for any operation
     */
    public void executeComprehensiveApiTest(String filePath, String sheetName, int rowNumber,
                                          String baseUrl, String authToken) {
        try {
            logger.info("=== Executing Comprehensive API Test - Sheet: {}, Row: {} ===", sheetName, rowNumber);

            // Read request body from Excel
            String requestBody = excelUtils.getCellData(filePath, sheetName, rowNumber, bodyCol);
            if (requestBody == null || requestBody.trim().isEmpty()) {
                updateExcelWithError(filePath, sheetName, rowNumber, "Request body is empty");
                return;
            }

            // Parse request to determine operation type
            JsonNode requestNode = mapper.readTree(requestBody);
            String httpMethod = requestNode.get("type").asText().toUpperCase();
            String endpoint = requestNode.get("endpoint").asText();

            logger.info("Processing {} operation for endpoint: {}", httpMethod, endpoint);

            // Execute based on HTTP method
            switch (httpMethod) {
                case "POST":
                    executePostOperation(filePath, sheetName, rowNumber, requestBody, baseUrl, authToken);
                    break;
                case "PUT":
                    executePutOperation(filePath, sheetName, rowNumber, requestBody, baseUrl, authToken);
                    break;
                case "GET":
                    if (endpoint.contains("getAll") || endpoint.endsWith("/list")) {
                        executeGetAllOperation(filePath, sheetName, rowNumber, requestBody, baseUrl, authToken);
                    } else {
                        executeGetByIdOperation(filePath, sheetName, rowNumber, requestBody, baseUrl, authToken);
                    }
                    break;
                case "DELETE":
                    executeDeleteOperation(filePath, sheetName, rowNumber, requestBody, baseUrl, authToken);
                    break;
                case "PATCH":
                    executePatchOperation(filePath, sheetName, rowNumber, requestBody, baseUrl, authToken);
                    break;
                default:
                    updateExcelWithError(filePath, sheetName, rowNumber, "Unsupported HTTP method: " + httpMethod);
            }

        } catch (Exception e) {
            logger.error("Error in comprehensive API test: {}", e.getMessage());
            updateExcelWithError(filePath, sheetName, rowNumber, "Test execution error: " + e.getMessage());
        }
    }

    /**
     * Execute POST operation with foreign key resolution
     */
    private void executePostOperation(String filePath, String sheetName, int rowNumber,
                                    String requestBody, String baseUrl, String authToken) {
        try {
            logger.info("=== Executing POST Operation ===");

            // Step 1: Resolve foreign keys
            String resolvedRequestBody = resolveForeignKeys(requestBody, baseUrl, authToken);

            // Step 2: Generate dynamic data
            String finalRequestBody = dataGenerator.processExcelJsonStructure(resolvedRequestBody);

            // Step 3: Execute POST request
            ApiResponse response = apiHandler.processApiRequest(finalRequestBody, baseUrl, authToken);

            // Step 4: Extract ID from response
            String createdId = extractIdFromResponse(response.getBody());

            // Step 5: Validate with database
            boolean isValid = validateWithDatabase(response.getBody(), createdId, requestBody);

            // Step 6: Status Code Validation - PRESERVE expected result from Excel
            String expectedStatusCode = excelUtils.getCellData(filePath, sheetName, rowNumber, expectedResultCol);
            String actualStatusCode = String.valueOf(response.getStatusCode());

            logger.info("Status Code Validation - Expected: {}, Actual: {}", expectedStatusCode, actualStatusCode);

            // Compare expected vs actual status code
            boolean statusCodeMatches = expectedStatusCode.trim().equals(actualStatusCode.trim());

            if (statusCodeMatches && response.isSuccess() && isValid) {
                updateExcelWithStatusCodeSuccess(filePath, sheetName, rowNumber, actualStatusCode);
            } else {
                String defectId = generateDefectWithTableName(filePath, sheetName, rowNumber, "POST", response,
                    String.format("Status code mismatch or validation failed. Expected: %s, Actual: %s",
                                expectedStatusCode, actualStatusCode));
                updateExcelWithStatusCodeFailure(filePath, sheetName, rowNumber, actualStatusCode, defectId);
            }

        } catch (Exception e) {
            logger.error("Error in POST operation: {}", e.getMessage());
            String defectId = generateDefect(rowNumber, "POST", null, "POST operation error: " + e.getMessage());
            updateExcelWithError(filePath, sheetName, rowNumber, "POST error: " + e.getMessage(), defectId);
        }
    }

    /**
     * Execute GET By ID operation with complete workflow
     */
    private void executeGetByIdOperation(String filePath, String sheetName, int rowNumber,
                                       String requestBody, String baseUrl, String authToken) {
        try {
            logger.info("=== Executing GET By ID Operation ===");

            // Step 1: Parse request to get entity information
            JsonNode requestNode = mapper.readTree(requestBody);
            String endpoint = requestNode.get("endpoint").asText();

            // Step 2: Determine entity name from endpoint
            String entityName = extractEntityNameFromEndpoint(endpoint);

            // Step 3: Hit getAll API first to get ID
            String getAllEndpoint = ApiConstants.getEntityEndpoints(entityName).getAllEndpoint();
            String getAllRequest = String.format(ApiConstants.GET_ALL_TEMPLATE, getAllEndpoint);

            ApiResponse getAllResponse = apiHandler.processApiRequest(getAllRequest, baseUrl, authToken);

            if (!getAllResponse.isSuccess()) {
                updateExcelWithError(filePath, sheetName, rowNumber, "Failed to get data for GetById test");
                return;
            }

            // Step 4: Extract 0th index ID
            String extractedId = extractZeroIndexId(getAllResponse.getBody());
            if (extractedId == null) {
                updateExcelWithError(filePath, sheetName, rowNumber, "No data found for GetById test");
                return;
            }

            // Step 5: Update endpoint with extracted ID
            String updatedEndpoint = updateEndpointWithId(endpoint, extractedId);

            // Step 6: Update Excel with new endpoint
            JsonNode updatedRequest = requestNode.deepCopy();
            ((ObjectNode) updatedRequest).put("endpoint", updatedEndpoint);
            String updatedRequestBody = mapper.writeValueAsString(updatedRequest);
            excelUtils.setCellData(filePath, sheetName, rowNumber, bodyCol, updatedRequestBody);

            // Step 7: Execute GetById request
            ApiResponse getByIdResponse = apiHandler.processApiRequest(updatedRequestBody, baseUrl, authToken);

            // Step 8: Get database data for comparison
            String tableName = ApiConstants.getTableName(entityName);
            String dbData = getDatabaseDataWithForeignKeys(tableName, extractedId);

            // Step 9: Compare API response with database data
            boolean isMatch = compareApiWithDatabase(getByIdResponse.getBody(), dbData);

            // Step 10: Update Excel with results
            if (getByIdResponse.isSuccess() && isMatch) {
                updateExcelWithSuccess(filePath, sheetName, rowNumber, dbData, getByIdResponse.getBody());
            } else {
                String defectId = generateDefect(rowNumber, "GET_BY_ID", getByIdResponse,
                                               "GetById validation failed");
                updateExcelWithFailure(filePath, sheetName, rowNumber, getByIdResponse.getBody(),
                                     dbData, defectId);
            }

        } catch (Exception e) {
            logger.error("Error in GET By ID operation: {}", e.getMessage());
            String defectId = generateDefect(rowNumber, "GET_BY_ID", null, "GetById error: " + e.getMessage());
            updateExcelWithError(filePath, sheetName, rowNumber, "GetById error: " + e.getMessage(), defectId);
        }
    }

    /**
     * Execute DELETE operation with complete workflow
     */
    private void executeDeleteOperation(String filePath, String sheetName, int rowNumber,
                                      String requestBody, String baseUrl, String authToken) {
        try {
            logger.info("=== Executing DELETE Operation ===");

            // Step 1: Get ID for deletion (similar to GetById workflow)
            JsonNode requestNode = mapper.readTree(requestBody);
            String endpoint = requestNode.get("endpoint").asText();
            String entityName = extractEntityNameFromEndpoint(endpoint);

            // Step 2: Get data to delete
            String getAllEndpoint = ApiConstants.getEntityEndpoints(entityName).getAllEndpoint();
            String getAllRequest = String.format(ApiConstants.GET_ALL_TEMPLATE, getAllEndpoint);

            ApiResponse getAllResponse = apiHandler.processApiRequest(getAllRequest, baseUrl, authToken);
            String extractedId = extractZeroIndexId(getAllResponse.getBody());

            if (extractedId == null) {
                updateExcelWithError(filePath, sheetName, rowNumber, "No data found for DELETE test");
                return;
            }

            // Step 3: Update endpoint with ID
            String updatedEndpoint = updateEndpointWithId(endpoint, extractedId);
            JsonNode updatedRequest = requestNode.deepCopy();
            ((ObjectNode) updatedRequest).put("endpoint", updatedEndpoint);
            String updatedRequestBody = mapper.writeValueAsString(updatedRequest);

            // Step 4: Execute DELETE request
            ApiResponse deleteResponse = apiHandler.processApiRequest(updatedRequestBody, baseUrl, authToken);

            // Step 5: Verify deletion by trying to get the record
            String getByIdEndpoint = ApiConstants.getEntityEndpoints(entityName).getGetByIdEndpoint(extractedId);
            String getByIdRequest = String.format(ApiConstants.GET_BY_ID_TEMPLATE, getByIdEndpoint);

            ApiResponse verifyResponse = apiHandler.processApiRequest(getByIdRequest, baseUrl, authToken);

            // Step 6: Check if record is deleted (should return 404 or empty)
            boolean isDeleted = !verifyResponse.isSuccess() ||
                              verifyResponse.getBody().contains("not found") ||
                              verifyResponse.getBody().equals("{}");

            // Step 7: Update Excel with results
            if (deleteResponse.isSuccess() && isDeleted) {
                updateExcelWithSuccess(filePath, sheetName, rowNumber, "Record deleted successfully",
                                     "DELETE operation successful");
            } else {
                String defectId = generateDefect(rowNumber, "DELETE", deleteResponse, "DELETE operation failed");
                updateExcelWithFailure(filePath, sheetName, rowNumber, deleteResponse.getBody(),
                                     "DELETE operation failed", defectId);
            }

        } catch (Exception e) {
            logger.error("Error in DELETE operation: {}", e.getMessage());
            String defectId = generateDefect(rowNumber, "DELETE", null, "DELETE error: " + e.getMessage());
            updateExcelWithError(filePath, sheetName, rowNumber, "DELETE error: " + e.getMessage(), defectId);
        }
    }

    /**
     * Resolve foreign keys by hitting getAll APIs
     */
    private String resolveForeignKeys(String requestBody, String baseUrl, String authToken) {
        try {
            JsonNode requestNode = mapper.readTree(requestBody);
            JsonNode payload = requestNode.get("payload");

            if (payload == null) {
                return requestBody;
            }

            // Find all foreign key fields
            Map<String, String> foreignKeyMappings = ApiConstants.getAllForeignKeyMappings();
            ObjectNode updatedPayload = payload.deepCopy();

            resolveForeignKeysRecursively(updatedPayload, foreignKeyMappings, baseUrl, authToken);

            // Update the request with resolved foreign keys
            ((ObjectNode) requestNode).set("payload", updatedPayload);
            return mapper.writeValueAsString(requestNode);

        } catch (Exception e) {
            logger.error("Error resolving foreign keys: {}", e.getMessage());
            return requestBody;
        }
    }

    /**
     * Recursively resolve foreign keys in nested objects
     */
    private void resolveForeignKeysRecursively(JsonNode node, Map<String, String> foreignKeyMappings,
                                             String baseUrl, String authToken) {
        if (node.isObject()) {
            ObjectNode objectNode = (ObjectNode) node;
            Iterator<Map.Entry<String, JsonNode>> fields = objectNode.fields();

            while (fields.hasNext()) {
                Map.Entry<String, JsonNode> field = fields.next();
                String fieldName = field.getKey();
                JsonNode fieldValue = field.getValue();

                // Check if this is a foreign key field
                if (foreignKeyMappings.containsKey(fieldName) &&
                    fieldValue.isTextual() && "{{foreign_key}}".equals(fieldValue.asText())) {

                    String entityName = foreignKeyMappings.get(fieldName);
                    String resolvedId = getForeignKeyId(entityName, baseUrl, authToken);

                    if (resolvedId != null) {
                        objectNode.put(fieldName, resolvedId);
                        logger.info("Resolved foreign key {} = {} from {}", fieldName, resolvedId, entityName);
                    }
                } else if (fieldValue.isObject() || fieldValue.isArray()) {
                    // Recursively process nested objects/arrays
                    resolveForeignKeysRecursively(fieldValue, foreignKeyMappings, baseUrl, authToken);
                }
            }
        } else if (node.isArray()) {
            for (JsonNode arrayElement : node) {
                resolveForeignKeysRecursively(arrayElement, foreignKeyMappings, baseUrl, authToken);
            }
        }
    }

    /**
     * Get foreign key ID by hitting getAll API
     */
    private String getForeignKeyId(String entityName, String baseUrl, String authToken) {
        try {
            ApiConstants.EntityEndpoints endpoints = ApiConstants.getEntityEndpoints(entityName);
            if (endpoints == null) {
                logger.warn("No endpoints found for entity: {}", entityName);
                return null;
            }

            String getAllRequest = String.format(ApiConstants.GET_ALL_TEMPLATE, endpoints.getAllEndpoint());
            ApiResponse response = apiHandler.processApiRequest(getAllRequest, baseUrl, authToken);

            if (response.isSuccess()) {
                return extractZeroIndexId(response.getBody());
            }

        } catch (Exception e) {
            logger.error("Error getting foreign key ID for {}: {}", entityName, e.getMessage());
        }

        return null;
    }

    /**
     * Extract entity name from endpoint
     */
    private String extractEntityNameFromEndpoint(String endpoint) {
        // Extract entity name from endpoint like "/core/api/CountryMaster/123"
        String[] parts = endpoint.split("/");
        for (int i = 0; i < parts.length; i++) {
            if ("api".equals(parts[i]) && i + 1 < parts.length) {
                return parts[i + 1];
            }
        }
        return "Unknown";
    }

    /**
     * Extract ID from API response
     */
    private String extractIdFromResponse(String responseBody) {
        try {
            JsonNode responseNode = mapper.readTree(responseBody);

            // Try different possible ID field names
            String[] idFields = {"id", "Id", "ID", "entityId", "recordId"};

            for (String idField : idFields) {
                if (responseNode.has(idField)) {
                    return responseNode.get(idField).asText();
                }
            }

            // If response is an array, get ID from first element
            if (responseNode.isArray() && responseNode.size() > 0) {
                JsonNode firstElement = responseNode.get(0);
                for (String idField : idFields) {
                    if (firstElement.has(idField)) {
                        return firstElement.get(idField).asText();
                    }
                }
            }

        } catch (Exception e) {
            logger.error("Error extracting ID from response: {}", e.getMessage());
        }

        return null;
    }

    /**
     * Extract 0th index ID from getAll response
     */
    private String extractZeroIndexId(String responseBody) {
        try {
            JsonNode responseNode = mapper.readTree(responseBody);

            if (responseNode.isArray() && responseNode.size() > 0) {
                return extractIdFromResponse(responseNode.get(0).toString());
            } else if (responseNode.has("data") && responseNode.get("data").isArray()) {
                JsonNode dataArray = responseNode.get("data");
                if (dataArray.size() > 0) {
                    return extractIdFromResponse(dataArray.get(0).toString());
                }
            }

        } catch (Exception e) {
            logger.error("Error extracting 0th index ID: {}", e.getMessage());
        }

        return null;
    }

    /**
     * Update endpoint with ID
     */
    private String updateEndpointWithId(String endpoint, String id) {
        if (endpoint.contains("{id}")) {
            return endpoint.replace("{id}", id);
        } else if (endpoint.endsWith("/")) {
            return endpoint + id;
        } else {
            return endpoint + "/" + id;
        }
    }

    /**
     * Get database data with foreign keys resolved
     */
    private String getDatabaseDataWithForeignKeys(String tableName, String id) {
        try {
            // Get main record
            String mainRecord = databaseUtils.getRecordByIdAsJson(tableName, "id", id);

            if ("{}".equals(mainRecord)) {
                return mainRecord;
            }

            JsonNode mainNode = mapper.readTree(mainRecord);
            ObjectNode resultNode = mainNode.deepCopy();

            // Resolve foreign keys
            Map<String, String> foreignKeyMappings = ApiConstants.getAllForeignKeyMappings();

            Iterator<Map.Entry<String, JsonNode>> fields = mainNode.fields();
            while (fields.hasNext()) {
                Map.Entry<String, JsonNode> field = fields.next();
                String fieldName = field.getKey();
                JsonNode fieldValue = field.getValue();

                if (foreignKeyMappings.containsKey(fieldName) && !fieldValue.isNull()) {
                    String foreignKeyId = fieldValue.asText();
                    String foreignEntityName = foreignKeyMappings.get(fieldName);
                    String foreignTableName = ApiConstants.getTableName(foreignEntityName);

                    if (foreignTableName != null) {
                        String foreignRecord = databaseUtils.getRecordByIdAsJson(foreignTableName, "id", foreignKeyId);
                        if (!"{}".equals(foreignRecord)) {
                            JsonNode foreignNode = mapper.readTree(foreignRecord);
                            resultNode.set(fieldName, foreignNode);
                        }
                    }
                }
            }

            return mapper.writeValueAsString(resultNode);

        } catch (Exception e) {
            logger.error("Error getting database data with foreign keys: {}", e.getMessage());
            return "{}";
        }
    }

    /**
     * Compare API response with database data
     */
    private boolean compareApiWithDatabase(String apiResponse, String dbData) {
        try {
            JsonNode apiNode = mapper.readTree(apiResponse);
            JsonNode dbNode = mapper.readTree(dbData);

            return compareJsonNodes(apiNode, dbNode);

        } catch (Exception e) {
            logger.error("Error comparing API with database: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Compare JSON nodes recursively
     */
    private boolean compareJsonNodes(JsonNode node1, JsonNode node2) {
        if (node1.getNodeType() != node2.getNodeType()) {
            return false;
        }

        if (node1.isObject()) {
            Iterator<String> fieldNames = node1.fieldNames();
            while (fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                if (!node2.has(fieldName)) {
                    continue; // Skip fields not in database
                }

                if (!compareJsonNodes(node1.get(fieldName), node2.get(fieldName))) {
                    return false;
                }
            }
            return true;
        } else if (node1.isArray()) {
            if (node1.size() != node2.size()) {
                return false;
            }

            for (int i = 0; i < node1.size(); i++) {
                if (!compareJsonNodes(node1.get(i), node2.get(i))) {
                    return false;
                }
            }
            return true;
        } else {
            return node1.equals(node2);
        }
    }

    /**
     * Generate defect for failed test with table name from Excel column 2
     */
    private String generateDefect(int rowNumber, String operation, ApiResponse response, String errorMessage) {
        return generateDefectWithTableName(null, null, rowNumber, operation, response, errorMessage);
    }

    /**
     * Generate defect with table name from Excel column 2
     */
    private String generateDefectWithTableName(String filePath, String sheetName, int rowNumber,
                                             String operation, ApiResponse response, String errorMessage) {
        try {
            // Read table name from Excel column 2
            String tableName = "UnknownTable";
            if (filePath != null && sheetName != null) {
                try {
                    tableName = excelUtils.getCellData(filePath, sheetName, rowNumber, 2); // Column 2
                    if (tableName == null || tableName.trim().isEmpty()) {
                        tableName = "UnknownTable";
                    } else {
                        // Clean table name - remove spaces and special characters
                        tableName = tableName.trim().replaceAll("[^a-zA-Z0-9]", "");
                    }
                    logger.info("📋 Table name from Excel column 2: '{}'", tableName);
                } catch (Exception e) {
                    logger.warn("Could not read table name from Excel column 2: {}", e.getMessage());
                    tableName = "UnknownTable";
                }
            }

            String testCaseName = String.format("API_%s_Row_%d", operation, rowNumber);
            String endpoint = response != null ? "Unknown" : "Unknown";
            String actualResult = response != null ? response.getBody() : errorMessage;

            return apiHandler.generateDefectForFailureWithTableName(testCaseName, endpoint, "Success",
                                                                  actualResult, errorMessage, tableName);
        } catch (Exception e) {
            logger.error("Error generating defect: {}", e.getMessage());
            return "DEFECT_GENERATION_FAILED";
        }
    }

    /**
     * Update Excel with success - PRESERVES expected result from Excel
     * GREEN COLOR for Passed status
     */
    private void updateExcelWithSuccess(String filePath, String sheetName, int rowNumber,
                                      String expectedResult, String actualResult) {
        try {
            // Set status with GREEN color
            excelUtils.setCellDataWithStatusColor(filePath, sheetName, rowNumber, statusCol, "Passed");
            // DO NOT overwrite expected result - keep user's input
            excelUtils.setCellData(filePath, sheetName, rowNumber, actualResultCol, actualResult);
            logger.info("✅ Test Passed - Status: Passed (GREEN)");
        } catch (Exception e) {
            logger.error("Error updating Excel with success: {}", e.getMessage());
        }
    }

    /**
     * Update Excel with success for status code validation - PRESERVES expected result
     * GREEN COLOR for Passed status
     */
    private void updateExcelWithStatusCodeSuccess(String filePath, String sheetName, int rowNumber,
                                                String actualStatusCode) {
        try {
            // Read existing expected result from Excel - DON'T CHANGE IT
            String existingExpected = excelUtils.getCellData(filePath, sheetName, rowNumber, expectedResultCol);

            // Set status with GREEN color
            excelUtils.setCellDataWithStatusColor(filePath, sheetName, rowNumber, statusCol, "Passed");
            // Keep expected result unchanged - only update actual with status code
            excelUtils.setCellData(filePath, sheetName, rowNumber, actualResultCol, actualStatusCode);

            logger.info("✅ Status Code Validation - Expected: {}, Actual: {}, Status: Passed (GREEN)",
                       existingExpected, actualStatusCode);
        } catch (Exception e) {
            logger.error("Error updating Excel with status code success: {}", e.getMessage());
        }
    }

    /**
     * Update Excel with failure - PRESERVES expected result from Excel
     * RED COLOR for Failed status
     */
    private void updateExcelWithFailure(String filePath, String sheetName, int rowNumber,
                                      String actualResult, String expectedResult, String defectId) {
        try {
            // Set status with RED color
            excelUtils.setCellDataWithStatusColor(filePath, sheetName, rowNumber, statusCol, "Failed");
            // DO NOT overwrite expected result - keep user's input
            excelUtils.setCellData(filePath, sheetName, rowNumber, actualResultCol, actualResult);
            if (defectIdCol > 0) {
                excelUtils.setCellData(filePath, sheetName, rowNumber, defectIdCol, defectId);
            }
            logger.info("❌ Test Failed - Status: Failed (RED)");
        } catch (Exception e) {
            logger.error("Error updating Excel with failure: {}", e.getMessage());
        }
    }

    /**
     * Update Excel with failure for status code validation - PRESERVES expected result
     * RED COLOR for Failed status
     */
    private void updateExcelWithStatusCodeFailure(String filePath, String sheetName, int rowNumber,
                                                String actualStatusCode, String defectId) {
        try {
            // Read existing expected result from Excel - DON'T CHANGE IT
            String existingExpected = excelUtils.getCellData(filePath, sheetName, rowNumber, expectedResultCol);

            // Set status with RED color
            excelUtils.setCellDataWithStatusColor(filePath, sheetName, rowNumber, statusCol, "Failed");
            // Keep expected result unchanged - only update actual with status code
            excelUtils.setCellData(filePath, sheetName, rowNumber, actualResultCol, actualStatusCode);
            if (defectIdCol > 0) {
                excelUtils.setCellData(filePath, sheetName, rowNumber, defectIdCol, defectId);
            }

            logger.info("❌ Status Code Validation - Expected: {}, Actual: {}, Status: Failed (RED)",
                       existingExpected, actualStatusCode);
        } catch (Exception e) {
            logger.error("Error updating Excel with status code failure: {}", e.getMessage());
        }
    }

    /**
     * Update Excel with error
     */
    private void updateExcelWithError(String filePath, String sheetName, int rowNumber, String errorMessage) {
        updateExcelWithError(filePath, sheetName, rowNumber, errorMessage, null);
    }

    private void updateExcelWithError(String filePath, String sheetName, int rowNumber,
                                    String errorMessage, String defectId) {
        try {
            // Set status with ORANGE color for Error
            excelUtils.setCellDataWithStatusColor(filePath, sheetName, rowNumber, statusCol, "Error");
            excelUtils.setCellData(filePath, sheetName, rowNumber, actualResultCol, errorMessage);
            if (defectId != null && defectIdCol > 0) {
                excelUtils.setCellData(filePath, sheetName, rowNumber, defectIdCol, defectId);
            }
            logger.info("⚠️ Test Error - Status: Error (ORANGE)");
        } catch (Exception e) {
            logger.error("Error updating Excel with error: {}", e.getMessage());
        }
    }

    /**
     * Execute PUT operation with complete workflow
     */
    private void executePutOperation(String filePath, String sheetName, int rowNumber,
                                   String requestBody, String baseUrl, String authToken) {
        try {
            logger.info("=== Executing PUT Operation ===");

            // Step 1: Get existing data first (similar to PUT workflow in UniversalApiHandler)
            JsonNode requestNode = mapper.readTree(requestBody);
            String endpoint = requestNode.get("endpoint").asText();
            String entityName = extractEntityNameFromEndpoint(endpoint);

            // Step 2: Hit getAll API to get existing data
            String getAllEndpoint = ApiConstants.getEntityEndpoints(entityName).getAllEndpoint();
            String getAllRequest = String.format(ApiConstants.GET_ALL_TEMPLATE, getAllEndpoint);

            ApiResponse getAllResponse = apiHandler.processApiRequest(getAllRequest, baseUrl, authToken);
            String existingData = extractZeroIndexData(getAllResponse.getBody());

            if (existingData == null) {
                updateExcelWithError(filePath, sheetName, rowNumber, "No existing data found for PUT operation");
                return;
            }

            // Step 3: Merge existing data with Excel payload
            JsonNode existingNode = mapper.readTree(existingData);
            JsonNode excelPayload = requestNode.get("payload");
            ObjectNode mergedPayload = mergePayloads(existingNode, excelPayload);

            // Step 4: Resolve foreign keys in merged payload
            String mergedRequestBody = createRequestWithPayload(requestNode, mergedPayload);
            String resolvedRequestBody = resolveForeignKeys(mergedRequestBody, baseUrl, authToken);

            // Step 5: Generate dynamic data
            String finalRequestBody = dataGenerator.processExcelJsonStructure(resolvedRequestBody);

            // Step 6: Execute PUT request
            ApiResponse response = apiHandler.processApiRequest(finalRequestBody, baseUrl, authToken);

            // Step 7: Validate with database
            String updatedId = extractIdFromResponse(response.getBody());
            boolean isValid = validateWithDatabase(response.getBody(), updatedId, finalRequestBody);

            // Step 8: Update Excel with results
            if (response.isSuccess() && isValid) {
                updateExcelWithSuccess(filePath, sheetName, rowNumber, response.getBody(), "PUT operation successful");
            } else {
                String defectId = generateDefect(rowNumber, "PUT", response, "PUT operation failed");
                updateExcelWithFailure(filePath, sheetName, rowNumber, response.getBody(),
                                     "PUT operation failed", defectId);
            }

        } catch (Exception e) {
            logger.error("Error in PUT operation: {}", e.getMessage());
            String defectId = generateDefect(rowNumber, "PUT", null, "PUT operation error: " + e.getMessage());
            updateExcelWithError(filePath, sheetName, rowNumber, "PUT error: " + e.getMessage(), defectId);
        }
    }

    /**
     * Execute PATCH operation
     */
    private void executePatchOperation(String filePath, String sheetName, int rowNumber,
                                     String requestBody, String baseUrl, String authToken) {
        try {
            logger.info("=== Executing PATCH Operation ===");

            // Similar to PUT but only updates specified fields
            JsonNode requestNode = mapper.readTree(requestBody);
            String endpoint = requestNode.get("endpoint").asText();
            String entityName = extractEntityNameFromEndpoint(endpoint);

            // Get ID for PATCH operation
            String getAllEndpoint = ApiConstants.getEntityEndpoints(entityName).getAllEndpoint();
            String getAllRequest = String.format(ApiConstants.GET_ALL_TEMPLATE, getAllEndpoint);

            ApiResponse getAllResponse = apiHandler.processApiRequest(getAllRequest, baseUrl, authToken);
            String extractedId = extractZeroIndexId(getAllResponse.getBody());

            if (extractedId == null) {
                updateExcelWithError(filePath, sheetName, rowNumber, "No data found for PATCH test");
                return;
            }

            // Update endpoint with ID
            String updatedEndpoint = updateEndpointWithId(endpoint, extractedId);
            JsonNode updatedRequest = requestNode.deepCopy();
            ((ObjectNode) updatedRequest).put("endpoint", updatedEndpoint);

            // Resolve foreign keys and generate data
            String resolvedRequestBody = resolveForeignKeys(mapper.writeValueAsString(updatedRequest), baseUrl, authToken);
            String finalRequestBody = dataGenerator.processExcelJsonStructure(resolvedRequestBody);

            // Execute PATCH request
            ApiResponse response = apiHandler.processApiRequest(finalRequestBody, baseUrl, authToken);

            // Validate with database
            boolean isValid = validateWithDatabase(response.getBody(), extractedId, finalRequestBody);

            // Update Excel with results
            if (response.isSuccess() && isValid) {
                updateExcelWithSuccess(filePath, sheetName, rowNumber, response.getBody(), "PATCH operation successful");
            } else {
                String defectId = generateDefect(rowNumber, "PATCH", response, "PATCH operation failed");
                updateExcelWithFailure(filePath, sheetName, rowNumber, response.getBody(),
                                     "PATCH operation failed", defectId);
            }

        } catch (Exception e) {
            logger.error("Error in PATCH operation: {}", e.getMessage());
            String defectId = generateDefect(rowNumber, "PATCH", null, "PATCH operation error: " + e.getMessage());
            updateExcelWithError(filePath, sheetName, rowNumber, "PATCH error: " + e.getMessage(), defectId);
        }
    }

    /**
     * Execute GET All operation
     */
    private void executeGetAllOperation(String filePath, String sheetName, int rowNumber,
                                      String requestBody, String baseUrl, String authToken) {
        try {
            logger.info("=== Executing GET All Operation ===");

            // Execute GET All request
            ApiResponse response = apiHandler.processApiRequest(requestBody, baseUrl, authToken);

            // Get database data for comparison
            JsonNode requestNode = mapper.readTree(requestBody);
            String endpoint = requestNode.get("endpoint").asText();
            String entityName = extractEntityNameFromEndpoint(endpoint);
            String tableName = ApiConstants.getTableName(entityName);

            String dbData = getAllDatabaseRecords(tableName);

            // Compare API response with database data
            boolean isMatch = compareApiWithDatabase(response.getBody(), dbData);

            // Update Excel with results
            if (response.isSuccess() && isMatch) {
                updateExcelWithSuccess(filePath, sheetName, rowNumber, dbData, response.getBody());
            } else {
                String defectId = generateDefect(rowNumber, "GET_ALL", response, "GetAll validation failed");
                updateExcelWithFailure(filePath, sheetName, rowNumber, response.getBody(),
                                     dbData, defectId);
            }

        } catch (Exception e) {
            logger.error("Error in GET All operation: {}", e.getMessage());
            String defectId = generateDefect(rowNumber, "GET_ALL", null, "GetAll error: " + e.getMessage());
            updateExcelWithError(filePath, sheetName, rowNumber, "GetAll error: " + e.getMessage(), defectId);
        }
    }

    // Helper methods for the new operations

    private String extractZeroIndexData(String responseBody) {
        try {
            JsonNode responseNode = mapper.readTree(responseBody);

            if (responseNode.isArray() && responseNode.size() > 0) {
                return responseNode.get(0).toString();
            } else if (responseNode.has("data") && responseNode.get("data").isArray()) {
                JsonNode dataArray = responseNode.get("data");
                if (dataArray.size() > 0) {
                    return dataArray.get(0).toString();
                }
            }

        } catch (Exception e) {
            logger.error("Error extracting 0th index data: {}", e.getMessage());
        }

        return null;
    }

    private ObjectNode mergePayloads(JsonNode existingData, JsonNode excelPayload) {
        ObjectNode merged = existingData.deepCopy();

        if (excelPayload != null && excelPayload.isObject()) {
            Iterator<Map.Entry<String, JsonNode>> fields = excelPayload.fields();
            while (fields.hasNext()) {
                Map.Entry<String, JsonNode> field = fields.next();
                merged.set(field.getKey(), field.getValue());
            }
        }

        return merged;
    }

    private String createRequestWithPayload(JsonNode originalRequest, JsonNode payload) {
        try {
            ObjectNode newRequest = originalRequest.deepCopy();
            newRequest.set("payload", payload);
            return mapper.writeValueAsString(newRequest);
        } catch (Exception e) {
            logger.error("Error creating request with payload: {}", e.getMessage());
            return "{}";
        }
    }

    private String getAllDatabaseRecords(String tableName) {
        try {
            // Use existing method or create a simple implementation
            return databaseUtils.getRecordByIdAsJson(tableName, "1", "1"); // Placeholder
        } catch (Exception e) {
            logger.error("Error getting all database records: {}", e.getMessage());
            return "[]";
        }
    }

    private boolean validateWithDatabase(String apiResponse, String id, String requestBody) {
        try {
            // Extract entity information
            JsonNode requestNode = mapper.readTree(requestBody);
            String endpoint = requestNode.get("endpoint").asText();
            String entityName = extractEntityNameFromEndpoint(endpoint);
            String tableName = ApiConstants.getTableName(entityName);

            if (tableName == null || id == null) {
                return true; // Skip validation if no table mapping or ID
            }

            // Get database data with foreign keys resolved
            String dbData = getDatabaseDataWithForeignKeys(tableName, id);

            // Compare API response with database data
            return compareApiWithDatabase(apiResponse, dbData);

        } catch (Exception e) {
            logger.error("Error validating with database: {}", e.getMessage());
            return false;
        }
    }
}
