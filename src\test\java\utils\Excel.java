package utils;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * Excel utility class for reading and writing Excel files
 * Provides basic Excel operations for the API testing framework
 */
public class Excel {
    private static final Logger logger = LoggerFactory.getLogger(Excel.class);
    
    /**
     * Read cell data from Excel file
     */
    public static String getCellData(String filePath, String sheetName, int rowNum, int colNum) {
        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = new XSSFWorkbook(fis)) {
            
            Sheet sheet = workbook.getSheet(sheetName);
            if (sheet == null) {
                logger.error("Sheet '{}' not found in file '{}'", sheetName, filePath);
                return "";
            }
            
            Row row = sheet.getRow(rowNum - 1); // Convert to 0-based index
            if (row == null) {
                return "";
            }
            
            Cell cell = row.getCell(colNum - 1); // Convert to 0-based index
            if (cell == null) {
                return "";
            }
            
            return getCellValueAsString(cell);
            
        } catch (IOException e) {
            logger.error("Error reading Excel file: " + e.getMessage());
            return "";
        }
    }
    
    /**
     * Write cell data to Excel file
     */
    public static boolean setCellData(String filePath, String sheetName, int rowNum, int colNum, String data) {
        return setCellData(filePath, sheetName, rowNum, colNum, data, false);
    }
    
    /**
     * Write cell data to Excel file with color formatting
     */
    public static boolean setCellData(String filePath, String sheetName, int rowNum, int colNum, String data, boolean isSuccess) {
        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = new XSSFWorkbook(fis)) {
            
            Sheet sheet = workbook.getSheet(sheetName);
            if (sheet == null) {
                sheet = workbook.createSheet(sheetName);
            }
            
            Row row = sheet.getRow(rowNum - 1); // Convert to 0-based index
            if (row == null) {
                row = sheet.createRow(rowNum - 1);
            }
            
            Cell cell = row.getCell(colNum - 1); // Convert to 0-based index
            if (cell == null) {
                cell = row.createCell(colNum - 1);
            }
            
            cell.setCellValue(data);
            
            // Apply color formatting if needed
            if (data.equalsIgnoreCase("PASS") || data.equalsIgnoreCase("Passed")) {
                applyCellStyle(workbook, cell, IndexedColors.GREEN);
            } else if (data.equalsIgnoreCase("FAIL") || data.equalsIgnoreCase("Failed")) {
                applyCellStyle(workbook, cell, IndexedColors.RED);
            }
            
            // Write back to file
            try (FileOutputStream fos = new FileOutputStream(filePath)) {
                workbook.write(fos);
            }
            
            return true;
            
        } catch (IOException e) {
            logger.error("Error writing to Excel file: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Get cell value as string regardless of cell type
     */
    private static String getCellValueAsString(Cell cell) {
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            case BLANK:
                return "";
            default:
                return "";
        }
    }
    
    /**
     * Apply cell style with background color
     */
    private static void applyCellStyle(Workbook workbook, Cell cell, IndexedColors color) {
        CellStyle style = workbook.createCellStyle();
        style.setFillForegroundColor(color.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cell.setCellStyle(style);
    }
    
    /**
     * Get row count in a sheet
     */
    public static int getRowCount(String filePath, String sheetName) {
        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = new XSSFWorkbook(fis)) {
            
            Sheet sheet = workbook.getSheet(sheetName);
            if (sheet == null) {
                return 0;
            }
            
            return sheet.getLastRowNum() + 1;
            
        } catch (IOException e) {
            logger.error("Error getting row count: " + e.getMessage());
            return 0;
        }
    }
    
    /**
     * Get column count in a sheet
     */
    public static int getColumnCount(String filePath, String sheetName, int rowNum) {
        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = new XSSFWorkbook(fis)) {
            
            Sheet sheet = workbook.getSheet(sheetName);
            if (sheet == null) {
                return 0;
            }
            
            Row row = sheet.getRow(rowNum - 1);
            if (row == null) {
                return 0;
            }
            
            return row.getLastCellNum();
            
        } catch (IOException e) {
            logger.error("Error getting column count: " + e.getMessage());
            return 0;
        }
    }
    
    /**
     * Check if sheet exists
     */
    public static boolean sheetExists(String filePath, String sheetName) {
        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = new XSSFWorkbook(fis)) {
            
            return workbook.getSheet(sheetName) != null;
            
        } catch (IOException e) {
            logger.error("Error checking if sheet exists: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Create new sheet
     */
    public static boolean createSheet(String filePath, String sheetName) {
        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = new XSSFWorkbook(fis)) {
            
            if (workbook.getSheet(sheetName) != null) {
                logger.warn("Sheet '{}' already exists", sheetName);
                return false;
            }
            
            workbook.createSheet(sheetName);
            
            try (FileOutputStream fos = new FileOutputStream(filePath)) {
                workbook.write(fos);
            }
            
            return true;
            
        } catch (IOException e) {
            logger.error("Error creating sheet: " + e.getMessage());
            return false;
        }
    }
}
