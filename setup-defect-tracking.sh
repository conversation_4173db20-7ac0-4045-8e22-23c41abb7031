#!/bin/bash

echo "========================================"
echo "  Defect Tracking Configuration Setup"
echo "========================================"
echo

echo "This script will help you configure defect tracking credentials."
echo

echo "Current configuration file: src/test/resources/defect-config.properties"
echo

echo "Please provide your Plane bug tracking credentials:"
echo

read -p "Enter your Plane URL (e.g., https://mycompany.plane.so): " PLANE_URL
read -p "Enter your Workspace ID (e.g., ws_abc123def456): " WORKSPACE_ID
read -p "Enter your Project ID (e.g., proj_xyz789abc123): " PROJECT_ID
read -p "Enter your API Key (e.g., plane_api_key_abc123xyz789): " API_KEY

echo
echo "========================================"
echo "  Updating Configuration File"
echo "========================================"

# Create backup
cp "src/test/resources/defect-config.properties" "src/test/resources/defect-config.properties.backup"

# Update the configuration file
sed -i.tmp "s|plane.url=https://your-plane-instance.com|plane.url=$PLANE_URL|g" "src/test/resources/defect-config.properties"
sed -i.tmp "s|plane.workspace.id=your-workspace-id|plane.workspace.id=$WORKSPACE_ID|g" "src/test/resources/defect-config.properties"
sed -i.tmp "s|plane.project.id=your-project-id|plane.project.id=$PROJECT_ID|g" "src/test/resources/defect-config.properties"
sed -i.tmp "s|plane.api.key=your-plane-api-key|plane.api.key=$API_KEY|g" "src/test/resources/defect-config.properties"

# Remove temporary files
rm -f "src/test/resources/defect-config.properties.tmp"

echo
echo "✅ Configuration updated successfully!"
echo

echo "========================================"
echo "  Testing Configuration"
echo "========================================"
echo

echo "Running test to verify defect tracking setup..."
mvn test -Dtest=ComprehensiveApiTestSuite#testPostOperationDemo -q

echo
echo "========================================"
echo "  Setup Complete"
echo "========================================"
echo

echo "Your defect tracking is now configured!"
echo
echo "Configuration file: src/test/resources/defect-config.properties"
echo "Backup file: src/test/resources/defect-config.properties.backup"
echo
echo "To test defect creation, run:"
echo "mvn test -Dtest=ComprehensiveApiTestSuite"
echo
echo "Check your Plane project for automatically created defects."
echo
