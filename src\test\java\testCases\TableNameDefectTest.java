package testCases;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.annotations.Test;
import utils.DefectDetails;
import utils.DefectTracker;

/**
 * Test to demonstrate defect ID generation using table name from Excel column 2
 */
public class TableNameDefectTest {
    private static final Logger logger = LoggerFactory.getLogger(TableNameDefectTest.class);
    
    @Test
    public void testDefectIdWithTableNameFromColumn2() {
        logger.info("=== Testing Defect ID with Table Name from Excel Column 2 ===");
        
        DefectTracker defectTracker = new DefectTracker();
        
        // Test 1: BundleProduct from Excel column 2
        DefectDetails bundleProductDefect = new DefectDetails();
        bundleProductDefect.setTitle("API Test Failure: API_POST_Row_14");
        bundleProductDefect.setDescription("""
            **API Test Case Failed**
            
            **Table/Entity:** BundleProduct
            **Endpoint:** /order/api/BundleProduct/save
            **Expected Result:** 201
            **Actual Result:** 400
            **Error Details:** Status code mismatch or validation failed. Expected: 201, Actual: 400
            
            **Steps to Reproduce:**
            1. Execute API test for endpoint: /order/api/BundleProduct/save
            2. Verify response matches expected result
            3. Observe the failure
            
            **Environment:** Test
            **Table/Entity:** BundleProduct
            **Generated by:** Automated API Testing Framework
            """);
        bundleProductDefect.setSeverity("Medium");
        bundleProductDefect.setPriority("High");
        bundleProductDefect.setComponent("BundleProduct"); // Component matches table name
        bundleProductDefect.setEnvironment("Test");
        bundleProductDefect.setTableName("BundleProduct"); // Table name from Excel column 2
        
        String defectId1 = defectTracker.createDefect(bundleProductDefect);
        logger.info("✅ BundleProduct Defect ID (from column 2): {}", defectId1);
        
        // Test 2: CountryMaster from Excel column 2
        DefectDetails countryMasterDefect = new DefectDetails();
        countryMasterDefect.setTitle("API Test Failure: API_GET_Row_5");
        countryMasterDefect.setDescription("""
            **API Test Case Failed**
            
            **Table/Entity:** CountryMaster
            **Endpoint:** /core/api/CountryMaster/getAll
            **Expected Result:** 200
            **Actual Result:** 500
            **Error Details:** Internal server error
            
            **Steps to Reproduce:**
            1. Execute API test for endpoint: /core/api/CountryMaster/getAll
            2. Verify response matches expected result
            3. Observe the failure
            
            **Environment:** Test
            **Table/Entity:** CountryMaster
            **Generated by:** Automated API Testing Framework
            """);
        countryMasterDefect.setSeverity("High");
        countryMasterDefect.setPriority("Critical");
        countryMasterDefect.setComponent("CountryMaster");
        countryMasterDefect.setEnvironment("Test");
        countryMasterDefect.setTableName("CountryMaster"); // Table name from Excel column 2
        
        String defectId2 = defectTracker.createDefect(countryMasterDefect);
        logger.info("✅ CountryMaster Defect ID (from column 2): {}", defectId2);
        
        // Test 3: StateMaster from Excel column 2
        DefectDetails stateMasterDefect = new DefectDetails();
        stateMasterDefect.setTitle("API Test Failure: API_PUT_Row_8");
        stateMasterDefect.setDescription("""
            **API Test Case Failed**
            
            **Table/Entity:** StateMaster
            **Endpoint:** /core/api/StateMaster/update/123
            **Expected Result:** 200
            **Actual Result:** 404
            **Error Details:** Record not found
            
            **Steps to Reproduce:**
            1. Execute API test for endpoint: /core/api/StateMaster/update/123
            2. Verify response matches expected result
            3. Observe the failure
            
            **Environment:** Test
            **Table/Entity:** StateMaster
            **Generated by:** Automated API Testing Framework
            """);
        stateMasterDefect.setSeverity("Medium");
        stateMasterDefect.setPriority("Medium");
        stateMasterDefect.setComponent("StateMaster");
        stateMasterDefect.setEnvironment("Test");
        stateMasterDefect.setTableName("StateMaster"); // Table name from Excel column 2
        
        String defectId3 = defectTracker.createDefect(stateMasterDefect);
        logger.info("✅ StateMaster Defect ID (from column 2): {}", defectId3);
        
        // Test 4: CompanyKeyPersonnelDetails from Excel column 2
        DefectDetails companyPersonnelDefect = new DefectDetails();
        companyPersonnelDefect.setTitle("API Test Failure: API_POST_Row_20");
        companyPersonnelDefect.setDescription("""
            **API Test Case Failed**
            
            **Table/Entity:** CompanyKeyPersonnelDetails
            **Endpoint:** /contact/api/CompanyKeyPersonnelDetails/save
            **Expected Result:** 201
            **Actual Result:** 422
            **Error Details:** Validation failed
            
            **Steps to Reproduce:**
            1. Execute API test for endpoint: /contact/api/CompanyKeyPersonnelDetails/save
            2. Verify response matches expected result
            3. Observe the failure
            
            **Environment:** Test
            **Table/Entity:** CompanyKeyPersonnelDetails
            **Generated by:** Automated API Testing Framework
            """);
        companyPersonnelDefect.setSeverity("Medium");
        companyPersonnelDefect.setPriority("High");
        companyPersonnelDefect.setComponent("CompanyKeyPersonnelDetails");
        companyPersonnelDefect.setEnvironment("Test");
        companyPersonnelDefect.setTableName("CompanyKeyPersonnelDetails"); // Table name from Excel column 2
        
        String defectId4 = defectTracker.createDefect(companyPersonnelDefect);
        logger.info("✅ CompanyKeyPersonnelDetails Defect ID (from column 2): {}", defectId4);
        
        logger.info("=== Table Name Defect ID Test Completed ===");
        logger.info("Expected Format: D_TableName_001 (using table name from Excel column 2)");
        logger.info("Generated IDs:");
        logger.info("  1. BundleProduct: {}", defectId1);
        logger.info("  2. CountryMaster: {}", defectId2);
        logger.info("  3. StateMaster: {}", defectId3);
        logger.info("  4. CompanyKeyPersonnelDetails: {}", defectId4);
        
        // Verify format
        logger.info("=== Verifying Defect ID Format ===");
        verifyDefectIdFormat(defectId1, "BundleProduct");
        verifyDefectIdFormat(defectId2, "CountryMaster");
        verifyDefectIdFormat(defectId3, "StateMaster");
        verifyDefectIdFormat(defectId4, "CompanyKeyPersonnelDetails");
    }
    
    private void verifyDefectIdFormat(String defectId, String expectedTableName) {
        if (defectId.startsWith("D_" + expectedTableName + "_")) {
            logger.info("✅ PASS: {} has correct format D_{}_XXX", defectId, expectedTableName);
        } else {
            logger.error("❌ FAIL: {} does not match expected format D_{}_XXX", defectId, expectedTableName);
        }
    }
}
