package testCases;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.annotations.Test;
import utils.ExcelUtils;

/**
 * Test to verify Excel color formatting for status column
 */
public class ExcelColorTest {
    private static final Logger logger = LoggerFactory.getLogger(ExcelColorTest.class);
    
    @Test
    public void testExcelColorFormatting() {
        logger.info("=== Testing Excel Color Formatting ===");
        
        ExcelUtils excelUtils = new ExcelUtils();
        String filePath = "data/R Filings.xlsx";
        String sheetName = "Order Service";
        
        try {
            // Test 1: Green for Passed
            logger.info("Setting cell with GREEN color for 'Passed'");
            excelUtils.setCellDataWithStatusColor(filePath, sheetName, 15, 10, "Passed");
            logger.info("✅ GREEN 'Passed' status set in row 15, column 10");
            
            // Test 2: Red for Failed
            logger.info("Setting cell with RED color for 'Failed'");
            excelUtils.setCellDataWithStatusColor(filePath, sheetName, 16, 10, "Failed");
            logger.info("❌ RED 'Failed' status set in row 16, column 10");
            
            // Test 3: Orange for Error
            logger.info("Setting cell with ORANGE color for 'Error'");
            excelUtils.setCellDataWithStatusColor(filePath, sheetName, 17, 10, "Error");
            logger.info("⚠️ ORANGE 'Error' status set in row 17, column 10");
            
            // Test 4: Yellow for Skipped
            logger.info("Setting cell with YELLOW color for 'Skipped'");
            excelUtils.setCellDataWithStatusColor(filePath, sheetName, 18, 10, "Skipped");
            logger.info("⏭️ YELLOW 'Skipped' status set in row 18, column 10");
            
            logger.info("=== Excel Color Formatting Test Completed ===");
            logger.info("Please check the Excel file to verify colors:");
            logger.info("  Row 15, Column 10: 'Passed' should be GREEN with white text");
            logger.info("  Row 16, Column 10: 'Failed' should be RED with white text");
            logger.info("  Row 17, Column 10: 'Error' should be ORANGE with white text");
            logger.info("  Row 18, Column 10: 'Skipped' should be YELLOW with black text");
            
        } catch (Exception e) {
            logger.error("Error during Excel color formatting test: {}", e.getMessage());
            e.printStackTrace();
        }
    }
    
    @Test
    public void testBooleanColorFormatting() {
        logger.info("=== Testing Boolean Color Formatting ===");
        
        ExcelUtils excelUtils = new ExcelUtils();
        String filePath = "data/R Filings.xlsx";
        String sheetName = "Order Service";
        
        try {
            // Test boolean method
            logger.info("Setting cell with boolean GREEN for true (Passed)");
            excelUtils.setCellData(filePath, sheetName, 19, 10, "Passed", true);
            logger.info("✅ Boolean GREEN 'Passed' status set in row 19, column 10");
            
            logger.info("Setting cell with boolean RED for false (Failed)");
            excelUtils.setCellData(filePath, sheetName, 20, 10, "Failed", false);
            logger.info("❌ Boolean RED 'Failed' status set in row 20, column 10");
            
            logger.info("=== Boolean Color Formatting Test Completed ===");
            logger.info("Please check the Excel file to verify colors:");
            logger.info("  Row 19, Column 10: 'Passed' should be GREEN");
            logger.info("  Row 20, Column 10: 'Failed' should be RED");
            
        } catch (Exception e) {
            logger.error("Error during boolean color formatting test: {}", e.getMessage());
            e.printStackTrace();
        }
    }
}
