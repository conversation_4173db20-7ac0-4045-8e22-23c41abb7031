@echo off
echo ========================================
echo CRUD Operations API Testing Framework
echo ========================================
echo.

REM Set Java options
set JAVA_OPTS=-Xmx2g -Dfile.encoding=UTF-8

REM Set test configuration
set TEST_CONFIG=-Dconfig.file=crud-config.properties

REM Set logging configuration
set LOG_CONFIG=-Dlogback.configurationFile=logback.xml

echo Starting CRUD Operations Tests...
echo.

REM Run Photos API Tests
echo [1/3] Running Photos API Tests...
mvn test -Dtest=PhotosApiTest %JAVA_OPTS% %TEST_CONFIG% %LOG_CONFIG%
if %ERRORLEVEL% neq 0 (
    echo ERROR: Photos API Tests failed!
    pause
    exit /b 1
)

echo.
echo [2/3] Running Products API Tests...
mvn test -Dtest=ProductsApiTest %JAVA_OPTS% %TEST_CONFIG% %LOG_CONFIG%
if %ERRORLEVEL% neq 0 (
    echo ERROR: Products API Tests failed!
    pause
    exit /b 1
)

echo.
echo [3/3] Running Complete CRUD Test Suite...
mvn test -DsuiteXmlFile=testng-crud-operations.xml %JAVA_OPTS% %TEST_CONFIG% %LOG_CONFIG%
if %ERRORLEVEL% neq 0 (
    echo ERROR: CRUD Test Suite failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo All CRUD Operations Tests Completed!
echo ========================================
echo.
echo Check the following for results:
echo - Excel file: data/SnackHack.xlsx
echo - Test reports: target/surefire-reports/
echo - Logs: logs/crud-operations.log
echo.

pause
