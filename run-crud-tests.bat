@echo off
echo ========================================
echo CRUD Operations API Testing Framework
echo ========================================
echo.

REM Set Java options
set JAVA_OPTS=-Xmx2g -Dfile.encoding=UTF-8

REM Set test configuration
set TEST_CONFIG=-Dconfig.file=crud-config.properties

REM Set logging configuration
set LOG_CONFIG=-Dlogback.configurationFile=logback.xml

REM Check if argument is provided
if "%1"=="" (
    echo No test type specified. Running complete suite...
    set TEST_TYPE=suite
) else (
    set TEST_TYPE=%1
)

echo Starting CRUD Operations Tests [Type: %TEST_TYPE%]...
echo.

REM Run tests based on type
if "%TEST_TYPE%"=="photos" (
    echo Running Photos API Tests Only...
    mvn test -Dtest=PhotosApiTest %JAVA_OPTS% %TEST_CONFIG% %LOG_CONFIG%
    goto :check_result
)

if "%TEST_TYPE%"=="products" (
    echo Running Products API Tests Only...
    mvn test -Dtest=ProductsApiTest %JAVA_OPTS% %TEST_CONFIG% %LOG_CONFIG%
    goto :check_result
)

if "%TEST_TYPE%"=="auth" (
    echo Running Authentication Tests Only...
    mvn test -Dtest=AuthenticationTest %JAVA_OPTS% %TEST_CONFIG% %LOG_CONFIG%
    goto :check_result
)

if "%TEST_TYPE%"=="xml" (
    echo Running Tests from XML Configuration...
    mvn test -DsuiteXmlFile=testng-crud-operations.xml %JAVA_OPTS% %TEST_CONFIG% %LOG_CONFIG%
    goto :check_result
)

if "%TEST_TYPE%"=="runner" (
    echo Running Tests using CrudTestRunner...
    mvn exec:java -Dexec.mainClass="runner.CrudTestRunner" -Dexec.args="suite" %JAVA_OPTS% %TEST_CONFIG%
    goto :check_result
)

REM Default: Run complete suite step by step
echo [1/4] Running Authentication Tests...
mvn test -Dtest=AuthenticationTest %JAVA_OPTS% %TEST_CONFIG% %LOG_CONFIG%
if %ERRORLEVEL% neq 0 (
    echo WARNING: Authentication Tests failed! Continuing with other tests...
)

echo.
echo [2/4] Running Photos API Tests...
mvn test -Dtest=PhotosApiTest %JAVA_OPTS% %TEST_CONFIG% %LOG_CONFIG%
if %ERRORLEVEL% neq 0 (
    echo WARNING: Photos API Tests failed! Continuing with other tests...
)

echo.
echo [3/4] Running Products API Tests...
mvn test -Dtest=ProductsApiTest %JAVA_OPTS% %TEST_CONFIG% %LOG_CONFIG%
if %ERRORLEVEL% neq 0 (
    echo WARNING: Products API Tests failed! Continuing with other tests...
)

echo.
echo [4/4] Running Complete CRUD Test Suite...
mvn test -DsuiteXmlFile=testng-crud-operations.xml %JAVA_OPTS% %TEST_CONFIG% %LOG_CONFIG%

:check_result
if %ERRORLEVEL% neq 0 (
    echo.
    echo ========================================
    echo Some Tests Failed!
    echo ========================================
    echo Check the reports for details.
) else (
    echo.
    echo ========================================
    echo All Tests Completed Successfully!
    echo ========================================
)

echo.
echo Check the following for results:
echo - Excel file: data/SnackHack.xlsx
echo - HTML Report: test-reports/crud-operations-report.html
echo - TestNG Reports: target/surefire-reports/
echo - Logs: logs/crud-operations.log
echo.
echo Usage: run-crud-tests.bat [photos|products|auth|xml|runner|suite]
echo   photos   - Run Photos API tests only
echo   products - Run Products API tests only
echo   auth     - Run Authentication tests only
echo   xml      - Run from XML configuration
echo   runner   - Use CrudTestRunner class
echo   suite    - Run complete suite (default)
echo.

pause
