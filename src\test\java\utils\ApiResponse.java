package utils;

import io.restassured.http.Headers;

/**
 * Universal API Response wrapper for any application
 */
public class ApiResponse {
    private final int statusCode;
    private final String body;
    private final Headers headers;
    
    public ApiResponse(int statusCode, String body, Headers headers) {
        this.statusCode = statusCode;
        this.body = body;
        this.headers = headers;
    }
    
    public int getStatusCode() {
        return statusCode;
    }
    
    public String getBody() {
        return body;
    }
    
    public Headers getHeaders() {
        return headers;
    }
    
    public boolean isSuccess() {
        return statusCode >= 200 && statusCode < 300;
    }
    
    public boolean isClientError() {
        return statusCode >= 400 && statusCode < 500;
    }
    
    public boolean isServerError() {
        return statusCode >= 500;
    }
    
    @Override
    public String toString() {
        return String.format("ApiResponse{statusCode=%d, body='%s'}", statusCode, body);
    }
}
