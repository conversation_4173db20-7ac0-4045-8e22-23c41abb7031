# Basic Package

## Overview
The Basic package contains the foundation classes for the API testing framework. These classes provide core functionality that is used by all test cases.

## Components

### BasicTestCase1.java
This is the main base class that all test cases should extend. It provides:

- **Authentication Management**: Methods for handling token-based authentication
- **API Request Handling**: Methods for making API requests with different HTTP methods
- **Response Validation**: Methods for validating API responses against expected results
- **Database Validation**: Methods for comparing API responses with database records
- **Excel Integration**: Methods for reading test data from and writing results to Excel

#### Key Methods

##### Authentication
- `signIn()`: Authenticates with the API and obtains an access token
- `refreshToken()`: Refreshes an expired token

##### API Requests
- `getPostRequestResponseAndSetInExcel()`: Makes a POST request and stores the response in Excel
- `getPostRequestStatusCode()`: Makes a POST request and returns the status code
- `getPostRequestErrorMessage()`: Makes a POST request and returns any error message

##### Response Validation
- `compareAndUpdateStatusCaseResult()`: Compares actual status code with expected
- `compareAndUpdateErrorMessageResult()`: Compares actual error message with expected
- `compareAndUpdateResponseBody()`: Compares API response with database record

##### Database Integration
- `compareApiWithDatabase()`: Validates API response against database records
- `extractIdFromPostResponse()`: Extracts ID from a POST response for subsequent operations
- `convertDbJsonToCamelCase()`: Converts database field names to match API field names

##### Foreign Key Handling
- `handleStateMasterTable()`: Special handling for state_master table
- `handleForeignKeyComparison()`: Compares foreign key fields between API and database

## Usage
To create a new test case, extend the BasicTestCase1 class:

```java
public class MyTestCase extends BasicTestCase1 {
    
    @Test
    public void testCreateResource() {
        // Test implementation using methods from BasicTestCase1
    }
}
```

## Extension Points
The BasicTestCase1 class is designed to be extended. Key extension points include:

- Adding new field mappings in `getDbToApiFieldMappings()`
- Adding new foreign key mappings in `getForeignKeyMappings()`
- Adding special handling for specific tables (similar to `handleStateMasterTable()`)

## Best Practices
- Use the provided methods rather than implementing your own API request logic
- Leverage the database validation capabilities for comprehensive testing
- Keep test methods focused on a single functionality
