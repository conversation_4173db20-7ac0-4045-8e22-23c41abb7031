<h2>Methods run, sorted chronologically</h2><h3>&gt;&gt; means before, &lt;&lt; means after</h3><p/><br/><em>Surefire suite</em><p/><small><i>(Hover the method name to see the test class name)</i></small><p/>
<table border="1">
<tr><th>Time</th><th>Delta (ms)</th><th>Suite<br>configuration</th><th>Test<br>configuration</th><th>Class<br>configuration</th><th>Groups<br>configuration</th><th>Method<br>configuration</th><th>Test<br>method</th><th>Thread</th><th>Instances</th></tr>
<tr bgcolor="addbb4">  <td>25/05/26 17:01:28</td>   <td>0</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;ForceDefectGenerationTest.setup()[pri:0, instance:testCases.ForceDefectGenerationTest@1ffaf86]">&gt;&gt;setup</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@617901222</td>   <td></td> </tr>
<tr bgcolor="addbb4">  <td>25/05/26 17:01:33</td>   <td>4646</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="ForceDefectGenerationTest.testMultipleDefectGenerationForSameTable()[pri:0, instance:testCases.ForceDefectGenerationTest@1ffaf86]">testMultipleDefectGenerationForSameTable</td> 
  <td>main@617901222</td>   <td></td> </tr>
</table>
