# API Testing Framework

## Overview
This is an enterprise-level API testing framework designed for testing RESTful APIs with comprehensive validation against database records. The framework supports all CRUD operations and provides robust mechanisms for handling authentication, data mapping, and response validation.

## Key Features
- **Flexible Data Mapping**: Handles complex field mappings between API and database
- **Comprehensive Testing**: Tests all CRUD operations with database validation
- **Robust Authentication**: Handles token-based authentication with automatic refresh
- **Data-Driven Testing**: Uses Excel for test data management
- **Foreign Key Relationship Handling**: Automatically resolves and validates relationships
- **Detailed Reporting**: Comprehensive logging and error reporting

## Project Structure
```
├── src/
│   ├── main/java/
│   │   └── com/
│   │       └── api/
│   │           └── utilities/
│   │               ├── common/
│   │               └── config/
│   └── test/java/
│       ├── basic/
│       │   ├── BasicTestCase1.java
│       │   └── README.md
│       ├── testCases/
│       │   ├── CoreService.java
│       │   └── README.md
│       └── utils/
│           ├── DatabaseUtils.java
│           ├── ExcelUtils.java
│           └── README.md
├── testData/
│   └── TestData.xlsx
├── pom.xml
└── README.md
```

## Getting Started

### Prerequisites
- Java 17 or higher
- Maven 3.6 or higher
- PostgreSQL database

### Installation
1. Clone the repository
2. Install dependencies:
   ```
   mvn clean install
   ```

### Configuration
1. Update database connection details in `config.properties`
2. Prepare test data in Excel files

### Running Tests
```
mvn test
```

## Framework Components

### Basic Package
Contains base test classes that provide core functionality for all test cases.

### TestCases Package
Contains actual test implementations for different API services.

### Utils Package
Contains utility classes for database operations, Excel handling, and other common functions.

## Test Data Management
The framework uses Excel files for test data management. Each row in the Excel file represents a test case with:
- Request details (endpoint, method, headers, body)
- Expected response
- Database validation criteria

## Authentication
The framework supports token-based authentication with automatic token refresh when needed. Authentication credentials are stored in the Excel file.

## Database Validation
The framework can validate API responses against database records, handling:
- Different field naming conventions
- Nested objects and relationships
- Foreign key relationships

## Extending the Framework
To add new tests:
1. Add test data to the Excel file
2. Create a new test class extending BasicTestCase1
3. Implement test methods using the provided utilities

## Best Practices
- Keep test data separate from test code
- Use meaningful test names
- Add appropriate assertions
- Validate both API responses and database state

## Contributing
Please read CONTRIBUTING.md for details on our code of conduct and the process for submitting pull requests.

## License
This project is licensed under the MIT License - see the LICENSE file for details.
