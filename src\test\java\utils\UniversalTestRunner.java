package utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Universal Test Runner for any application API testing
 * Handles all HTTP methods, dynamic payloads, PUT workflow, and defect tracking
 */
public class UniversalTestRunner {
    private static final Logger logger = LoggerFactory.getLogger(UniversalTestRunner.class);
    
    private final UniversalApiHandler apiHandler;
    private final ExcelUtils excelUtils;
    private final ObjectMapper mapper;
    
    public UniversalTestRunner() {
        this.apiHandler = new UniversalApiHandler();
        this.excelUtils = new ExcelUtils();
        this.mapper = new ObjectMapper();
    }
    
    /**
     * Execute universal API test from Excel row
     */
    public void executeApiTest(String filePath, String sheetName, int rowNumber, 
                              String baseUrl, String authToken, 
                              int urlCol, int bodyCol, int statusCol, int actualResultCol, int expectedResultCol) {
        try {
            logger.info("=== Executing Universal API Test - Row {} ===", rowNumber);
            
            // Read test data from Excel
            String requestBody = excelUtils.getCellData(filePath, sheetName, rowNumber, bodyCol);
            String expectedResult = excelUtils.getCellData(filePath, sheetName, rowNumber, expectedResultCol);
            
            if (requestBody == null || requestBody.trim().isEmpty()) {
                logger.error("Request body is empty for row {}", rowNumber);
                updateExcelWithFailure(filePath, sheetName, rowNumber, statusCol, actualResultCol, 
                                     "Request body is empty", expectedResult);
                return;
            }
            
            logger.info("Request body from Excel: {}", requestBody);
            
            // Parse request to determine HTTP method
            JsonNode requestNode = mapper.readTree(requestBody);
            String httpMethod = requestNode.get("type").asText().toUpperCase();
            String endpoint = requestNode.get("endpoint").asText();
            
            logger.info("HTTP Method: {}, Endpoint: {}", httpMethod, endpoint);
            
            // Handle PUT workflow - get existing data first
            if ("PUT".equals(httpMethod)) {
                handlePutWorkflow(filePath, sheetName, rowNumber, bodyCol, requestBody, baseUrl, authToken);
                // Re-read the updated request body
                requestBody = excelUtils.getCellData(filePath, sheetName, rowNumber, bodyCol);
            }
            
            // Execute API request
            ApiResponse response = apiHandler.processApiRequest(requestBody, baseUrl, authToken);
            
            // Validate response
            boolean testPassed = validateResponse(response, expectedResult);
            
            if (testPassed) {
                // Test passed
                updateExcelWithSuccess(filePath, sheetName, rowNumber, statusCol, actualResultCol, response);
                logger.info("✅ Test PASSED for row {}", rowNumber);
            } else {
                // Test failed - generate defect
                String defectId = generateDefectForFailure(rowNumber, endpoint, expectedResult, 
                                                         response.getBody(), response.getStatusCode());
                updateExcelWithFailure(filePath, sheetName, rowNumber, statusCol, actualResultCol, 
                                     response.getBody(), expectedResult, defectId);
                logger.error("❌ Test FAILED for row {} - Defect ID: {}", rowNumber, defectId);
            }
            
        } catch (Exception e) {
            logger.error("Error executing API test for row {}: {}", rowNumber, e.getMessage());
            String defectId = generateDefectForFailure(rowNumber, "Unknown", expectedResultCol != -1 ? 
                excelUtils.getCellData(filePath, sheetName, rowNumber, expectedResultCol) : "Unknown", 
                "Exception: " + e.getMessage(), 500);
            updateExcelWithFailure(filePath, sheetName, rowNumber, statusCol, actualResultCol, 
                                 "Exception: " + e.getMessage(), "Success", defectId);
        }
    }
    
    /**
     * Handle PUT workflow - get existing data and update Excel
     */
    private void handlePutWorkflow(String filePath, String sheetName, int rowNumber, int bodyCol, 
                                  String originalRequestBody, String baseUrl, String authToken) {
        try {
            logger.info("Handling PUT workflow - Getting existing data first");
            
            JsonNode requestNode = mapper.readTree(originalRequestBody);
            String endpoint = requestNode.get("endpoint").asText();
            
            // Build getAll URL
            String getAllUrl = endpoint.replaceAll("/(save|update)$", "/getAll");
            
            // Create getAll request
            String getAllRequestBody = String.format("""
                {
                  "endpoint": "%s",
                  "type": "get",
                  "payload": null,
                  "tenantId": "%s",
                  "auth": "%s"
                }
                """, getAllUrl, 
                requestNode.has("tenantId") ? requestNode.get("tenantId").asText() : "{{tenantId}}", 
                authToken);
            
            // Execute getAll request
            ApiResponse getAllResponse = apiHandler.processApiRequest(getAllRequestBody, baseUrl, authToken);
            
            if (getAllResponse.isSuccess()) {
                // Extract 0th index payload
                JsonNode existingData = extractZeroIndexPayload(getAllResponse.getBody());
                
                if (existingData != null) {
                    // Update original request with existing payload
                    JsonNode updatedRequest = updateRequestWithExistingPayload(requestNode, existingData);
                    String updatedRequestBody = mapper.writeValueAsString(updatedRequest);
                    
                    // Update Excel with new request body
                    excelUtils.setCellData(filePath, sheetName, rowNumber, bodyCol, updatedRequestBody);
                    logger.info("Updated Excel with existing payload for PUT operation");
                    logger.info("Updated request body: {}", updatedRequestBody);
                } else {
                    logger.warn("No existing data found for PUT operation");
                }
            } else {
                logger.error("Failed to get existing data for PUT: {}", getAllResponse.getBody());
            }
            
        } catch (Exception e) {
            logger.error("Error in PUT workflow: {}", e.getMessage());
        }
    }
    
    /**
     * Extract 0th index payload from response
     */
    private JsonNode extractZeroIndexPayload(String responseBody) {
        try {
            JsonNode responseNode = mapper.readTree(responseBody);
            
            if (responseNode.isArray() && responseNode.size() > 0) {
                return responseNode.get(0);
            } else if (responseNode.has("data") && responseNode.get("data").isArray()) {
                JsonNode dataArray = responseNode.get("data");
                return dataArray.size() > 0 ? dataArray.get(0) : null;
            } else if (responseNode.has("content") && responseNode.get("content").isArray()) {
                JsonNode contentArray = responseNode.get("content");
                return contentArray.size() > 0 ? contentArray.get(0) : null;
            }
            
            return null;
        } catch (Exception e) {
            logger.error("Error extracting 0th index payload: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * Update request with existing payload
     */
    private JsonNode updateRequestWithExistingPayload(JsonNode originalRequest, JsonNode existingPayload) {
        try {
            JsonNode updatedRequest = originalRequest.deepCopy();
            ((com.fasterxml.jackson.databind.node.ObjectNode) updatedRequest).set("payload", existingPayload);
            return updatedRequest;
        } catch (Exception e) {
            logger.error("Error updating request with existing payload: {}", e.getMessage());
            return originalRequest;
        }
    }
    
    /**
     * Validate API response
     */
    private boolean validateResponse(ApiResponse response, String expectedResult) {
        try {
            // Basic validation - can be enhanced based on requirements
            if (expectedResult == null || expectedResult.trim().isEmpty()) {
                return response.isSuccess();
            }
            
            if ("Success".equalsIgnoreCase(expectedResult.trim())) {
                return response.isSuccess();
            }
            
            if (expectedResult.startsWith("Status:")) {
                int expectedStatus = Integer.parseInt(expectedResult.substring(7).trim());
                return response.getStatusCode() == expectedStatus;
            }
            
            // Default: check if response is successful
            return response.isSuccess();
            
        } catch (Exception e) {
            logger.error("Error validating response: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * Update Excel with success result
     */
    private void updateExcelWithSuccess(String filePath, String sheetName, int rowNumber, 
                                       int statusCol, int actualResultCol, ApiResponse response) {
        try {
            excelUtils.setCellData(filePath, sheetName, rowNumber, statusCol, "Passed");
            excelUtils.setCellData(filePath, sheetName, rowNumber, actualResultCol, 
                                 "Status: " + response.getStatusCode() + " - Success");
        } catch (Exception e) {
            logger.error("Error updating Excel with success: {}", e.getMessage());
        }
    }
    
    /**
     * Update Excel with failure result
     */
    private void updateExcelWithFailure(String filePath, String sheetName, int rowNumber, 
                                       int statusCol, int actualResultCol, String actualResult, 
                                       String expectedResult) {
        updateExcelWithFailure(filePath, sheetName, rowNumber, statusCol, actualResultCol, 
                              actualResult, expectedResult, null);
    }
    
    /**
     * Update Excel with failure result and defect ID
     */
    private void updateExcelWithFailure(String filePath, String sheetName, int rowNumber, 
                                       int statusCol, int actualResultCol, String actualResult, 
                                       String expectedResult, String defectId) {
        try {
            excelUtils.setCellData(filePath, sheetName, rowNumber, statusCol, "Failed");
            
            String failureMessage = actualResult;
            if (defectId != null) {
                failureMessage += " | Defect ID: " + defectId;
            }
            
            excelUtils.setCellData(filePath, sheetName, rowNumber, actualResultCol, failureMessage);
        } catch (Exception e) {
            logger.error("Error updating Excel with failure: {}", e.getMessage());
        }
    }
    
    /**
     * Generate defect for test failure
     */
    private String generateDefectForFailure(int rowNumber, String endpoint, String expectedResult, 
                                          String actualResult, int statusCode) {
        try {
            String testCaseName = "API Test Row " + rowNumber;
            String errorDetails = String.format("Status Code: %d, Response: %s", statusCode, actualResult);
            
            return apiHandler.generateDefectForFailure(testCaseName, endpoint, expectedResult, 
                                                     actualResult, errorDetails);
        } catch (Exception e) {
            logger.error("Error generating defect: {}", e.getMessage());
            return "DEFECT_GENERATION_FAILED";
        }
    }
}
