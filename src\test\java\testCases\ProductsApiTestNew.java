package testCases;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

/**
 * Products API Test Class
 * Tests all CRUD operations for Products entity with comprehensive validations
 * Extends BaseTestOperations for common functionality
 * 
 * Test Focus:
 * - Status code validation for successful creation
 * - Constraint violation testing (null/unique)
 * - Error message validation
 * - Request body and response matching
 * - Database validation for products table
 */
public class ProductsApiTestNew extends BaseTestOperations {
    private static final Logger logger = LoggerFactory.getLogger(ProductsApiTestNew.class);
    
    private String sheetName = "Products";
    
    // Test data row numbers in Excel
    private static final int AUTH_ROW = 13;
    private static final int CREATE_ROW = 2;
    private static final int UPDATE_ROW = 3;
    private static final int GET_ALL_ROW = 4;
    private static final int GET_BY_ID_ROW = 5;
    private static final int DELETE_ROW = 6;

    @BeforeClass
    public void setup() {
        logger.info("=== Products API Test Setup ===");
        
        try {
            // Initialize common components
            initializeCommonComponents();
            
            // Get authentication token from Excel row 13
            authToken = getAuthToken(sheetName);
            
            logger.info("Products API Test setup completed successfully");
            
        } catch (Exception e) {
            logger.error("Failed to setup Products API Test: " + e.getMessage());
            throw new RuntimeException("Setup failed", e);
        }
    }

    /**
     * Single test method that executes all CRUD operations for Products
     * Reads test data from Excel sheet and validates:
     * 1. Status codes for successful creation
     * 2. Constraint violations with error messages
     * 3. Request/response body matching
     * 4. Database validation for products table
     */
    @Test(priority = 1)
    public void testProductsCrudOperations() {
        logger.info("=== Starting Products CRUD Operations Test ===");
        
        try {
            // 1. Test POST operation (CREATE)
            logger.info("🔄 CRUD Operation: CREATE (POST)");
            executePostOperation(sheetName, CREATE_ROW, "products");
            
            // 2. Test PUT operation (UPDATE) - uses created entity ID
            logger.info("🔄 CRUD Operation: UPDATE (PUT)");
            executePutOperation(sheetName, UPDATE_ROW, "products");
            
            // 3. Test GET ALL operation (READ ALL)
            logger.info("🔄 CRUD Operation: READ ALL (GET)");
            executeGetAllOperation(sheetName, GET_ALL_ROW, "products");
            
            // 4. Test GET BY ID operation (READ BY ID)
            logger.info("🔄 CRUD Operation: READ BY ID (GET)");
            executeGetByIdOperation(sheetName, GET_BY_ID_ROW, "products");
            
            // 5. Test DELETE operation (DELETE)
            logger.info("🔄 CRUD Operation: DELETE");
            executeDeleteOperation(sheetName, DELETE_ROW, "products");
            
            logger.info("✅ Products CRUD Operations Test completed successfully");
            
        } catch (Exception e) {
            logger.error("❌ Products CRUD Operations Test failed: " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * Additional test for constraint violations specific to Products
     */
    @Test(priority = 2)
    public void testProductsConstraintValidations() {
        logger.info("=== Testing Products Constraint Validations ===");
        
        try {
            String excelPath = config.getExcelFilePath();
            
            // Read test data for constraint testing
            String url = excelUtils.getCellData(excelPath, sheetName, CREATE_ROW, COL_URL);
            String requestBody = excelUtils.getCellData(excelPath, sheetName, CREATE_ROW, COL_REQUEST_BODY);
            
            // Test Products-specific constraints
            testProductsNullConstraints(url, requestBody);
            testProductsUniqueConstraints(url, requestBody);
            testProductsDataTypeConstraints(url, requestBody);
            
            logger.info("✅ Products Constraint Validations completed");
            
        } catch (Exception e) {
            logger.error("❌ Products Constraint Validations failed: " + e.getMessage());
        }
    }
    
    /**
     * Test Products-specific null constraints
     */
    private void testProductsNullConstraints(String url, String requestBody) {
        logger.info("Testing Products null constraints");
        
        // Test null name
        testNullField(url, requestBody, "name", "products");
        
        // Test null price
        testNullField(url, requestBody, "price", "products");
        
        // Test null category
        testNullField(url, requestBody, "category", "products");
    }
    
    /**
     * Test Products-specific unique constraints
     */
    private void testProductsUniqueConstraints(String url, String requestBody) {
        logger.info("Testing Products unique constraints");
        
        // Test duplicate product SKU (if unique constraint exists)
        testUniqueConstraint(url, requestBody, "products");
    }
    
    /**
     * Test Products-specific data type constraints
     */
    private void testProductsDataTypeConstraints(String url, String requestBody) {
        logger.info("Testing Products data type constraints");
        
        // Test invalid price (non-numeric)
        testInvalidDataType(url, requestBody, "price", "invalid_price", "products");
        
        // Test invalid stock (non-numeric)
        testInvalidDataType(url, requestBody, "stock", "invalid_stock", "products");
    }
    
    /**
     * Helper method to test null field constraints
     */
    private void testNullField(String url, String requestBody, String fieldName, String entityType) {
        try {
            logger.info("Testing null constraint for field: {}", fieldName);
            
            // Create request body with null field
            String nullBody = requestBody.replaceAll("\"" + fieldName + "\"\\s*:\\s*\"[^\"]*\"", 
                                                   "\"" + fieldName + "\":null");
            
            var response = io.restassured.RestAssured.given()
                .contentType("application/json")
                .header("Authorization", authToken)
                .body(nullBody)
                .post(url);
            
            int statusCode = response.getStatusCode();
            String responseBody = response.getBody().asString();
            
            // Validate constraint violation
            if (statusCode == 400 || statusCode == 422) {
                logger.info("✅ Null constraint validation passed for {} - Status: {}", fieldName, statusCode);
                
                // Validate error message contains field name
                if (responseBody.toLowerCase().contains(fieldName.toLowerCase()) ||
                    responseBody.toLowerCase().contains("null") ||
                    responseBody.toLowerCase().contains("required")) {
                    logger.info("✅ Error message validation passed: {}", responseBody);
                } else {
                    logger.warn("⚠️ Error message validation failed for {}: {}", fieldName, responseBody);
                }
            } else {
                logger.warn("⚠️ Null constraint test failed for {} - Expected: 400/422, Actual: {}", 
                           fieldName, statusCode);
            }
            
        } catch (Exception e) {
            logger.error("Error testing null constraint for {}: {}", fieldName, e.getMessage());
        }
    }
    
    /**
     * Helper method to test invalid data type constraints
     */
    private void testInvalidDataType(String url, String requestBody, String fieldName, 
                                   String invalidValue, String entityType) {
        try {
            logger.info("Testing invalid data type for field: {}", fieldName);
            
            // Create request body with invalid data type
            String invalidBody = requestBody.replaceAll("\"" + fieldName + "\"\\s*:\\s*[\\d\\.]+", 
                                                       "\"" + fieldName + "\":\"" + invalidValue + "\"");
            
            var response = io.restassured.RestAssured.given()
                .contentType("application/json")
                .header("Authorization", authToken)
                .body(invalidBody)
                .post(url);
            
            int statusCode = response.getStatusCode();
            String responseBody = response.getBody().asString();
            
            // Validate data type constraint violation
            if (statusCode == 400 || statusCode == 422) {
                logger.info("✅ Data type constraint validation passed for {} - Status: {}", fieldName, statusCode);
                
                // Validate error message
                if (responseBody.toLowerCase().contains("invalid") ||
                    responseBody.toLowerCase().contains("type") ||
                    responseBody.toLowerCase().contains("format")) {
                    logger.info("✅ Error message validation passed: {}", responseBody);
                } else {
                    logger.warn("⚠️ Error message validation failed for {}: {}", fieldName, responseBody);
                }
            } else {
                logger.warn("⚠️ Data type constraint test failed for {} - Expected: 400/422, Actual: {}", 
                           fieldName, statusCode);
            }
            
        } catch (Exception e) {
            logger.error("Error testing data type constraint for {}: {}", fieldName, e.getMessage());
        }
    }
}
