# CRUD Operations API Testing Configuration
# This file contains configuration settings for Photos and Products API testing

# ==================== EXCEL CONFIGURATION ====================
# Path to the Excel file containing test data
excel.file.path=data/SnackHack.xlsx

# Excel column mappings (1-based indexing)
excel.column.url=3
excel.column.body=4
excel.column.expected=5
excel.column.actual=6
excel.column.status=7
excel.column.defectid=8
excel.column.tablename=2

# ==================== API CONFIGURATION ====================
# Base URL for the API
base.url=http://localhost:8080

# Authentication configuration
auth.endpoint=/api/auth/login
auth.username=testuser
auth.password=testpass
auth.sheet.name=Auth
auth.row.number=13

# Request timeout settings (in seconds)
request.timeout=30
retry.count=3

# ==================== PHOTOS API ENDPOINTS ====================
# Photos CRUD endpoints
photos.post.endpoint=/api/photos
photos.put.endpoint=/api/photos/{id}
photos.get.endpoint=/api/photos/{id}
photos.getall.endpoint=/api/photos
photos.delete.endpoint=/api/photos/{id}

# ==================== PRODUCTS API ENDPOINTS ====================
# Products CRUD endpoints
products.post.endpoint=/api/products
products.put.endpoint=/api/products/{id}
products.get.endpoint=/api/products/{id}
products.getall.endpoint=/api/products
products.delete.endpoint=/api/products/{id}

# ==================== DATABASE CONFIGURATION ====================
# Database connection settings
database.url=*************************************
database.username=root
database.password=password
database.driver=com.mysql.cj.jdbc.Driver
database.schema=snackhack

# Database validation settings
database.validation.enabled=true
database.connection.pool.size=5
database.query.timeout=30

# ==================== TEST EXECUTION CONFIGURATION ====================
# Test execution settings
test.start.row=14
test.parallel.threads=1
test.timeout.seconds=300

# Test data generation
dynamic.data.generation.enabled=true
faker.locale=en

# ==================== DEFECT TRACKING CONFIGURATION ====================
# Defect tracking settings
defect.tracking.enabled=true
defect.id.format=D_{tableName}_{sequence}

# Bug tracking system integration (optional)
bug.tracking.system=mock
jira.url=https://your-jira-instance.atlassian.net
jira.project.key=API
jira.auth.token=your-jira-token

# ==================== LOGGING CONFIGURATION ====================
# Logging settings
log.level=INFO
log.detailed.enabled=false
log.file.path=logs/crud-operations.log

# ==================== VALIDATION CONFIGURATION ====================
# Validation settings
validation.database.enabled=true
validation.status.code.enabled=true
validation.response.body.enabled=true
validation.constraint.enabled=true

# Response validation timeouts
validation.timeout.seconds=10
validation.retry.attempts=3

# ==================== CONSTRAINT TESTING CONFIGURATION ====================
# Null constraint testing
constraint.null.enabled=true
constraint.null.fields=title,name,email

# Unique constraint testing
constraint.unique.enabled=true
constraint.unique.fields=sku,email,username

# ==================== PERFORMANCE CONFIGURATION ====================
# Performance testing settings
performance.testing.enabled=false
performance.max.response.time=5000
performance.concurrent.users=10

# ==================== REPORTING CONFIGURATION ====================
# Test reporting settings
reporting.enabled=true
reporting.format=html,json
reporting.output.directory=test-reports

# Email reporting (optional)
email.reporting.enabled=false
email.smtp.host=smtp.gmail.com
email.smtp.port=587
email.recipients=<EMAIL>

# ==================== ENVIRONMENT SPECIFIC SETTINGS ====================
# Environment settings
environment=test
test.environment.url=http://localhost:8080
staging.environment.url=http://staging.company.com
production.environment.url=http://api.company.com

# ==================== SECURITY CONFIGURATION ====================
# Security settings
security.ssl.enabled=false
security.certificate.validation=false
security.api.key=your-api-key

# ==================== RETRY CONFIGURATION ====================
# Retry settings for failed tests
retry.enabled=true
retry.max.attempts=3
retry.delay.seconds=5

# ==================== DATA CLEANUP CONFIGURATION ====================
# Data cleanup settings
cleanup.enabled=true
cleanup.after.test=true
cleanup.test.data.retention.days=7

# ==================== MONITORING CONFIGURATION ====================
# Monitoring and alerting
monitoring.enabled=false
monitoring.webhook.url=https://hooks.slack.com/your-webhook
monitoring.alert.threshold=5

# ==================== CUSTOM CONFIGURATION ====================
# Custom settings for specific requirements
custom.photo.max.file.size=10485760
custom.product.max.price=99999.99
custom.user.password.complexity=true

# ==================== FEATURE FLAGS ====================
# Feature flags for enabling/disabling specific features
feature.database.validation=true
feature.defect.tracking=true
feature.faker.data.generation=true
feature.excel.color.coding=true
feature.foreign.key.validation=true
feature.constraint.testing=true
feature.performance.monitoring=false
feature.email.notifications=false
