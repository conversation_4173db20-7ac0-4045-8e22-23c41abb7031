<!DOCTYPE html>

<html>
  <head>
  <meta charset='utf-8'>
  <title>TestNG reports</title>

    <link type="text/css" href="testng-reports1.css" rel="stylesheet" id="ultra" />
    <link type="text/css" href="testng-reports.css" rel="stylesheet" id="retro" disabled="false"/>
    <script type="text/javascript" src="jquery-3.6.0.min.js"></script>
    <script type="text/javascript" src="testng-reports.js"></script>
    <script type="text/javascript" src="https://www.google.com/jsapi"></script>
    <script type='text/javascript'>
      google.load('visualization', '1', {packages:['table']});
      google.setOnLoadCallback(drawTable);
      var suiteTableInitFunctions = new Array();
      var suiteTableData = new Array();
    </script>
    <!--
      <script type="text/javascript" src="jquery-ui/js/jquery-ui-1.8.16.custom.min.js"></script>
     -->
  </head>

  <body>    <div class="top-banner-root">
      <span class="top-banner-title-font">Test results</span>
      <button id="button" class="button">Switch Retro Theme</button> <!-- button -->
      <br/>
      <span class="top-banner-font-1">1 suite</span>
    </div> <!-- top-banner-root -->
    <div class="navigator-root">
      <div class="navigator-suite-header">
        <span>All suites</span>
        <a href="#" title="Collapse/expand all the suites" class="collapse-all-link">
          <img src="collapseall.gif" class="collapse-all-icon">
          </img> <!-- collapse-all-icon -->
        </a> <!-- collapse-all-link -->
      </div> <!-- navigator-suite-header -->
      <div class="suite">
        <div class="rounded-window">
          <div class="suite-header light-rounded-window-top">
            <a href="#" panel-name="suite-Surefire_suite" class="navigator-link">
              <span class="suite-name border-passed">Surefire suite</span>
            </a> <!-- navigator-link -->
          </div> <!-- suite-header light-rounded-window-top -->
          <div class="navigator-suite-content">
            <div class="suite-section-title">
              <span>Info</span>
            </div> <!-- suite-section-title -->
            <div class="suite-section-content">
              <ul>
                <li>
                  <a href="#" panel-name="test-xml-Surefire_suite" class="navigator-link ">
                    <span>[unset file name]</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="testlist-Surefire_suite" class="navigator-link ">
                    <span class="test-stats">1 test</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="group-Surefire_suite" class="navigator-link ">
                    <span>0 groups</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="times-Surefire_suite" class="navigator-link ">
                    <span>Times</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="reporter-Surefire_suite" class="navigator-link ">
                    <span>Reporter output</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="ignored-methods-Surefire_suite" class="navigator-link ">
                    <span>Ignored methods</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="chronological-Surefire_suite" class="navigator-link ">
                    <span>Chronological view</span>
                  </a> <!-- navigator-link  -->
                </li>
              </ul>
            </div> <!-- suite-section-content -->
            <div class="result-section">
              <div class="suite-section-title">
                <span>Results</span>
              </div> <!-- suite-section-title -->
              <div class="suite-section-content">
                <ul>
                  <li>
                    <span class="method-stats">1 method,   1 passed</span>
                  </li>
                  <li>
                    <span class="method-list-title passed">Passed methods</span>
                    <span class="show-or-hide-methods passed">
                      <a href="#" panel-name="suite-Surefire_suite" class="hide-methods passed suite-Surefire_suite"> (hide)</a> <!-- hide-methods passed suite-Surefire_suite -->
                      <a href="#" panel-name="suite-Surefire_suite" class="show-methods passed suite-Surefire_suite"> (show)</a> <!-- show-methods passed suite-Surefire_suite -->
                    </span>
                    <div class="method-list-content passed suite-Surefire_suite">
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Surefire_suite" title="testCases.ForceDefectGenerationTest" class="method navigator-link" hash-for-method="testMultipleDefectGenerationForSameTable">testMultipleDefectGenerationForSameTable</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                    </div> <!-- method-list-content passed suite-Surefire_suite -->
                  </li>
                </ul>
              </div> <!-- suite-section-content -->
            </div> <!-- result-section -->
          </div> <!-- navigator-suite-content -->
        </div> <!-- rounded-window -->
      </div> <!-- suite -->
    </div> <!-- navigator-root -->
    <div class="wrapper">
      <div class="main-panel-root">
        <div panel-name="suite-Surefire_suite" class="panel Surefire_suite">
          <div class="suite-Surefire_suite-class-passed">
            <div class="main-panel-header rounded-window-top">
              <img src="passed.png"/>
              <span class="class-name">testCases.ForceDefectGenerationTest</span>
            </div> <!-- main-panel-header rounded-window-top -->
            <div class="main-panel-content rounded-window-bottom">
              <div class="method">
                <div class="method-content">
                  <a name="testMultipleDefectGenerationForSameTable">
                  </a> <!-- testMultipleDefectGenerationForSameTable -->
                  <span class="method-name">testMultipleDefectGenerationForSameTable</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
            </div> <!-- main-panel-content rounded-window-bottom -->
          </div> <!-- suite-Surefire_suite-class-passed -->
        </div> <!-- panel Surefire_suite -->
        <div panel-name="test-xml-Surefire_suite" class="panel">
          <div class="main-panel-header rounded-window-top">
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <pre>
&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;
&lt;!DOCTYPE suite SYSTEM &quot;https://testng.org/testng-1.0.dtd&quot;&gt;
&lt;suite thread-count=&quot;1&quot; name=&quot;Surefire suite&quot; verbose=&quot;0&quot;&gt;
  &lt;test thread-count=&quot;1&quot; name=&quot;Surefire test&quot; verbose=&quot;0&quot;&gt;
    &lt;method-selectors&gt;
      &lt;method-selector&gt;
        &lt;selector-class name=&quot;org.apache.maven.surefire.testng.utils.MethodSelector&quot; priority=&quot;10000&quot;/&gt;
      &lt;/method-selector&gt;
    &lt;/method-selectors&gt;
    &lt;classes&gt;
      &lt;class name=&quot;testCases.ForceDefectGenerationTest&quot;/&gt;
    &lt;/classes&gt;
  &lt;/test&gt; &lt;!-- Surefire test --&gt;
&lt;/suite&gt; &lt;!-- Surefire suite --&gt;
            </pre>
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="testlist-Surefire_suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Tests for Surefire suite</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <ul>
              <li>
                <span class="test-name">Surefire test (1 class)</span>
              </li>
            </ul>
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="group-Surefire_suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Groups for Surefire suite</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="times-Surefire_suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Times for Surefire suite</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <div class="times-div">
              <script type="text/javascript">
suiteTableInitFunctions.push('tableData_Surefire_suite');
function tableData_Surefire_suite() {
var data = new google.visualization.DataTable();
data.addColumn('number', 'Number');
data.addColumn('string', 'Method');
data.addColumn('string', 'Class');
data.addColumn('number', 'Time (ms)');
data.addRows(1);
data.setCell(0, 0, 0)
data.setCell(0, 1, 'testMultipleDefectGenerationForSameTable')
data.setCell(0, 2, 'testCases.ForceDefectGenerationTest')
data.setCell(0, 3, 14837);
window.suiteTableData['Surefire_suite']= { tableData: data, tableDiv: 'times-div-Surefire_suite'}
return data;
}
              </script>
              <span class="suite-total-time">Total running time: 14 seconds</span>
              <div id="times-div-Surefire_suite">
              </div> <!-- times-div-Surefire_suite -->
            </div> <!-- times-div -->
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="reporter-Surefire_suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Reporter output for Surefire suite</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="ignored-methods-Surefire_suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">2 ignored methods</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <div class="ignored-class-div">
              <span class="ignored-class-name">testCases.ForceDefectGenerationTest</span>
              <div class="ignored-methods-div">
                <span class="ignored-method-name">testDefectGenerationForDifferentTables</span>
                <br/>
                <span class="ignored-method-name">testForceDefectGeneration</span>
                <br/>
              </div> <!-- ignored-methods-div -->
            </div> <!-- ignored-class-div -->
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="chronological-Surefire_suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Methods in chronological order</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <div class="chronological-class">
              <div class="chronological-class-name">testCases.ForceDefectGenerationTest</div> <!-- chronological-class-name -->
              <div class="configuration-class before">
                <span class="method-name">setup</span>
                <span class="method-start">0 ms</span>
              </div> <!-- configuration-class before -->
              <div class="test-method">
                <span class="method-name">testMultipleDefectGenerationForSameTable</span>
                <span class="method-start">4654 ms</span>
              </div> <!-- test-method -->
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
      </div> <!-- main-panel-root -->
    </div> <!-- wrapper -->
  </body>
<script type="text/javascript" src="testng-reports2.js"></script>
</html>
