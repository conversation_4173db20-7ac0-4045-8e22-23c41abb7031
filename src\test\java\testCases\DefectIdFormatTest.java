package testCases;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.annotations.Test;
import utils.DefectDetails;
import utils.DefectTracker;

/**
 * Test to demonstrate the new defect ID format: D_TableName_001
 */
public class DefectIdFormatTest {
    private static final Logger logger = LoggerFactory.getLogger(DefectIdFormatTest.class);
    
    @Test
    public void testDefectIdFormat() {
        logger.info("=== Testing New Defect ID Format: D_TableName_001 ===");
        
        DefectTracker defectTracker = new DefectTracker();
        
        // Test 1: BundleProduct defect
        DefectDetails bundleProductDefect = new DefectDetails();
        bundleProductDefect.setTitle("API Test Failure: API_POST_Row_14");
        bundleProductDefect.setDescription("""
            **API Test Case Failed**
            
            **Endpoint:** /order/api/BundleProduct/save
            **Expected Result:** 201
            **Actual Result:** 400
            **Error Details:** Status code mismatch or validation failed. Expected: 201, Actual: 400
            
            **Steps to Reproduce:**
            1. Execute API test for endpoint: /order/api/BundleProduct/save
            2. Verify response matches expected result
            3. Observe the failure
            
            **Environment:** Test
            **Generated by:** Automated API Testing Framework
            """);
        bundleProductDefect.setSeverity("Medium");
        bundleProductDefect.setPriority("High");
        bundleProductDefect.setComponent("API Testing");
        bundleProductDefect.setEnvironment("Test");
        
        String defectId1 = defectTracker.createDefect(bundleProductDefect);
        logger.info("✅ BundleProduct Defect ID: {}", defectId1);
        
        // Test 2: CountryMaster defect
        DefectDetails countryMasterDefect = new DefectDetails();
        countryMasterDefect.setTitle("API Test Failure: API_GET_Row_5");
        countryMasterDefect.setDescription("""
            **API Test Case Failed**
            
            **Endpoint:** /core/api/CountryMaster/getAll
            **Expected Result:** 200
            **Actual Result:** 500
            **Error Details:** Internal server error
            
            **Steps to Reproduce:**
            1. Execute API test for endpoint: /core/api/CountryMaster/getAll
            2. Verify response matches expected result
            3. Observe the failure
            
            **Environment:** Test
            **Generated by:** Automated API Testing Framework
            """);
        countryMasterDefect.setSeverity("High");
        countryMasterDefect.setPriority("Critical");
        countryMasterDefect.setComponent("API Testing");
        countryMasterDefect.setEnvironment("Test");
        
        String defectId2 = defectTracker.createDefect(countryMasterDefect);
        logger.info("✅ CountryMaster Defect ID: {}", defectId2);
        
        // Test 3: StateMaster defect
        DefectDetails stateMasterDefect = new DefectDetails();
        stateMasterDefect.setTitle("API Test Failure: API_PUT_Row_8");
        stateMasterDefect.setDescription("""
            **API Test Case Failed**
            
            **Endpoint:** /core/api/StateMaster/update/123
            **Expected Result:** 200
            **Actual Result:** 404
            **Error Details:** Record not found
            
            **Steps to Reproduce:**
            1. Execute API test for endpoint: /core/api/StateMaster/update/123
            2. Verify response matches expected result
            3. Observe the failure
            
            **Environment:** Test
            **Generated by:** Automated API Testing Framework
            """);
        stateMasterDefect.setSeverity("Medium");
        stateMasterDefect.setPriority("Medium");
        stateMasterDefect.setComponent("API Testing");
        stateMasterDefect.setEnvironment("Test");
        
        String defectId3 = defectTracker.createDefect(stateMasterDefect);
        logger.info("✅ StateMaster Defect ID: {}", defectId3);
        
        // Test 4: Unknown endpoint defect
        DefectDetails unknownDefect = new DefectDetails();
        unknownDefect.setTitle("API Test Failure: API_DELETE_Row_12");
        unknownDefect.setDescription("""
            **API Test Case Failed**
            
            **Endpoint:** /unknown/api/endpoint
            **Expected Result:** 204
            **Actual Result:** 403
            **Error Details:** Forbidden access
            
            **Steps to Reproduce:**
            1. Execute API test for endpoint: /unknown/api/endpoint
            2. Verify response matches expected result
            3. Observe the failure
            
            **Environment:** Test
            **Generated by:** Automated API Testing Framework
            """);
        unknownDefect.setSeverity("Low");
        unknownDefect.setPriority("Low");
        unknownDefect.setComponent("API Testing");
        unknownDefect.setEnvironment("Test");
        
        String defectId4 = defectTracker.createDefect(unknownDefect);
        logger.info("✅ Unknown Endpoint Defect ID: {}", defectId4);
        
        logger.info("=== Defect ID Format Test Completed ===");
        logger.info("Expected Format: D_TableName_001");
        logger.info("Generated IDs:");
        logger.info("  1. BundleProduct: {}", defectId1);
        logger.info("  2. CountryMaster: {}", defectId2);
        logger.info("  3. StateMaster: {}", defectId3);
        logger.info("  4. Unknown: {}", defectId4);
    }
}
