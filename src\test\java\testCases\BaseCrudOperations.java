package testCases;

import io.restassured.RestAssured;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.testng.Assert;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import utils.TestConfiguration;
import utils.ExcelUtils;
import utils.DatabaseValidationUtils;
import com.github.javafaker.Faker;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;

/**
 * Base CRUD Operations class containing all common CRUD methods
 * This class contains all the common code for POST, PUT, GET, DELETE operations
 */
public class BaseCrudOperations {
    protected static final Logger logger = LoggerFactory.getLogger(BaseCrudOperations.class);
    protected TestConfiguration config;
    protected ExcelUtils excelUtils;
    protected DatabaseValidationUtils dbUtils;
    protected Faker faker;
    protected ObjectMapper objectMapper;
    protected String authToken;

    // Excel column constants
    protected static final int COL_URL = 1;           // Column A
    protected static final int COL_REQUEST_BODY = 2;  // Column B
    protected static final int COL_EXPECTED = 3;      // Column C
    protected static final int COL_ACTUAL = 4;        // Column D
    protected static final int COL_STATUS = 5;        // Column E
    protected static final int COL_DEFECT_ID = 6;     // Column F

    /**
     * Initialize common components
     */
    protected void initializeCommonComponents() {
        try {
            config = TestConfiguration.getInstance();
            excelUtils = new ExcelUtils();
            dbUtils = new DatabaseValidationUtils();
            faker = new Faker();
            objectMapper = new ObjectMapper();

            // Set RestAssured base URI
            RestAssured.baseURI = config.getBaseUrl();

            logger.info("Common components initialized successfully");
        } catch (Exception e) {
            logger.error("Failed to initialize common components: " + e.getMessage());
            throw new RuntimeException("Initialization failed", e);
        }
    }

    /**
     * Get authentication token from Excel
     */
    protected String getAuthToken(String sheetName) {
        try {
            logger.info("Getting authentication token from Excel sheet: {}", sheetName);

            String excelPath = config.getExcelFilePath();

            // Get auth data from row 13 (as per requirement)
            String authUrl = excelUtils.getCellData(excelPath, sheetName, 13, COL_URL);
            String authBody = excelUtils.getCellData(excelPath, sheetName, 13, COL_REQUEST_BODY);

            if (authUrl == null || authUrl.isEmpty()) {
                logger.warn("Auth URL not found in Excel, using default");
                authUrl = "/api/auth/login";
            }

            if (authBody == null || authBody.isEmpty()) {
                logger.warn("Auth body not found in Excel, using default");
                authBody = "{\"username\":\"admin\",\"password\":\"admin\"}";
            }

            // Make authentication request
            Response response = RestAssured.given()
                .contentType("application/json")
                .body(authBody)
                .post(authUrl);

            if (response.getStatusCode() == 200) {
                String token = response.jsonPath().getString("token");
                if (token == null) {
                    token = response.jsonPath().getString("access_token");
                }
                if (token == null) {
                    token = "Bearer " + System.currentTimeMillis(); // Fallback token
                }

                logger.info("Authentication successful, token obtained");
                return token;
            } else {
                logger.warn("Authentication failed, using fallback token");
                return "Bearer fallback_token_" + System.currentTimeMillis();
            }

        } catch (Exception e) {
            logger.error("Error getting auth token: " + e.getMessage());
            return "Bearer error_token_" + System.currentTimeMillis();
        }
    }

    /**
     * Execute POST operation (CREATE)
     */
    protected void executePostOperation(String sheetName, int rowNumber, String entityType) {
        logger.info("=== Executing POST Operation for {} (Row: {}) ===", entityType, rowNumber);

        try {
            String excelPath = config.getExcelFilePath();

            // Read test data from Excel
            String url = excelUtils.getCellData(excelPath, sheetName, rowNumber, COL_URL);
            String requestBody = excelUtils.getCellData(excelPath, sheetName, rowNumber, COL_REQUEST_BODY);
            String expectedResult = excelUtils.getCellData(excelPath, sheetName, rowNumber, COL_EXPECTED);

            logger.info("POST URL: {}", url);
            logger.info("POST Body: {}", requestBody);

            // Generate dynamic data using Faker if needed
            requestBody = generateDynamicData(requestBody, entityType);

            // Make POST request
            RequestSpecification request = RestAssured.given()
                .contentType("application/json")
                .header("Authorization", authToken);

            if (requestBody != null && !requestBody.isEmpty()) {
                request.body(requestBody);
            }

            Response response = request.post(url);

            // Validate response
            int actualStatusCode = response.getStatusCode();
            String actualResponse = response.getBody().asString();

            logger.info("POST Response Status: {}", actualStatusCode);
            logger.info("POST Response Body: {}", actualResponse);

            // Update Excel with results
            updateExcelWithResults(sheetName, rowNumber, expectedResult, actualResponse,
                                 actualStatusCode, "POST", entityType);

            // Database validation
            if (actualStatusCode >= 200 && actualStatusCode < 300) {
                validateDatabaseRecord(response, entityType, "CREATE");
            }

            // Constraint testing
            testConstraints(url, requestBody, entityType, "POST");

        } catch (Exception e) {
            logger.error("Error in POST operation: " + e.getMessage());
            updateExcelWithError(sheetName, rowNumber, e.getMessage(), "POST");
        }
    }

    /**
     * Execute PUT operation (UPDATE)
     */
    protected void executePutOperation(String sheetName, int rowNumber, String entityType, String entityId) {
        logger.info("=== Executing PUT Operation for {} (Row: {}, ID: {}) ===", entityType, rowNumber, entityId);

        try {
            String excelPath = config.getExcelFilePath();

            // Read test data from Excel
            String url = excelUtils.getCellData(excelPath, sheetName, rowNumber, COL_URL);
            String requestBody = excelUtils.getCellData(excelPath, sheetName, rowNumber, COL_REQUEST_BODY);
            String expectedResult = excelUtils.getCellData(excelPath, sheetName, rowNumber, COL_EXPECTED);

            // Replace {id} placeholder with actual ID
            url = url.replace("{id}", entityId);

            logger.info("PUT URL: {}", url);
            logger.info("PUT Body: {}", requestBody);

            // Generate dynamic data using Faker if needed
            requestBody = generateDynamicData(requestBody, entityType);

            // Make PUT request
            RequestSpecification request = RestAssured.given()
                .contentType("application/json")
                .header("Authorization", authToken);

            if (requestBody != null && !requestBody.isEmpty()) {
                request.body(requestBody);
            }

            Response response = request.put(url);

            // Validate response
            int actualStatusCode = response.getStatusCode();
            String actualResponse = response.getBody().asString();

            logger.info("PUT Response Status: {}", actualStatusCode);
            logger.info("PUT Response Body: {}", actualResponse);

            // Update Excel with results
            updateExcelWithResults(sheetName, rowNumber, expectedResult, actualResponse,
                                 actualStatusCode, "PUT", entityType);

            // Database validation
            if (actualStatusCode >= 200 && actualStatusCode < 300) {
                validateDatabaseRecord(response, entityType, "UPDATE");
            }

        } catch (Exception e) {
            logger.error("Error in PUT operation: " + e.getMessage());
            updateExcelWithError(sheetName, rowNumber, e.getMessage(), "PUT");
        }
    }

    /**
     * Execute GET ALL operation (READ ALL)
     */
    protected void executeGetAllOperation(String sheetName, int rowNumber, String entityType) {
        logger.info("=== Executing GET ALL Operation for {} (Row: {}) ===", entityType, rowNumber);

        try {
            String excelPath = config.getExcelFilePath();

            // Read test data from Excel
            String url = excelUtils.getCellData(excelPath, sheetName, rowNumber, COL_URL);
            String expectedResult = excelUtils.getCellData(excelPath, sheetName, rowNumber, COL_EXPECTED);

            logger.info("GET ALL URL: {}", url);

            // Make GET request
            Response response = RestAssured.given()
                .contentType("application/json")
                .header("Authorization", authToken)
                .get(url);

            // Validate response
            int actualStatusCode = response.getStatusCode();
            String actualResponse = response.getBody().asString();

            logger.info("GET ALL Response Status: {}", actualStatusCode);
            logger.info("GET ALL Response Body: {}", actualResponse);

            // Update Excel with results
            updateExcelWithResults(sheetName, rowNumber, expectedResult, actualResponse,
                                 actualStatusCode, "GET_ALL", entityType);

            // Database validation - compare API response with database
            if (actualStatusCode >= 200 && actualStatusCode < 300) {
                validateGetAllWithDatabase(response, entityType);
            }

        } catch (Exception e) {
            logger.error("Error in GET ALL operation: " + e.getMessage());
            updateExcelWithError(sheetName, rowNumber, e.getMessage(), "GET_ALL");
        }
    }

    /**
     * Execute GET BY ID operation (READ BY ID)
     */
    protected void executeGetByIdOperation(String sheetName, int rowNumber, String entityType, String entityId) {
        logger.info("=== Executing GET BY ID Operation for {} (Row: {}, ID: {}) ===", entityType, rowNumber, entityId);

        try {
            String excelPath = config.getExcelFilePath();

            // Read test data from Excel
            String url = excelUtils.getCellData(excelPath, sheetName, rowNumber, COL_URL);
            String expectedResult = excelUtils.getCellData(excelPath, sheetName, rowNumber, COL_EXPECTED);

            // Replace {id} placeholder with actual ID
            url = url.replace("{id}", entityId);

            logger.info("GET BY ID URL: {}", url);

            // Make GET request
            Response response = RestAssured.given()
                .contentType("application/json")
                .header("Authorization", authToken)
                .get(url);

            // Validate response
            int actualStatusCode = response.getStatusCode();
            String actualResponse = response.getBody().asString();

            logger.info("GET BY ID Response Status: {}", actualStatusCode);
            logger.info("GET BY ID Response Body: {}", actualResponse);

            // Update Excel with results
            updateExcelWithResults(sheetName, rowNumber, expectedResult, actualResponse,
                                 actualStatusCode, "GET_BY_ID", entityType);

            // Database validation with foreign keys
            if (actualStatusCode >= 200 && actualStatusCode < 300) {
                validateGetByIdWithDatabase(response, entityType, entityId);
            }

        } catch (Exception e) {
            logger.error("Error in GET BY ID operation: " + e.getMessage());
            updateExcelWithError(sheetName, rowNumber, e.getMessage(), "GET_BY_ID");
        }
    }

    /**
     * Execute DELETE operation (DELETE)
     */
    protected void executeDeleteOperation(String sheetName, int rowNumber, String entityType, String entityId) {
        logger.info("=== Executing DELETE Operation for {} (Row: {}, ID: {}) ===", entityType, rowNumber, entityId);

        try {
            String excelPath = config.getExcelFilePath();

            // Read test data from Excel
            String url = excelUtils.getCellData(excelPath, sheetName, rowNumber, COL_URL);
            String expectedResult = excelUtils.getCellData(excelPath, sheetName, rowNumber, COL_EXPECTED);

            // Replace {id} placeholder with actual ID
            url = url.replace("{id}", entityId);

            logger.info("DELETE URL: {}", url);

            // Make DELETE request
            Response response = RestAssured.given()
                .contentType("application/json")
                .header("Authorization", authToken)
                .delete(url);

            // Validate response
            int actualStatusCode = response.getStatusCode();
            String actualResponse = response.getBody().asString();

            logger.info("DELETE Response Status: {}", actualStatusCode);
            logger.info("DELETE Response Body: {}", actualResponse);

            // Update Excel with results
            updateExcelWithResults(sheetName, rowNumber, expectedResult, actualResponse,
                                 actualStatusCode, "DELETE", entityType);

            // Database validation - verify deletion
            if (actualStatusCode >= 200 && actualStatusCode < 300) {
                validateDeletion(entityType, entityId);
            }

        } catch (Exception e) {
            logger.error("Error in DELETE operation: " + e.getMessage());
            updateExcelWithError(sheetName, rowNumber, e.getMessage(), "DELETE");
        }
    }

    /**
     * Execute FILTER operation (GET with filters)
     */
    protected void executeFilterOperation(String sheetName, int rowNumber, String entityType) {
        logger.info("=== Executing FILTER Operation for {} (Row: {}) ===", entityType, rowNumber);

        try {
            String excelPath = config.getExcelFilePath();

            // Read test data from Excel
            String url = excelUtils.getCellData(excelPath, sheetName, rowNumber, COL_URL);
            String expectedResult = excelUtils.getCellData(excelPath, sheetName, rowNumber, COL_EXPECTED);

            logger.info("FILTER URL: {}", url);

            // Make GET request with filters
            Response response = RestAssured.given()
                .contentType("application/json")
                .header("Authorization", authToken)
                .get(url);

            // Validate response
            int actualStatusCode = response.getStatusCode();
            String actualResponse = response.getBody().asString();

            logger.info("FILTER Response Status: {}", actualStatusCode);
            logger.info("FILTER Response Body: {}", actualResponse);

            // Update Excel with results
            updateExcelWithResults(sheetName, rowNumber, expectedResult, actualResponse,
                                 actualStatusCode, "FILTER", entityType);

            // Database validation - verify filter results
            if (actualStatusCode >= 200 && actualStatusCode < 300) {
                validateFilterWithDatabase(response, entityType, url);
            }

        } catch (Exception e) {
            logger.error("Error in FILTER operation: " + e.getMessage());
            updateExcelWithError(sheetName, rowNumber, e.getMessage(), "FILTER");
        }
    }

    /**
     * Generate dynamic data using Faker
     */
    protected String generateDynamicData(String requestBody, String entityType) {
        try {
            if (requestBody == null || requestBody.isEmpty()) {
                return requestBody;
            }

            // Replace placeholders with Faker data
            if (entityType.equalsIgnoreCase("photos")) {
                requestBody = requestBody.replace("{{title}}", faker.lorem().sentence(3))
                                       .replace("{{description}}", faker.lorem().paragraph())
                                       .replace("{{url}}", faker.internet().url())
                                       .replace("{{thumbnailUrl}}", faker.internet().url())
                                       .replace("{{albumId}}", String.valueOf(faker.number().numberBetween(1, 100)))
                                       .replace("{{userId}}", String.valueOf(faker.number().numberBetween(1, 10)));
            } else if (entityType.equalsIgnoreCase("products")) {
                requestBody = requestBody.replace("{{name}}", faker.commerce().productName())
                                       .replace("{{description}}", faker.lorem().paragraph())
                                       .replace("{{price}}", String.valueOf(faker.number().randomDouble(2, 10, 1000)))
                                       .replace("{{category}}", faker.commerce().department())
                                       .replace("{{brand}}", faker.company().name())
                                       .replace("{{sku}}", faker.code().ean13())
                                       .replace("{{stock}}", String.valueOf(faker.number().numberBetween(0, 100)));
            }

            // Replace common placeholders
            requestBody = requestBody.replace("{{timestamp}}", String.valueOf(System.currentTimeMillis()))
                                   .replace("{{random_number}}", String.valueOf(faker.number().numberBetween(1, 1000)))
                                   .replace("{{random_string}}", faker.lorem().word());

            return requestBody;

        } catch (Exception e) {
            logger.error("Error generating dynamic data: " + e.getMessage());
            return requestBody;
        }
    }

    /**
     * Update Excel with test results
     */
    protected void updateExcelWithResults(String sheetName, int rowNumber, String expectedResult,
                                        String actualResponse, int statusCode, String operation, String entityType) {
        try {
            String excelPath = config.getExcelFilePath();

            // Update actual result column
            excelUtils.setCellData(excelPath, sheetName, rowNumber, COL_ACTUAL, actualResponse);

            // Determine test status
            String status = "PASS";
            String defectId = "";

            // Check status code
            int expectedStatusCode = Integer.parseInt(expectedResult.trim());
            if (statusCode != expectedStatusCode) {
                status = "FAIL";
                defectId = generateDefectId(entityType, operation, "STATUS_CODE_MISMATCH");
            }

            // Update status column
            excelUtils.setCellData(excelPath, sheetName, rowNumber, COL_STATUS, status);

            // Update defect ID if failed
            if (!defectId.isEmpty()) {
                excelUtils.setCellData(excelPath, sheetName, rowNumber, COL_DEFECT_ID, defectId);
            }

            logger.info("Excel updated - Row: {}, Status: {}, DefectID: {}", rowNumber, status, defectId);

        } catch (Exception e) {
            logger.error("Error updating Excel: " + e.getMessage());
        }
    }

    /**
     * Update Excel with error information
     */
    protected void updateExcelWithError(String sheetName, int rowNumber, String errorMessage, String operation) {
        try {
            String excelPath = config.getExcelFilePath();

            // Update actual result with error
            excelUtils.setCellData(excelPath, sheetName, rowNumber, COL_ACTUAL, "ERROR: " + errorMessage);

            // Update status as FAIL
            excelUtils.setCellData(excelPath, sheetName, rowNumber, COL_STATUS, "FAIL");

            // Generate defect ID
            String defectId = generateDefectId("UNKNOWN", operation, "EXECUTION_ERROR");
            excelUtils.setCellData(excelPath, sheetName, rowNumber, COL_DEFECT_ID, defectId);

            logger.error("Excel updated with error - Row: {}, DefectID: {}", rowNumber, defectId);

        } catch (Exception e) {
            logger.error("Error updating Excel with error: " + e.getMessage());
        }
    }

    /**
     * Generate defect ID
     */
    protected String generateDefectId(String entityType, String operation, String issueType) {
        return String.format("D_%s_%s_%s_%d",
                           entityType.toUpperCase(),
                           operation.toUpperCase(),
                           issueType.toUpperCase(),
                           System.currentTimeMillis() % 1000);
    }
