package api.helpers;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.slf4j.Logger;
import utils.Constants;
import utils.ExcelUtils;

/**
 * Helper class for making POST requests with dynamic request bodies
 */
public class PostWithDynamicRequestBody {
    private Logger logger;
    private String filePath;
    private String sheetName;
    private int url;
    private int body;
    private ExcelUtils excelUtils;

    /**
     * Constructor
     * @param logger Logger instance
     * @param filePath Excel file path
     * @param sheetName Excel sheet name
     * @param url URL column index
     * @param body Request body column index
     */
    public PostWithDynamicRequestBody(Logger logger, String filePath, String sheetName, int url, int body) {
        this.logger = logger;
        this.filePath = filePath;
        this.sheetName = sheetName;
        this.url = url;
        this.body = body;
        this.excelUtils = new ExcelUtils();
    }

    /**
     * Make a POST request with a dynamic request body
     * @param rowNum Excel row number
     * @param requestBody Request body
     * @return Response from the API
     * @throws Exception If any error occurs during the process
     */
    public Response post(int rowNum, String requestBody) throws Exception {
        try {
            // Parse the request body
            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readTree(requestBody);

            // Extract the endpoint, type, and auth token
            String endpoint = root.has(Constants.FIELD_ENDPOINT) ? root.get(Constants.FIELD_ENDPOINT).asText() : "";
            String type = root.has(Constants.FIELD_TYPE) ? root.get(Constants.FIELD_TYPE).asText() : Constants.TYPE_POST;
            String auth = root.has(Constants.FIELD_AUTH) ? root.get(Constants.FIELD_AUTH).asText() : "";
            JsonNode payload = root.has(Constants.FIELD_PAYLOAD) ? root.get(Constants.FIELD_PAYLOAD) : null;

            // Get the base URL from Excel
            String baseUrl = excelUtils.getCellData(filePath, sheetName, rowNum, url);
            if (baseUrl == null || baseUrl.isEmpty()) {
                logger.error("Base URL is empty for row {}", rowNum);
                return null;
            }

            // Create the full URL
            // For all requests to /decrypt, use just the base URL
            // The endpoint is specified in the request body
            String fullUrl = baseUrl;
            logger.info("Using URL: {}", fullUrl);

            // Create the request specification
            RequestSpecification requestSpec = RestAssured.given()
                    .contentType(ContentType.JSON);

            // Add authorization header if available
            if (auth != null && !auth.isEmpty()) {
                requestSpec.header("Authorization", "Bearer " + auth);
            }

            // For requests to /decrypt, use the entire request body
            if (baseUrl.contains("/decrypt")) {
                requestSpec.body(requestBody);
                logger.info("Using entire request body for /decrypt endpoint: {}", requestBody);
            }
            // For other endpoints, add payload if available
            else if (payload != null && !payload.isNull()) {
                requestSpec.body(payload.toString());
            }

            // Make the request based on the type
            Response response;

            // For requests to /decrypt, always use POST method
            // The operation type is specified in the request body
            if (baseUrl.contains("/decrypt")) {
                logger.info("Making POST request to {} with {} operation type in the request body", fullUrl, type.toUpperCase());
                response = requestSpec.post(fullUrl);
            } else {
                // For regular endpoints, use the appropriate HTTP method
                switch (type.toLowerCase()) {
                    case Constants.TYPE_GET:
                        logger.info("Making GET request to {}", fullUrl);
                        response = requestSpec.get(fullUrl);
                        break;
                    case Constants.TYPE_PUT:
                        logger.info("Making PUT request to {}", fullUrl);
                        response = requestSpec.put(fullUrl);
                        break;
                    case Constants.TYPE_PATCH:
                        logger.info("Making PATCH request to {}", fullUrl);
                        response = requestSpec.patch(fullUrl);
                        break;
                    case Constants.TYPE_DELETE:
                        logger.info("Making DELETE request to {}", fullUrl);
                        response = requestSpec.delete(fullUrl);
                        break;
                    default:
                        logger.info("Making POST request to {}", fullUrl);
                        response = requestSpec.post(fullUrl);
                        break;
                }
            }

            // Log the response
            int statusCode = response.getStatusCode();
            String responseBody = response.getBody().asPrettyString();
            logger.info("Response status code: {}", statusCode);
            logger.info("Response body: {}", responseBody);

            return response;
        } catch (Exception e) {
            logger.error("Error making request: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Make a PUT request with a dynamic request body
     * @param rowNum Excel row number
     * @param requestBody Request body
     * @return Response from the API
     * @throws Exception If any error occurs during the process
     */
    public Response put(int rowNum, String requestBody) throws Exception {
        // Get the URL from Excel
        String uri = excelUtils.getCellData(filePath, sheetName, rowNum, url);
        logger.info("Using URI from Excel: {}", uri);

        // Create the request specification
        RequestSpecification request = RestAssured.given();
        request.header("Content-Type", "application/json");
        request.header("Accept", "*/*");
        request.body(requestBody);

        // Log the request details
        logger.info("Making PUT request to: {}", uri);
        logger.info("Request body: {}", requestBody);

        // Make the request
        Response response = request.put(uri);

        // Log the response details
        logger.info("Response status code: {}", response.getStatusCode());
        logger.info("Response body: {}", response.getBody().asPrettyString());

        return response;
    }
}
