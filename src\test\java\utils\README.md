# Utils Package

## Overview
The Utils package contains utility classes that provide common functionality used throughout the testing framework. These utilities handle database operations, Excel file management, and other helper functions.

## Components

### DatabaseUtils.java
This class provides database connectivity and operations. It includes:

- **Database Connection**: Methods for connecting to the database
- **Query Execution**: Methods for executing SQL queries
- **Result Processing**: Methods for processing query results into usable formats
- **Foreign Key Resolution**: Methods for resolving foreign key relationships

#### Key Methods
- `fetchDataFromDatabase(String query)`: Executes a query and returns results
- `fetchRelatedRecords(String table, String field, String value)`: Fetches records related by foreign key
- `executeUpdate(String query)`: Executes update/insert/delete operations
- `closeConnection()`: Closes the database connection

### ExcelUtils.java
This class handles Excel file operations for test data management. It includes:

- **File Operations**: Methods for reading from and writing to Excel files
- **Cell Operations**: Methods for getting and setting cell values
- **Sheet Operations**: Methods for working with Excel sheets

#### Key Methods
- `getCellData(String filePath, String sheetName, int rowNum, int colNum)`: Gets data from a specific cell
- `setCellData(String filePath, String sheetName, int rowNum, int colNum, String data)`: Sets data in a specific cell
- `getRowCount(String filePath, String sheetName)`: Gets the number of rows in a sheet
- `getColumnCount(String filePath, String sheetName, int rowNum)`: Gets the number of columns in a row

### PostWithDynamicRequestBody.java
This class handles dynamic request body generation and API calls. It includes:

- **Request Building**: Methods for building API requests with dynamic content
- **Response Handling**: Methods for processing API responses

#### Key Methods
- `post(int rowNum, String requestBody)`: Makes a POST request with the given body
- `get(int rowNum, String endpoint)`: Makes a GET request to the given endpoint
- `put(int rowNum, String requestBody)`: Makes a PUT request with the given body
- `delete(int rowNum, String endpoint)`: Makes a DELETE request to the given endpoint

## Usage

### Database Operations
```java
DatabaseUtils dbUtils = new DatabaseUtils();
List<String> results = dbUtils.fetchDataFromDatabase("SELECT * FROM users WHERE id = '123'");
```

### Excel Operations
```java
ExcelUtils excelUtils = new ExcelUtils();
String data = excelUtils.getCellData("TestData.xlsx", "Sheet1", 1, 2);
excelUtils.setCellData("TestData.xlsx", "Sheet1", 1, 3, "Test Result");
```

### API Requests
```java
PostWithDynamicRequestBody requestHandler = new PostWithDynamicRequestBody(logger, filePath, sheetName, url, body);
Response response = requestHandler.post(rowNum, requestBody);
```

## Best Practices
- **Connection Management**: Always close database connections after use
- **Error Handling**: Implement proper error handling for database and file operations
- **Performance**: Optimize database queries for performance
- **Security**: Never hardcode sensitive information like database credentials
- **Reusability**: Design utility methods to be reusable across different test cases
