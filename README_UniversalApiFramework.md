# Universal API Testing Framework

## 🎯 Overview
A comprehensive, dynamic API testing framework that works with **any application** and **any request body structure**. Supports all HTTP methods, automatic payload generation, PUT workflow enhancement, and integrated defect tracking.

## ✅ Key Features

### 1. **Universal Application Support**
- Works with **any application API** (Contact, Core, Finance, etc.)
- Handles **any request body structure** dynamically
- No hardcoded API endpoints or structures

### 2. **Complete HTTP Method Support**
- **POST**: Dynamic payload generation with {{faker}} placeholders
- **PUT**: Automatic getAll → extract 0th index → update Excel → PUT workflow
- **GET**: Simple endpoint execution with query parameters
- **DELETE**: Clean endpoint execution
- **PATCH**: Partial update with dynamic data

### 3. **Smart Payload Handling**
- **{{faker}}** → Replaced with dynamic data based on field constraints
- **{{foreign_key}}** → Preserved for dependency resolution
- **Static values** → Remain exactly as specified in Excel
- **null payload** → For GET/DELETE operations

### 4. **PUT API Enhancement**
- Automatically hits `getAll` endpoint first
- Extracts 0th index payload from response
- Updates Excel sheet with existing data
- Merges with {{faker}} updates for final PUT request

### 5. **Integrated Defect Tracking**
- Automatic defect creation on test failures
- Supports multiple bug tracking systems:
  - **Jira** (Atlassian)
  - **Azure DevOps** (Microsoft)
  - **Plane** (Bug tracking application)
  - **Bugzilla**
  - **Mock** (for testing)

### 6. **Table-Specific Constraints**
- `countryShortName` → length=3 for Country Master table
- `stateShortName` → length=3 for State Master table
- Configurable field constraints from properties file

## 📊 Request Body Examples

### Contact API Example (Your Use Case):
```json
{
    "endpoint": "/contact/api/CompanyKeyPersonnelDetails/save",
    "payload": {
        "companyId": {
            "companyName": "{{faker}}",
            "phoneNo": "{{faker}}",
            "companyEmail": "{{faker}}",
            "websiteUrl": "http://www.example.com",
            "id": 14
        },
        "keyPersonnelName": "{{faker}}",
        "keyPersonnelTitle": "{{faker}}",
        "contactNo": "{{faker}}",
        "emailId": "{{faker}}",
        "firstName": "{{faker}}",
        "lastName": "{{faker}}"
    },
    "type": "post",
    "tenantId": "{{tenantId}}",
    "auth": "{{auth}}"
}
```

### Core API Example:
```json
{
    "endpoint": "/core/api/CountryMaster/save",
    "payload": {
        "countryShortName": "{{faker}}",
        "countryFullDesc": "{{faker}}",
        "active": true,
        "priority": 1
    },
    "type": "post",
    "tenantId": "{{tenantId}}",
    "auth": "{{auth}}"
}
```

### PUT API Example:
```json
{
    "endpoint": "/contact/api/CompanyKeyPersonnelDetails/update",
    "payload": {
        "id": "{{foreign_key}}",
        "keyPersonnelName": "{{faker}}",
        "contactNo": "{{faker}}"
    },
    "type": "put",
    "tenantId": "{{tenantId}}",
    "auth": "{{auth}}"
}
```

## 🔧 Configuration

### Field Constraints (field-constraints.properties):
```properties
# Country Master Constraints
countryShortName.length=3
countryShortName.pattern=[A-Z]{3}

# Contact Fields
companyName.minLength=2
companyName.maxLength=50
phoneNo.length=10
phoneNo.pattern=[0-9]{10}
emailId.type=EMAIL
```

### Defect Tracking (defect-config.properties):
```properties
# Choose: jira, azure, plane, bugzilla, mock
bug.tracking.system=plane

# Plane Configuration
plane.url=https://your-plane-instance.com
plane.workspace.id=your-workspace-id
plane.project.id=your-project-id
plane.api.key=your-plane-api-key
```

## 🚀 Usage

### 1. Excel Setup
Put your API structure in Excel body column:
```json
{
    "endpoint": "/your-app/api/YourEntity/save",
    "payload": {
        "field1": "{{faker}}",
        "field2": "static value",
        "field3": "{{foreign_key}}",
        "field4": true
    },
    "type": "post",
    "tenantId": "{{tenantId}}",
    "auth": "{{auth}}"
}
```

### 2. Java Code
```java
UniversalTestRunner testRunner = new UniversalTestRunner();

// Execute any API test
testRunner.executeApiTest(
    filePath, sheetName, rowNumber,
    baseUrl, authToken,
    urlCol, bodyCol, statusCol, actualResultCol, expectedResultCol
);
```

### 3. Test Execution
```bash
mvn test -Dtest=CoreService#TC_UniversalApiFrameworkDemo
```

## 🔄 PUT API Workflow

1. **Original PUT Request** in Excel:
   ```json
   {
     "endpoint": "/api/Entity/update",
     "payload": {"name": "{{faker}}"},
     "type": "put"
   }
   ```

2. **Framework automatically**:
   - Hits `/api/Entity/getAll`
   - Extracts 0th index payload
   - Updates Excel with existing data

3. **Updated Excel Request**:
   ```json
   {
     "endpoint": "/api/Entity/update",
     "payload": {
       "id": 123,
       "name": "Generated Name",
       "existingField": "existing value"
     },
     "type": "put"
   }
   ```

4. **Executes PUT** with complete payload

## 🐛 Defect Tracking Integration

### When Test Fails:
1. **Automatic defect creation** in configured bug tracking system
2. **Defect includes**:
   - Test case name
   - API endpoint
   - Expected vs Actual results
   - Error details
   - Environment information

### Example Defect:
```
Title: API Test Failure: TC_CompanyKeyPersonnelDetails_Save
Description: 
**API Test Case Failed**

**Endpoint:** /contact/api/CompanyKeyPersonnelDetails/save
**Expected Result:** Success
**Actual Result:** Status: 400 - Validation Error
**Error Details:** companyName is required

**Environment:** Test
**Generated by:** Automated API Testing Framework
```

## ✅ Benefits

1. **Universal**: Works with any application API structure
2. **Dynamic**: {{faker}} placeholders for dynamic data generation
3. **Smart**: Table-specific constraints (length=3 for country codes)
4. **Complete**: All HTTP methods supported
5. **Enhanced**: PUT workflow with automatic data retrieval
6. **Integrated**: Automatic defect tracking on failures
7. **Configurable**: Field constraints and bug tracking systems
8. **Maintainable**: No hardcoded values, everything configurable

## 🎯 Test Results

### Structure Preservation:
- ✅ Only {{faker}} values replaced with dynamic data
- ✅ Static values remain unchanged
- ✅ Foreign keys preserved for dependency resolution
- ✅ Exact JSON structure from Excel maintained

### Constraint Validation:
- ✅ countryShortName: length=3 (e.g., "USA", "IND")
- ✅ phoneNo: 10-digit pattern (e.g., "9876543210")
- ✅ emailId: valid format (e.g., "<EMAIL>")

### Defect Integration:
- ✅ Automatic defect creation on test failures
- ✅ Configurable bug tracking systems
- ✅ Detailed defect information with API context

**Your Universal API Testing Framework is ready for any application! 🎉**
