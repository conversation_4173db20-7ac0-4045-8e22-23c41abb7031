# Defect Tracking Configuration
# Choose your bug tracking system: jira, azure, bugzilla, plane, mock
bug.tracking.system=plane

# Jira Configuration
jira.url=https://your-company.atlassian.net
jira.project.key=API
jira.auth.token=your-jira-basic-auth-token
jira.username=your-username
jira.password=your-password

# Azure DevOps Configuration
azure.url=https://dev.azure.com/your-organization
azure.project=YourProject
azure.auth.token=your-azure-personal-access-token

# Bugzilla Configuration
bugzilla.url=https://your-bugzilla-instance.com
bugzilla.product=YourProduct
bugzilla.api.key=your-bugzilla-api-key

# Plane Configuration (Bug Tracking Application)
# TODO: Replace with your actual Plane credentials
plane.url=https://your-plane-instance.com
plane.workspace.id=your-workspace-id
plane.project.id=your-project-id
plane.api.key=your-plane-api-key

# Example Real Configuration (Replace with your values):
# plane.url=https://mycompany.plane.so
# plane.workspace.id=ws_abc123def456
# plane.project.id=proj_xyz789abc123
# plane.api.key=plane_api_key_abc123xyz789

# How to get Plane credentials:
# 1. Plane URL: Your Plane instance URL
# 2. Workspace ID: Plane → Workspace Settings → Copy ID
# 3. Project ID: Plane → Project Settings → Copy ID
# 4. API Key: Plane → User Settings → API Tokens → Generate New Token

# Default Defect Settings
defect.default.severity=Medium
defect.default.priority=High
defect.default.component=API Testing
defect.default.environment=Test
defect.default.reporter=Automated Testing Framework

# Defect Auto-Assignment
defect.auto.assign=true
defect.default.assignee=<EMAIL>

# Notification Settings
defect.notify.on.creation=true
defect.notify.email=<EMAIL>
defect.notify.slack.webhook=https://hooks.slack.com/your-webhook-url

# Integration Settings
defect.include.screenshots=false
defect.include.logs=true
defect.include.request.response=true
