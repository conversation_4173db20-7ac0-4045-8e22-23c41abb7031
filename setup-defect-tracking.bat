@echo off
echo ========================================
echo   Defect Tracking Configuration Setup
echo ========================================
echo.

echo This script will help you configure defect tracking credentials.
echo.

echo Current configuration file: src\test\resources\defect-config.properties
echo.

echo Please provide your Plane bug tracking credentials:
echo.

set /p PLANE_URL="Enter your Plane URL (e.g., https://mycompany.plane.so): "
set /p WORKSPACE_ID="Enter your Workspace ID (e.g., ws_abc123def456): "
set /p PROJECT_ID="Enter your Project ID (e.g., proj_xyz789abc123): "
set /p API_KEY="Enter your API Key (e.g., plane_api_key_abc123xyz789): "

echo.
echo ========================================
echo   Updating Configuration File
echo ========================================

:: Create backup
copy "src\test\resources\defect-config.properties" "src\test\resources\defect-config.properties.backup"

:: Update the configuration file
powershell -Command "(Get-Content 'src\test\resources\defect-config.properties') -replace 'plane.url=https://your-plane-instance.com', 'plane.url=%PLANE_URL%' | Set-Content 'src\test\resources\defect-config.properties'"
powershell -Command "(Get-Content 'src\test\resources\defect-config.properties') -replace 'plane.workspace.id=your-workspace-id', 'plane.workspace.id=%WORKSPACE_ID%' | Set-Content 'src\test\resources\defect-config.properties'"
powershell -Command "(Get-Content 'src\test\resources\defect-config.properties') -replace 'plane.project.id=your-project-id', 'plane.project.id=%PROJECT_ID%' | Set-Content 'src\test\resources\defect-config.properties'"
powershell -Command "(Get-Content 'src\test\resources\defect-config.properties') -replace 'plane.api.key=your-plane-api-key', 'plane.api.key=%API_KEY%' | Set-Content 'src\test\resources\defect-config.properties'"

echo.
echo ✅ Configuration updated successfully!
echo.
echo ========================================
echo   Testing Configuration
echo ========================================
echo.

echo Running test to verify defect tracking setup...
mvn test -Dtest=ComprehensiveApiTestSuite#testPostOperationDemo -q

echo.
echo ========================================
echo   Setup Complete
echo ========================================
echo.
echo Your defect tracking is now configured!
echo.
echo Configuration file: src\test\resources\defect-config.properties
echo Backup file: src\test\resources\defect-config.properties.backup
echo.
echo To test defect creation, run:
echo mvn test -Dtest=ComprehensiveApiTestSuite
echo.
echo Check your Plane project for automatically created defects.
echo.
pause
