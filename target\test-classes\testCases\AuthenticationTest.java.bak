package testCases;

// BasicTestCase1 removed - using direct implementation
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.annotations.Test;
import utils.TestConfiguration;

/**
 * Authentication Test to verify token retrieval works correctly
 */
public class AuthenticationTest {
    private static final Logger logger = LoggerFactory.getLogger(AuthenticationTest.class);

    @Test
    public void testAuthenticationForPhotos() {
        logger.info("=== Testing Authentication for Photos API ===");

        try {
            // Configuration
            TestConfiguration config = TestConfiguration.getInstance();
            String filePath = config.getExcelFilePath();
            String sheetName = "Photos";
            int urlCol = config.getUrlColumn();
            int bodyCol = config.getBodyColumn();
            int statusCol = config.getStatusColumn();
            int actualResultCol = config.getActualResultColumn();
            int expectedResultCol = config.getExpectedResultColumn();
            int tableNameCol = 2;

            // Create BasicTestCase1 instance
            BasicTestCase1 bt = new BasicTestCase1(
                logger, filePath, sheetName, urlCol, bodyCol, statusCol,
                actualResultCol, expectedResultCol, tableNameCol
            );

            // Test authentication
            String authToken = bt.signIn(13); // Auth row number

            if (authToken != null && !authToken.isEmpty()) {
                logger.info("✅ Authentication successful for Photos API");
                logger.info("Token: {}", authToken);
            } else {
                logger.error("❌ Authentication failed for Photos API");
            }

        } catch (Exception e) {
            logger.error("Error testing authentication for Photos API: " + e.getMessage());
        }
    }

    @Test
    public void testAuthenticationForProducts() {
        logger.info("=== Testing Authentication for Products API ===");

        try {
            // Configuration
            TestConfiguration config = TestConfiguration.getInstance();
            String filePath = config.getExcelFilePath();
            String sheetName = "Products";
            int urlCol = config.getUrlColumn();
            int bodyCol = config.getBodyColumn();
            int statusCol = config.getStatusColumn();
            int actualResultCol = config.getActualResultColumn();
            int expectedResultCol = config.getExpectedResultColumn();
            int tableNameCol = 2;

            // Create BasicTestCase1 instance
            BasicTestCase1 bt = new BasicTestCase1(
                logger, filePath, sheetName, urlCol, bodyCol, statusCol,
                actualResultCol, expectedResultCol, tableNameCol
            );

            // Test authentication
            String authToken = bt.signIn(13); // Auth row number

            if (authToken != null && !authToken.isEmpty()) {
                logger.info("✅ Authentication successful for Products API");
                logger.info("Token: {}", authToken);
            } else {
                logger.error("❌ Authentication failed for Products API");
            }

        } catch (Exception e) {
            logger.error("Error testing authentication for Products API: " + e.getMessage());
        }
    }

    @Test
    public void testPhotosApiTestClassAuthentication() {
        logger.info("=== Testing PhotosApiTest Class Authentication ===");

        try {
            PhotosApiTest photosTest = new PhotosApiTest();
            photosTest.setup();

            logger.info("✅ PhotosApiTest setup completed successfully");

        } catch (Exception e) {
            logger.error("❌ Error in PhotosApiTest setup: " + e.getMessage());
        }
    }

    @Test
    public void testProductsApiTestClassAuthentication() {
        logger.info("=== Testing ProductsApiTest Class Authentication ===");

        try {
            ProductsApiTest productsTest = new ProductsApiTest();
            productsTest.setup();

            logger.info("✅ ProductsApiTest setup completed successfully");

        } catch (Exception e) {
            logger.error("❌ Error in ProductsApiTest setup: " + e.getMessage());
        }
    }
}
