package testNG;

import com.aventstack.extentreports.ExtentReports;
import com.aventstack.extentreports.ExtentTest;
import com.aventstack.extentreports.Status;
import com.aventstack.extentreports.reporter.ExtentHtmlReporter;
import org.testng.ITestContext;
import org.testng.ITestResult;
import org.testng.TestListenerAdapter;

/**
 * Enhanced Report Generator for CRUD Operations Test Framework
 * Generates comprehensive HTML reports with test results, defect tracking, and database validation details
 */
public class ReportGenerator extends TestListenerAdapter {
    private ExtentHtmlReporter htmlReporter;
    private ExtentReports extentReports;
    private ExtentTest extentTest;

    public ReportGenerator() {
        String reportPath = System.getProperty("user.dir") + "/test-reports/crud-operations-report.html";
        htmlReporter = new ExtentHtmlReporter(reportPath);
        extentReports = new ExtentReports();
        extentReports.attachReporter(htmlReporter);

        // Configure report settings
        htmlReporter.config().setDocumentTitle("CRUD Operations Test Report");
        htmlReporter.config().setReportName("Photos & Products API Testing");
        htmlReporter.config().setTheme(com.aventstack.extentreports.reporter.configuration.Theme.STANDARD);

        // Add system information
        extentReports.setSystemInfo("Framework", "CRUD Operations API Testing");
        extentReports.setSystemInfo("Environment", System.getProperty("environment", "Test"));
        extentReports.setSystemInfo("User", System.getProperty("user.name"));
        extentReports.setSystemInfo("Java Version", System.getProperty("java.version"));
        extentReports.setSystemInfo("OS", System.getProperty("os.name"));
    }

    /**
     * Custom constructor for specific report names
     */
    public ReportGenerator(String reportName) {
        String reportPath = System.getProperty("user.dir") + "/test-reports/" + reportName + ".html";
        htmlReporter = new ExtentHtmlReporter(reportPath);
        extentReports = new ExtentReports();
        extentReports.attachReporter(htmlReporter);

        // Configure report settings
        htmlReporter.config().setDocumentTitle(reportName + " Test Report");
        htmlReporter.config().setReportName(reportName + " API Testing");
        htmlReporter.config().setTheme(com.aventstack.extentreports.reporter.configuration.Theme.STANDARD);
    }

    @Override
    public void onStart(ITestContext testContext) {
        extentTest = extentReports.createTest(testContext.getName());
        extentTest.info("Test Suite Started: " + testContext.getName());
        extentTest.info("Total Tests: " + testContext.getAllTestMethods().length);
    }

    @Override
    public void onTestStart(ITestResult result) {
        String testName = result.getMethod().getMethodName();
        String className = result.getTestClass().getName();

        extentTest = extentReports.createTest(testName);
        extentTest.info("Test Class: " + className);
        extentTest.info("Test Method: " + testName);
        extentTest.info("Test Started at: " + new java.util.Date());
    }

    @Override
    public void onTestSuccess(ITestResult result) {
        String testName = result.getMethod().getMethodName();
        extentTest.log(Status.PASS, "✅ Test Passed: " + testName);
        extentTest.info("Execution Time: " + (result.getEndMillis() - result.getStartMillis()) + " ms");

        // Add CRUD operation specific information
        if (testName.contains("Create") || testName.contains("POST")) {
            extentTest.info("🔄 CRUD Operation: CREATE (POST)");
        } else if (testName.contains("Update") || testName.contains("PUT")) {
            extentTest.info("🔄 CRUD Operation: UPDATE (PUT)");
        } else if (testName.contains("GetAll") || testName.contains("GET")) {
            extentTest.info("🔄 CRUD Operation: READ (GET)");
        } else if (testName.contains("Delete") || testName.contains("DELETE")) {
            extentTest.info("🔄 CRUD Operation: DELETE");
        }
    }

    @Override
    public void onTestFailure(ITestResult result) {
        String testName = result.getMethod().getMethodName();
        Throwable throwable = result.getThrowable();

        extentTest.log(Status.FAIL, "❌ Test Failed: " + testName);
        extentTest.info("Execution Time: " + (result.getEndMillis() - result.getStartMillis()) + " ms");

        if (throwable != null) {
            extentTest.fail("Error Message: " + throwable.getMessage());
            extentTest.fail("Stack Trace: " + java.util.Arrays.toString(throwable.getStackTrace()));
        }

        // Add defect tracking information
        extentTest.warning("🐛 Defect ID will be generated for this failure");
        extentTest.warning("📊 Check Excel sheet for defect tracking details");

        // Add database validation failure info if applicable
        if (testName.contains("Database") || testName.contains("Validation")) {
            extentTest.fail("🗄️ Database validation failed - check database connectivity and data consistency");
        }
    }

    @Override
    public void onTestSkipped(ITestResult result) {
        String testName = result.getMethod().getMethodName();
        extentTest.log(Status.SKIP, "⏭️ Test Skipped: " + testName);

        if (result.getThrowable() != null) {
            extentTest.skip("Skip Reason: " + result.getThrowable().getMessage());
        }
    }

    @Override
    public void onFinish(ITestContext testContext) {
        extentTest.info("Test Suite Completed: " + testContext.getName());
        extentTest.info("Total Tests: " + testContext.getAllTestMethods().length);
        extentTest.info("Passed: " + testContext.getPassedTests().size());
        extentTest.info("Failed: " + testContext.getFailedTests().size());
        extentTest.info("Skipped: " + testContext.getSkippedTests().size());

        // Add summary information
        if (testContext.getFailedTests().size() > 0) {
            extentTest.warning("⚠️ Some tests failed - check defect IDs in Excel sheets");
        } else {
            extentTest.pass("🎉 All tests passed successfully!");
        }

        extentReports.flush();
    }

    /**
     * Add custom log entry to current test
     */
    public void logInfo(String message) {
        if (extentTest != null) {
            extentTest.info(message);
        }
    }

    /**
     * Add custom warning to current test
     */
    public void logWarning(String message) {
        if (extentTest != null) {
            extentTest.warning(message);
        }
    }

    /**
     * Add custom error to current test
     */
    public void logError(String message) {
        if (extentTest != null) {
            extentTest.fail(message);
        }
    }

    /**
     * Add database validation result
     */
    public void logDatabaseValidation(String tableName, boolean isValid, String details) {
        if (extentTest != null) {
            if (isValid) {
                extentTest.pass("🗄️ Database Validation Passed for " + tableName);
            } else {
                extentTest.fail("🗄️ Database Validation Failed for " + tableName);
            }
            extentTest.info("Details: " + details);
        }
    }

    /**
     * Add defect tracking information
     */
    public void logDefectGenerated(String defectId, String testCase, String errorMessage) {
        if (extentTest != null) {
            extentTest.warning("🐛 Defect Generated: " + defectId);
            extentTest.warning("Test Case: " + testCase);
            extentTest.warning("Error: " + errorMessage);
        }
    }

    /**
     * Add API response validation result
     */
    public void logApiValidation(String operation, int expectedStatus, int actualStatus, boolean isValid) {
        if (extentTest != null) {
            if (isValid) {
                extentTest.pass("✅ API Validation Passed for " + operation);
            } else {
                extentTest.fail("❌ API Validation Failed for " + operation);
            }
            extentTest.info("Expected Status: " + expectedStatus + ", Actual Status: " + actualStatus);
        }
    }

    /**
     * Finalize and flush the report
     */
    public void finalizeReport() {
        if (extentReports != null) {
            extentReports.flush();
        }
    }
}