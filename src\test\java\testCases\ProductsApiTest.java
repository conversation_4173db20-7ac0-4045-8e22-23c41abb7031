package testCases;

// BasicTestCase1 removed - using direct implementation
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;
import utils.*;
import io.restassured.response.Response;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.javafaker.Faker;
import java.util.HashMap;
import java.util.Map;
import java.math.BigDecimal;

/**
 * CRUD Operations Test Class for Products Table
 * Handles POST, PUT, GET, GET ALL, and DELETE operations with database validation
 */
public class ProductsApiTest {
    private static final Logger logger = LoggerFactory.getLogger(ProductsApiTest.class);

    // Configuration
    private final TestConfiguration config = TestConfiguration.getInstance();
    private String filePath;
    private String baseUrl;
    private String authToken;
    private String sheetName = "Products";

    // Column mappings
    private int urlCol;
    private int bodyCol;
    private int expectedResultCol;
    private int actualResultCol;
    private int statusCol;
    private int defectIdCol;
    private int tableNameCol;

    // Utilities
    private ExcelUtils excelUtils;
    private DatabaseValidationUtils dbUtils;
    private DynamicDataGenerator dataGenerator;
    private DefectTracker defectTracker;
    private Faker faker;
    private ObjectMapper objectMapper;

    // Test data storage
    private String lastCreatedId;

    @BeforeClass
    public void setup() {
        logger.info("=== Products API Test Setup ===");

        // Load configuration
        loadConfiguration();

        // Initialize utilities
        initializeUtilities();

        // Get authentication token
        authToken = getAuthToken();
        logger.info("Authentication token obtained for Products API testing");
    }

    /**
     * Load configuration from properties
     */
    private void loadConfiguration() {
        logger.info("Loading configuration for Products API...");

        filePath = config.getExcelFilePath();
        baseUrl = config.getBaseUrl();

        // Column mappings
        urlCol = config.getUrlColumn();
        bodyCol = config.getBodyColumn();
        expectedResultCol = config.getExpectedResultColumn();
        actualResultCol = config.getActualResultColumn();
        statusCol = config.getStatusColumn();
        defectIdCol = config.getDefectIdColumn();
        tableNameCol = 2; // Table name column

        logger.info("Products API configuration loaded successfully");
    }

    /**
     * Initialize utility classes
     */
    private void initializeUtilities() {
        excelUtils = new ExcelUtils();
        dbUtils = new DatabaseValidationUtils();
        dataGenerator = new DynamicDataGenerator();
        defectTracker = new DefectTracker();
        faker = new Faker();
        objectMapper = new ObjectMapper();
    }

    /**
     * Get authentication token
     */
    private String getAuthToken() {
        try {
            BasicTestCase1 bt = new BasicTestCase1(
                logger, filePath, sheetName, urlCol, bodyCol, statusCol,
                actualResultCol, expectedResultCol, tableNameCol
            );
            return bt.signIn(13); // Auth row number
        } catch (Exception e) {
            logger.error("Failed to get auth token: " + e.getMessage());
            return null;
        }
    }

    /**
     * POST API Test - Create Product
     * Tests successful creation, status code validation, null constraints, unique constraints
     */
    @Test(priority = 1)
    public void testCreateProduct() {
        logger.info("=== Testing Product Creation (POST) ===");

        int testRow = 14; // Starting row for POST tests

        try {
            // Test 1: Successful creation with status code validation
            testSuccessfulProductCreation(testRow);

            // Test 2: Null constraint validation
            testProductNullConstraints(testRow + 1);

            // Test 3: Unique constraint validation
            testProductUniqueConstraints(testRow + 2);

        } catch (Exception e) {
            logger.error("Error in testCreateProduct: " + e.getMessage());
            handleTestFailure(testRow, "POST Test Failed", e.getMessage());
        }
    }

    /**
     * Test successful product creation with status code and response validation
     */
    private void testSuccessfulProductCreation(int rowNum) throws Exception {
        logger.info("Testing successful product creation at row {}", rowNum);

        // Generate dynamic test data using Faker
        String requestBody = generateProductRequestBody();

        // Update Excel with generated request body
        excelUtils.setCellData(filePath, sheetName, rowNum, bodyCol, requestBody);

        // Make POST request
        Response postResponse = makePostRequest(rowNum, requestBody);

        if (postResponse != null) {
            // Validate status code
            int actualStatusCode = postResponse.getStatusCode();
            String expectedStatusCode = excelUtils.getCellData(filePath, sheetName, rowNum, expectedResultCol);

            if (String.valueOf(actualStatusCode).equals(expectedStatusCode.trim())) {
                // Extract ID from response
                lastCreatedId = extractIdFromResponse(postResponse);

                if (lastCreatedId != null) {
                    // Hit GetById API to validate creation
                    validateCreationWithGetById(rowNum, lastCreatedId);

                    // Update Excel with success
                    updateExcelWithSuccess(rowNum, postResponse.asString(), "Product created successfully");
                } else {
                    updateExcelWithFailure(rowNum, "Failed to extract ID from response", postResponse.asString());
                }
            } else {
                updateExcelWithFailure(rowNum,
                    "Status code mismatch. Expected: " + expectedStatusCode + ", Actual: " + actualStatusCode,
                    postResponse.asString());
            }
        }
    }

    /**
     * Test null constraint validation for product creation
     */
    private void testProductNullConstraints(int rowNum) throws Exception {
        logger.info("Testing product null constraints at row {}", rowNum);

        // Generate request body with null required field
        String requestBodyWithNull = generateProductRequestBodyWithNullField();

        // Update Excel with request body
        excelUtils.setCellData(filePath, sheetName, rowNum, bodyCol, requestBodyWithNull);

        // Make POST request
        Response response = makePostRequest(rowNum, requestBodyWithNull);

        if (response != null) {
            // Get expected error message from Excel
            String expectedError = excelUtils.getCellData(filePath, sheetName, rowNum, expectedResultCol);
            String actualResponse = response.asString();

            // Check if actual response contains expected error message
            if (actualResponse.toLowerCase().contains(expectedError.toLowerCase()) ||
                response.getStatusCode() >= 400) {
                updateExcelWithSuccess(rowNum, actualResponse, "Null constraint validation passed");
            } else {
                updateExcelWithFailure(rowNum, "Null constraint validation failed", actualResponse);
            }
        }
    }

    /**
     * Test unique constraint validation for product creation
     */
    private void testProductUniqueConstraints(int rowNum) throws Exception {
        logger.info("Testing product unique constraints at row {}", rowNum);

        // Use the same data as successful creation to trigger unique constraint
        if (lastCreatedId != null) {
            String duplicateRequestBody = generateDuplicateProductRequestBody();

            // Update Excel with request body
            excelUtils.setCellData(filePath, sheetName, rowNum, bodyCol, duplicateRequestBody);

            // Make POST request
            Response response = makePostRequest(rowNum, duplicateRequestBody);

            if (response != null) {
                // Get expected error message from Excel
                String expectedError = excelUtils.getCellData(filePath, sheetName, rowNum, expectedResultCol);
                String actualResponse = response.asString();

                // Check if actual response contains expected error message
                if (actualResponse.toLowerCase().contains(expectedError.toLowerCase()) ||
                    response.getStatusCode() >= 400) {
                    updateExcelWithSuccess(rowNum, actualResponse, "Unique constraint validation passed");
                } else {
                    updateExcelWithFailure(rowNum, "Unique constraint validation failed", actualResponse);
                }
            }
        }
    }

    /**
     * PUT API Test - Update Product
     * Tests successful update, status code validation, null constraints, unique constraints
     */
    @Test(priority = 2, dependsOnMethods = "testCreateProduct")
    public void testUpdateProduct() {
        logger.info("=== Testing Product Update (PUT) ===");

        int testRow = 17; // Starting row for PUT tests

        try {
            if (lastCreatedId != null) {
                // Test 1: Successful update with status code validation
                testSuccessfulProductUpdate(testRow);

                // Test 2: Null constraint validation for update
                testProductUpdateNullConstraints(testRow + 1);

                // Test 3: Unique constraint validation for update
                testProductUpdateUniqueConstraints(testRow + 2);
            } else {
                logger.warn("No created product ID available for update tests");
            }

        } catch (Exception e) {
            logger.error("Error in testUpdateProduct: " + e.getMessage());
            handleTestFailure(testRow, "PUT Test Failed", e.getMessage());
        }
    }

    /**
     * Test successful product update
     */
    private void testSuccessfulProductUpdate(int rowNum) throws Exception {
        logger.info("Testing successful product update at row {}", rowNum);

        // Generate updated test data using Faker
        String updateRequestBody = generateProductUpdateRequestBody(lastCreatedId);

        // Update Excel with generated request body
        excelUtils.setCellData(filePath, sheetName, rowNum, bodyCol, updateRequestBody);

        // Make PUT request
        Response putResponse = makePutRequest(rowNum, updateRequestBody);

        if (putResponse != null) {
            // Validate status code
            int actualStatusCode = putResponse.getStatusCode();
            String expectedStatusCode = excelUtils.getCellData(filePath, sheetName, rowNum, expectedResultCol);

            if (String.valueOf(actualStatusCode).equals(expectedStatusCode.trim())) {
                // Extract ID from response and validate with GetById
                String updatedId = extractIdFromResponse(putResponse);
                if (updatedId != null) {
                    validateUpdateWithGetById(rowNum, updatedId);
                    updateExcelWithSuccess(rowNum, putResponse.asString(), "Product updated successfully");
                } else {
                    updateExcelWithFailure(rowNum, "Failed to extract ID from update response", putResponse.asString());
                }
            } else {
                updateExcelWithFailure(rowNum,
                    "Status code mismatch. Expected: " + expectedStatusCode + ", Actual: " + actualStatusCode,
                    putResponse.asString());
            }
        }
    }

    /**
     * Test null constraint validation for product update
     */
    private void testProductUpdateNullConstraints(int rowNum) throws Exception {
        logger.info("Testing product update null constraints at row {}", rowNum);

        // Generate update request body with null required field
        String updateRequestBodyWithNull = generateProductUpdateRequestBodyWithNullField(lastCreatedId);

        // Update Excel with request body
        excelUtils.setCellData(filePath, sheetName, rowNum, bodyCol, updateRequestBodyWithNull);

        // Make PUT request
        Response response = makePutRequest(rowNum, updateRequestBodyWithNull);

        if (response != null) {
            // Get expected error message from Excel
            String expectedError = excelUtils.getCellData(filePath, sheetName, rowNum, expectedResultCol);
            String actualResponse = response.asString();

            // Check if actual response contains expected error message
            if (actualResponse.toLowerCase().contains(expectedError.toLowerCase()) ||
                response.getStatusCode() >= 400) {
                updateExcelWithSuccess(rowNum, actualResponse, "Update null constraint validation passed");
            } else {
                updateExcelWithFailure(rowNum, "Update null constraint validation failed", actualResponse);
            }
        }
    }

    /**
     * Test unique constraint validation for product update
     */
    private void testProductUpdateUniqueConstraints(int rowNum) throws Exception {
        logger.info("Testing product update unique constraints at row {}", rowNum);

        // Generate update request body that violates unique constraint
        String duplicateUpdateRequestBody = generateDuplicateProductUpdateRequestBody(lastCreatedId);

        // Update Excel with request body
        excelUtils.setCellData(filePath, sheetName, rowNum, bodyCol, duplicateUpdateRequestBody);

        // Make PUT request
        Response response = makePutRequest(rowNum, duplicateUpdateRequestBody);

        if (response != null) {
            // Get expected error message from Excel
            String expectedError = excelUtils.getCellData(filePath, sheetName, rowNum, expectedResultCol);
            String actualResponse = response.asString();

            // Check if actual response contains expected error message
            if (actualResponse.toLowerCase().contains(expectedError.toLowerCase()) ||
                response.getStatusCode() >= 400) {
                updateExcelWithSuccess(rowNum, actualResponse, "Update unique constraint validation passed");
            } else {
                updateExcelWithFailure(rowNum, "Update unique constraint validation failed", actualResponse);
            }
        }
    }

    /**
     * GET ALL API Test - Retrieve all products with database validation
     */
    @Test(priority = 3)
    public void testGetAllProducts() {
        logger.info("=== Testing Get All Products (GET ALL) ===");

        int testRow = 20; // Starting row for GET ALL tests

        try {
            // Test with different indices (0, 1, 2, 3...n)
            for (int index = 0; index < 5; index++) {
                testGetAllProductsWithIndex(testRow + index, index);
            }

        } catch (Exception e) {
            logger.error("Error in testGetAllProducts: " + e.getMessage());
            handleTestFailure(testRow, "GET ALL Test Failed", e.getMessage());
        }
    }

    /**
     * Test get all products with specific index
     */
    private void testGetAllProductsWithIndex(int rowNum, int index) throws Exception {
        logger.info("Testing get all products with index {} at row {}", index, rowNum);

        // Get request body from Excel
        String requestBody = excelUtils.getCellData(filePath, sheetName, rowNum, bodyCol);

        // Make GET ALL request
        Response getAllResponse = makeGetAllRequest(rowNum, requestBody);

        if (getAllResponse != null) {
            // Validate status code
            int actualStatusCode = getAllResponse.getStatusCode();
            String expectedStatusCode = excelUtils.getCellData(filePath, sheetName, rowNum, expectedResultCol);

            if (String.valueOf(actualStatusCode).equals(expectedStatusCode.trim())) {
                // Parse response array and get specific index
                JsonNode responseArray = objectMapper.readTree(getAllResponse.asString());

                if (responseArray.isArray() && responseArray.size() > index) {
                    JsonNode productAtIndex = responseArray.get(index);
                    String productId = productAtIndex.get("id").asText();

                    // Store in expected result
                    excelUtils.setCellData(filePath, sheetName, rowNum, expectedResultCol, productAtIndex.toString());

                    // Query database for this product
                    String dbResult = dbUtils.getProductFromDatabase(productId);

                    // Store in actual result
                    excelUtils.setCellData(filePath, sheetName, rowNum, actualResultCol, dbResult);

                    // Compare API response with database
                    if (compareApiWithDatabase(productAtIndex.toString(), dbResult)) {
                        updateExcelWithSuccess(rowNum, dbResult, "Get All Products validation passed for index " + index);
                    } else {
                        updateExcelWithFailure(rowNum, "API and Database mismatch for index " + index, dbResult);
                    }
                } else {
                    updateExcelWithFailure(rowNum, "Array index " + index + " not found in response", getAllResponse.asString());
                }
            } else {
                updateExcelWithFailure(rowNum,
                    "Status code mismatch. Expected: " + expectedStatusCode + ", Actual: " + actualStatusCode,
                    getAllResponse.asString());
            }
        }
    }

    /**
     * GET BY ID API Test - Retrieve product by ID with database validation
     */
    @Test(priority = 4, dependsOnMethods = "testCreateProduct")
    public void testGetProductById() {
        logger.info("=== Testing Get Product By ID (GET BY ID) ===");

        int testRow = 25; // Starting row for GET BY ID tests

        try {
            if (lastCreatedId != null) {
                testGetProductByIdWithValidation(testRow);
            } else {
                logger.warn("No created product ID available for GetById tests");
            }

        } catch (Exception e) {
            logger.error("Error in testGetProductById: " + e.getMessage());
            handleTestFailure(testRow, "GET BY ID Test Failed", e.getMessage());
        }
    }

    /**
     * Test get product by ID with database validation
     */
    private void testGetProductByIdWithValidation(int rowNum) throws Exception {
        logger.info("Testing get product by ID at row {}", rowNum);

        // Generate GetById request body
        String getByIdRequestBody = generateGetByIdRequestBody(lastCreatedId);

        // Update Excel with request body
        excelUtils.setCellData(filePath, sheetName, rowNum, bodyCol, getByIdRequestBody);

        // Make GET BY ID request
        Response getByIdResponse = makeGetByIdRequest(rowNum, getByIdRequestBody);

        if (getByIdResponse != null) {
            // Validate status code
            int actualStatusCode = getByIdResponse.getStatusCode();
            String expectedStatusCode = excelUtils.getCellData(filePath, sheetName, rowNum, expectedResultCol);

            if (String.valueOf(actualStatusCode).equals(expectedStatusCode.trim())) {
                // Store API response in expected result
                String apiResponse = getByIdResponse.asString();
                excelUtils.setCellData(filePath, sheetName, rowNum, expectedResultCol, apiResponse);

                // Extract ID from response
                String responseId = extractIdFromResponse(getByIdResponse);

                if (responseId != null) {
                    // Query database for this product with foreign key handling
                    String dbResult = dbUtils.getProductFromDatabaseWithForeignKeys(responseId);

                    // Store in actual result
                    excelUtils.setCellData(filePath, sheetName, rowNum, actualResultCol, dbResult);

                    // Compare API response with database
                    if (compareApiWithDatabase(apiResponse, dbResult)) {
                        updateExcelWithSuccess(rowNum, dbResult, "Get Product By ID validation passed");
                    } else {
                        updateExcelWithFailure(rowNum, "API and Database mismatch", dbResult);
                    }
                } else {
                    updateExcelWithFailure(rowNum, "Failed to extract ID from GetById response", apiResponse);
                }
            } else {
                updateExcelWithFailure(rowNum,
                    "Status code mismatch. Expected: " + expectedStatusCode + ", Actual: " + actualStatusCode,
                    getByIdResponse.asString());
            }
        }
    }

    /**
     * DELETE API Test - Delete product and verify deletion
     */
    @Test(priority = 5, dependsOnMethods = "testCreateProduct")
    public void testDeleteProduct() {
        logger.info("=== Testing Product Deletion (DELETE) ===");

        int testRow = 26; // Starting row for DELETE tests

        try {
            if (lastCreatedId != null) {
                testProductDeleteWithValidation(testRow);
            } else {
                logger.warn("No created product ID available for delete tests");
            }

        } catch (Exception e) {
            logger.error("Error in testDeleteProduct: " + e.getMessage());
            handleTestFailure(testRow, "DELETE Test Failed", e.getMessage());
        }
    }

    /**
     * Test product deletion with validation
     */
    private void testProductDeleteWithValidation(int rowNum) throws Exception {
        logger.info("Testing product deletion at row {}", rowNum);

        // Generate DELETE request body
        String deleteRequestBody = generateDeleteRequestBody(lastCreatedId);

        // Update Excel with request body
        excelUtils.setCellData(filePath, sheetName, rowNum, bodyCol, deleteRequestBody);

        // Make DELETE request
        Response deleteResponse = makeDeleteRequest(rowNum, deleteRequestBody);

        if (deleteResponse != null) {
            // Validate status code
            int actualStatusCode = deleteResponse.getStatusCode();
            String expectedStatusCode = excelUtils.getCellData(filePath, sheetName, rowNum, expectedResultCol);

            if (String.valueOf(actualStatusCode).equals(expectedStatusCode.trim())) {
                // Verify deletion by trying to get the deleted record
                verifyDeletionWithGetById(rowNum + 1, lastCreatedId);
                updateExcelWithSuccess(rowNum, deleteResponse.asString(), "Product deleted successfully");
            } else {
                updateExcelWithFailure(rowNum,
                    "Status code mismatch. Expected: " + expectedStatusCode + ", Actual: " + actualStatusCode,
                    deleteResponse.asString());
            }
        }
    }

    /**
     * Verify deletion by attempting to get the deleted record
     */
    private void verifyDeletionWithGetById(int rowNum, String deletedId) throws Exception {
        logger.info("Verifying deletion by GetById at row {}", rowNum);

        // Generate GetById request body for deleted record
        String getByIdRequestBody = generateGetByIdRequestBody(deletedId);

        // Make GET BY ID request for deleted record
        Response getByIdResponse = makeGetByIdRequest(rowNum, getByIdRequestBody);

        if (getByIdResponse != null) {
            int statusCode = getByIdResponse.getStatusCode();
            String expectedStatusCode = excelUtils.getCellData(filePath, sheetName, rowNum, expectedResultCol);

            // Should return 404 or similar error status
            if (String.valueOf(statusCode).equals(expectedStatusCode.trim()) || statusCode >= 400) {
                updateExcelWithSuccess(rowNum, getByIdResponse.asString(), "Deletion verification passed - record not found");
            } else {
                updateExcelWithFailure(rowNum, "Deletion verification failed - record still exists", getByIdResponse.asString());
            }
        }
    }

    // ==================== HELPER METHODS ====================

    /**
     * Generate product request body using Faker
     */
    private String generateProductRequestBody() throws Exception {
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> productData = new HashMap<>();

            productData.put("name", faker.commerce().productName());
            productData.put("description", faker.lorem().paragraph());
            productData.put("price", faker.number().randomDouble(2, 10, 1000));
            productData.put("category", faker.commerce().department());
            productData.put("brand", faker.company().name());
            productData.put("sku", faker.code().ean13());
            productData.put("stock", faker.number().numberBetween(1, 100));
            productData.put("isActive", true);
            productData.put("imageUrl", faker.internet().url());
            productData.put("weight", BigDecimal.valueOf(faker.number().randomDouble(2, 1, 50)));
            productData.put("dimensions", faker.number().numberBetween(10, 100) + "x" +
                                        faker.number().numberBetween(10, 100) + "x" +
                                        faker.number().numberBetween(10, 100));
            productData.put("createdBy", faker.name().username());
            productData.put("auth", authToken);
            productData.put("type", "post");

            return mapper.writeValueAsString(productData);
        } catch (Exception e) {
            logger.error("Error generating product request body: " + e.getMessage());
            throw e;
        }
    }

    /**
     * Generate product request body with null field for constraint testing
     */
    private String generateProductRequestBodyWithNullField() throws Exception {
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> productData = new HashMap<>();

            // Leave name as null to test null constraint
            productData.put("name", null);
            productData.put("description", faker.lorem().paragraph());
            productData.put("price", faker.number().randomDouble(2, 10, 1000));
            productData.put("category", faker.commerce().department());
            productData.put("brand", faker.company().name());
            productData.put("sku", faker.code().ean13());
            productData.put("stock", faker.number().numberBetween(1, 100));
            productData.put("auth", authToken);
            productData.put("type", "post");

            return mapper.writeValueAsString(productData);
        } catch (Exception e) {
            logger.error("Error generating product request body with null field: " + e.getMessage());
            throw e;
        }
    }

    /**
     * Generate duplicate product request body for unique constraint testing
     */
    private String generateDuplicateProductRequestBody() throws Exception {
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> productData = new HashMap<>();

            // Use same SKU to trigger unique constraint
            productData.put("name", "Duplicate Product Name");
            productData.put("description", faker.lorem().paragraph());
            productData.put("price", faker.number().randomDouble(2, 10, 1000));
            productData.put("category", faker.commerce().department());
            productData.put("brand", faker.company().name());
            productData.put("sku", "DUPLICATE-SKU-123456");
            productData.put("stock", faker.number().numberBetween(1, 100));
            productData.put("auth", authToken);
            productData.put("type", "post");

            return mapper.writeValueAsString(productData);
        } catch (Exception e) {
            logger.error("Error generating duplicate product request body: " + e.getMessage());
            throw e;
        }
    }

    /**
     * Generate product update request body using Faker
     */
    private String generateProductUpdateRequestBody(String productId) throws Exception {
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> productData = new HashMap<>();

            productData.put("id", productId);
            productData.put("name", faker.commerce().productName() + " - Updated");
            productData.put("description", faker.lorem().paragraph() + " - Updated");
            productData.put("price", faker.number().randomDouble(2, 10, 1000));
            productData.put("category", faker.commerce().department());
            productData.put("brand", faker.company().name());
            productData.put("sku", faker.code().ean13());
            productData.put("stock", faker.number().numberBetween(1, 100));
            productData.put("isActive", true);
            productData.put("imageUrl", faker.internet().url());
            productData.put("weight", BigDecimal.valueOf(faker.number().randomDouble(2, 1, 50)));
            productData.put("dimensions", faker.number().numberBetween(10, 100) + "x" +
                                        faker.number().numberBetween(10, 100) + "x" +
                                        faker.number().numberBetween(10, 100));
            productData.put("updatedBy", faker.name().username());
            productData.put("auth", authToken);
            productData.put("type", "put");

            return mapper.writeValueAsString(productData);
        } catch (Exception e) {
            logger.error("Error generating product update request body: " + e.getMessage());
            throw e;
        }
    }

    /**
     * Generate product update request body with null field
     */
    private String generateProductUpdateRequestBodyWithNullField(String productId) throws Exception {
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> productData = new HashMap<>();

            productData.put("id", productId);
            productData.put("name", null); // Null field for constraint testing
            productData.put("description", faker.lorem().paragraph());
            productData.put("price", faker.number().randomDouble(2, 10, 1000));
            productData.put("auth", authToken);
            productData.put("type", "put");

            return mapper.writeValueAsString(productData);
        } catch (Exception e) {
            logger.error("Error generating product update request body with null field: " + e.getMessage());
            throw e;
        }
    }

    /**
     * Generate duplicate product update request body
     */
    private String generateDuplicateProductUpdateRequestBody(String productId) throws Exception {
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> productData = new HashMap<>();

            productData.put("id", productId);
            productData.put("name", "Duplicate Product Name"); // Duplicate name
            productData.put("description", faker.lorem().paragraph());
            productData.put("sku", "DUPLICATE-SKU-123456"); // Duplicate SKU
            productData.put("auth", authToken);
            productData.put("type", "put");

            return mapper.writeValueAsString(productData);
        } catch (Exception e) {
            logger.error("Error generating duplicate product update request body: " + e.getMessage());
            throw e;
        }
    }

    /**
     * Generate GetById request body
     */
    private String generateGetByIdRequestBody(String productId) throws Exception {
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> requestData = new HashMap<>();

            requestData.put("id", productId);
            requestData.put("auth", authToken);
            requestData.put("type", "getById");

            return mapper.writeValueAsString(requestData);
        } catch (Exception e) {
            logger.error("Error generating GetById request body: " + e.getMessage());
            throw e;
        }
    }

    /**
     * Generate Delete request body
     */
    private String generateDeleteRequestBody(String productId) throws Exception {
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> requestData = new HashMap<>();

            requestData.put("id", productId);
            requestData.put("auth", authToken);
            requestData.put("type", "delete");

            return mapper.writeValueAsString(requestData);
        } catch (Exception e) {
            logger.error("Error generating Delete request body: " + e.getMessage());
            throw e;
        }
    }

    /**
     * Make POST request
     */
    private Response makePostRequest(int rowNum, String requestBody) throws Exception {
        try {
            api.helpers.PostWithDynamicRequestBody requestHandler =
                new api.helpers.PostWithDynamicRequestBody(logger, filePath, sheetName, urlCol, bodyCol);
            return requestHandler.post(rowNum, requestBody);
        } catch (Exception e) {
            logger.error("Error making POST request: " + e.getMessage());
            updateExcelWithFailure(rowNum, "POST request failed", e.getMessage());
            return null;
        }
    }

    /**
     * Make PUT request
     */
    private Response makePutRequest(int rowNum, String requestBody) throws Exception {
        try {
            api.helpers.PostWithDynamicRequestBody requestHandler =
                new api.helpers.PostWithDynamicRequestBody(logger, filePath, sheetName, urlCol, bodyCol);
            return requestHandler.post(rowNum, requestBody);
        } catch (Exception e) {
            logger.error("Error making PUT request: " + e.getMessage());
            updateExcelWithFailure(rowNum, "PUT request failed", e.getMessage());
            return null;
        }
    }

    /**
     * Make GET ALL request
     */
    private Response makeGetAllRequest(int rowNum, String requestBody) throws Exception {
        try {
            api.helpers.PostWithDynamicRequestBody requestHandler =
                new api.helpers.PostWithDynamicRequestBody(logger, filePath, sheetName, urlCol, bodyCol);
            return requestHandler.post(rowNum, requestBody);
        } catch (Exception e) {
            logger.error("Error making GET ALL request: " + e.getMessage());
            updateExcelWithFailure(rowNum, "GET ALL request failed", e.getMessage());
            return null;
        }
    }

    /**
     * Make GET BY ID request
     */
    private Response makeGetByIdRequest(int rowNum, String requestBody) throws Exception {
        try {
            api.helpers.PostWithDynamicRequestBody requestHandler =
                new api.helpers.PostWithDynamicRequestBody(logger, filePath, sheetName, urlCol, bodyCol);
            return requestHandler.post(rowNum, requestBody);
        } catch (Exception e) {
            logger.error("Error making GET BY ID request: " + e.getMessage());
            updateExcelWithFailure(rowNum, "GET BY ID request failed", e.getMessage());
            return null;
        }
    }

    /**
     * Make DELETE request
     */
    private Response makeDeleteRequest(int rowNum, String requestBody) throws Exception {
        try {
            api.helpers.PostWithDynamicRequestBody requestHandler =
                new api.helpers.PostWithDynamicRequestBody(logger, filePath, sheetName, urlCol, bodyCol);
            return requestHandler.post(rowNum, requestBody);
        } catch (Exception e) {
            logger.error("Error making DELETE request: " + e.getMessage());
            updateExcelWithFailure(rowNum, "DELETE request failed", e.getMessage());
            return null;
        }
    }

    /**
     * Extract ID from response
     */
    private String extractIdFromResponse(Response response) {
        try {
            JsonNode jsonNode = objectMapper.readTree(response.asString());
            if (jsonNode.has("id")) {
                return jsonNode.get("id").asText();
            } else if (jsonNode.has("data") && jsonNode.get("data").has("id")) {
                return jsonNode.get("data").get("id").asText();
            }
            logger.warn("ID not found in response: " + response.asString());
            return null;
        } catch (Exception e) {
            logger.error("Error extracting ID from response: " + e.getMessage());
            return null;
        }
    }

    /**
     * Validate creation with GetById API
     */
    private void validateCreationWithGetById(int rowNum, String createdId) throws Exception {
        logger.info("Validating creation with GetById for ID: {}", createdId);

        String getByIdRequestBody = generateGetByIdRequestBody(createdId);
        Response getByIdResponse = makeGetByIdRequest(rowNum, getByIdRequestBody);

        if (getByIdResponse != null && getByIdResponse.getStatusCode() == 200) {
            logger.info("Creation validation successful - record found with GetById");
        } else {
            logger.warn("Creation validation failed - record not found with GetById");
        }
    }

    /**
     * Validate update with GetById API
     */
    private void validateUpdateWithGetById(int rowNum, String updatedId) throws Exception {
        logger.info("Validating update with GetById for ID: {}", updatedId);

        String getByIdRequestBody = generateGetByIdRequestBody(updatedId);
        Response getByIdResponse = makeGetByIdRequest(rowNum, getByIdRequestBody);

        if (getByIdResponse != null && getByIdResponse.getStatusCode() == 200) {
            logger.info("Update validation successful - updated record found with GetById");
        } else {
            logger.warn("Update validation failed - updated record not found with GetById");
        }
    }

    /**
     * Compare API response with database result
     */
    private boolean compareApiWithDatabase(String apiResponse, String dbResult) {
        try {
            JsonNode apiNode = objectMapper.readTree(apiResponse);
            JsonNode dbNode = objectMapper.readTree(dbResult);

            // Compare key fields (ignoring timestamps and system fields)
            String[] fieldsToCompare = {"id", "name", "description", "price", "category", "brand", "sku", "stock"};

            for (String field : fieldsToCompare) {
                if (apiNode.has(field) && dbNode.has(field)) {
                    String apiValue = apiNode.get(field).asText();
                    String dbValue = dbNode.get(field).asText();

                    if (!apiValue.equals(dbValue)) {
                        logger.warn("Field mismatch - {}: API={}, DB={}", field, apiValue, dbValue);
                        return false;
                    }
                }
            }

            return true;
        } catch (Exception e) {
            logger.error("Error comparing API with database: " + e.getMessage());
            return false;
        }
    }

    /**
     * Update Excel with success result
     */
    private void updateExcelWithSuccess(int rowNum, String actualResult, String message) {
        try {
            excelUtils.setCellData(filePath, sheetName, rowNum, actualResultCol, actualResult);
            excelUtils.setCellData(filePath, sheetName, rowNum, statusCol, "PASS", true);
            logger.info("Test PASSED at row {}: {}", rowNum, message);
        } catch (Exception e) {
            logger.error("Error updating Excel with success: " + e.getMessage());
        }
    }

    /**
     * Update Excel with failure result
     */
    private void updateExcelWithFailure(int rowNum, String errorMessage, String actualResult) {
        try {
            excelUtils.setCellData(filePath, sheetName, rowNum, actualResultCol, actualResult);
            excelUtils.setCellData(filePath, sheetName, rowNum, statusCol, "FAIL", false);

            // Generate defect ID for failed test case
            String defectId = defectTracker.generateDefectId("PRODUCT", "API_TEST", errorMessage);
            excelUtils.setCellData(filePath, sheetName, rowNum, defectIdCol, defectId);

            logger.error("Test FAILED at row {}: {}", rowNum, errorMessage);
        } catch (Exception e) {
            logger.error("Error updating Excel with failure: " + e.getMessage());
        }
    }

    /**
     * Handle test failure
     */
    private void handleTestFailure(int rowNum, String testType, String errorMessage) {
        try {
            String defectId = defectTracker.generateDefectId("PRODUCT", testType, errorMessage);
            excelUtils.setCellData(filePath, sheetName, rowNum, statusCol, "FAIL", false);
            excelUtils.setCellData(filePath, sheetName, rowNum, defectIdCol, defectId);
            excelUtils.setCellData(filePath, sheetName, rowNum, actualResultCol, "Test execution failed: " + errorMessage);

            logger.error("Test execution failed at row {}: {}", rowNum, errorMessage);
        } catch (Exception e) {
            logger.error("Error handling test failure: " + e.getMessage());
        }
    }
}