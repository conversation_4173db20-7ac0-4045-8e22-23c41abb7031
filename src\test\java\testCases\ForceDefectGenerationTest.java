package testCases;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;
import utils.*;

/**
 * Test to force defect generation by creating intentional failures
 */
public class ForceDefectGenerationTest {
    private static final Logger logger = LoggerFactory.getLogger(ForceDefectGenerationTest.class);

    private final TestConfiguration config = TestConfiguration.getInstance();
    private String filePath;
    private String baseUrl;
    private String authToken;
    private ExcelUtils excelUtils;

    // Column mappings from configuration
    private int urlCol;
    private int bodyCol;
    private int expectedResultCol;
    private int actualResultCol;
    private int statusCol;
    private int defectIdCol;

    @BeforeClass
    public void setup() {
        logger.info("=== Force Defect Generation Test Setup ===");

        // Load configuration
        loadConfiguration();
        
        // Initialize Excel utils
        excelUtils = new ExcelUtils();

        // Get authentication token
        authToken = getAuthToken();
        logger.info("Authentication token obtained for forced defect generation testing");
    }

    private void loadConfiguration() {
        logger.info("Loading configuration for forced defect generation test...");

        // Excel configuration
        filePath = config.getExcelFilePath();

        // API configuration
        baseUrl = config.getBaseUrl();

        // Column mappings
        urlCol = config.getUrlColumn();
        bodyCol = config.getBodyColumn();
        expectedResultCol = config.getExpectedResultColumn();
        actualResultCol = config.getActualResultColumn();
        statusCol = config.getStatusColumn();
        defectIdCol = config.getDefectIdColumn();

        logger.info("Forced defect generation test configuration loaded successfully");
    }

    private String getAuthToken() {
        try {
            basic.BasicTestCase1 bt = new basic.BasicTestCase1(
                logger, filePath, "Order Service", urlCol, bodyCol, statusCol,
                actualResultCol, expectedResultCol, 2
            );
            return bt.signIn(13); // Use row 13 for authentication
        } catch (Exception e) {
            logger.error("Error getting auth token: {}", e.getMessage());
            return "default-token";
        }
    }

    @Test
    public void testForceDefectGeneration() {
        logger.info("=== Forcing Defect Generation by Setting Wrong Expected Result ===");
        
        int testRow = 25; // Use row 25 for this test
        String sheetName = "Order Service";
        
        try {
            // Step 1: Set wrong expected result in Excel to force failure
            logger.info("Step 1: Setting wrong expected result (400) in Excel row {}", testRow);
            excelUtils.setCellData(filePath, sheetName, testRow, expectedResultCol, "400");
            
            // Step 2: Set table name in column 2 for defect ID generation
            logger.info("Step 2: Setting table name 'TestTable' in column 2");
            excelUtils.setCellData(filePath, sheetName, testRow, 2, "TestTable");
            
            // Step 3: Execute the test (API will return 201, but we expect 400)
            logger.info("Step 3: Executing API test that will fail...");
            ComprehensiveApiTestEngine testEngine = new ComprehensiveApiTestEngine(
                urlCol, bodyCol, expectedResultCol, actualResultCol, statusCol, defectIdCol
            );
            
            testEngine.executeComprehensiveApiTest(filePath, sheetName, testRow, baseUrl, authToken);
            
            // Step 4: Verify defect was generated
            logger.info("Step 4: Checking if defect ID was generated...");
            String defectId = excelUtils.getCellData(filePath, sheetName, testRow, defectIdCol);
            String status = excelUtils.getCellData(filePath, sheetName, testRow, statusCol);
            String actualResult = excelUtils.getCellData(filePath, sheetName, testRow, actualResultCol);
            
            logger.info("=== Test Results ===");
            logger.info("Status: {}", status);
            logger.info("Defect ID: {}", defectId);
            logger.info("Actual Result: {}", actualResult);
            
            if (defectId != null && !defectId.trim().isEmpty() && defectId.startsWith("D_")) {
                logger.info("✅ SUCCESS: Defect ID generated successfully: {}", defectId);
                logger.info("✅ Expected format D_TableName_XXX: {}", defectId.matches("D_\\w+_\\d{3}"));
            } else {
                logger.error("❌ FAILURE: No defect ID generated or wrong format: {}", defectId);
            }
            
            if ("Failed".equals(status)) {
                logger.info("✅ SUCCESS: Status correctly set to 'Failed'");
            } else {
                logger.error("❌ FAILURE: Status not set to 'Failed': {}", status);
            }
            
        } catch (Exception e) {
            logger.error("Error during forced defect generation test: {}", e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testMultipleDefectGenerationForSameTable() {
        logger.info("=== Testing Multiple Defects for Same Table (Unique IDs) ===");
        
        String sheetName = "Order Service";
        String tableName = "MultiTestTable";
        
        // Generate 3 defects for the same table to test uniqueness
        for (int i = 1; i <= 3; i++) {
            int testRow = 25 + i; // Use rows 26, 27, 28
            
            try {
                logger.info("Generating defect {} for table '{}'", i, tableName);
                
                // Set wrong expected result to force failure
                excelUtils.setCellData(filePath, sheetName, testRow, expectedResultCol, "500");
                
                // Set same table name for all tests
                excelUtils.setCellData(filePath, sheetName, testRow, 2, tableName);
                
                // Execute test
                ComprehensiveApiTestEngine testEngine = new ComprehensiveApiTestEngine(
                    urlCol, bodyCol, expectedResultCol, actualResultCol, statusCol, defectIdCol
                );
                
                testEngine.executeComprehensiveApiTest(filePath, sheetName, testRow, baseUrl, authToken);
                
                // Check generated defect ID
                String defectId = excelUtils.getCellData(filePath, sheetName, testRow, defectIdCol);
                logger.info("Generated defect ID {}: {}", i, defectId);
                
                // Verify format
                if (defectId != null && defectId.startsWith("D_" + tableName + "_")) {
                    logger.info("✅ Defect {} has correct format", i);
                } else {
                    logger.error("❌ Defect {} has wrong format: {}", i, defectId);
                }
                
            } catch (Exception e) {
                logger.error("Error generating defect {}: {}", i, e.getMessage());
            }
        }
        
        logger.info("=== Multiple Defect Generation Test Completed ===");
        logger.info("Check Excel rows 26-28 for unique defect IDs:");
        logger.info("Expected: D_MultiTestTable_001, D_MultiTestTable_002, D_MultiTestTable_003");
    }

    @Test
    public void testDefectGenerationForDifferentTables() {
        logger.info("=== Testing Defect Generation for Different Tables ===");
        
        String sheetName = "Order Service";
        String[] tableNames = {"TableA", "TableB", "TableC"};
        
        // Generate defects for different tables
        for (int i = 0; i < tableNames.length; i++) {
            int testRow = 29 + i; // Use rows 29, 30, 31
            String tableName = tableNames[i];
            
            try {
                logger.info("Generating defect for table '{}'", tableName);
                
                // Set wrong expected result to force failure
                excelUtils.setCellData(filePath, sheetName, testRow, expectedResultCol, "404");
                
                // Set different table name
                excelUtils.setCellData(filePath, sheetName, testRow, 2, tableName);
                
                // Execute test
                ComprehensiveApiTestEngine testEngine = new ComprehensiveApiTestEngine(
                    urlCol, bodyCol, expectedResultCol, actualResultCol, statusCol, defectIdCol
                );
                
                testEngine.executeComprehensiveApiTest(filePath, sheetName, testRow, baseUrl, authToken);
                
                // Check generated defect ID
                String defectId = excelUtils.getCellData(filePath, sheetName, testRow, defectIdCol);
                logger.info("Generated defect ID for {}: {}", tableName, defectId);
                
                // Verify format
                if (defectId != null && defectId.startsWith("D_" + tableName + "_001")) {
                    logger.info("✅ Defect for {} has correct format (should be _001 for first defect)", tableName);
                } else {
                    logger.warn("⚠️ Defect for {} format: {} (may not be _001 if table was used before)", tableName, defectId);
                }
                
            } catch (Exception e) {
                logger.error("Error generating defect for {}: {}", tableName, e.getMessage());
            }
        }
        
        logger.info("=== Different Tables Defect Generation Test Completed ===");
        logger.info("Check Excel rows 29-31 for table-specific defect IDs:");
        logger.info("Expected: D_TableA_XXX, D_TableB_XXX, D_TableC_XXX");
    }
}
