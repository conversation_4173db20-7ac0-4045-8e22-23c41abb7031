<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="3d4f6578-92ee-41f4-afda-fa56b64f3ff6" name="Changes" comment="adding logout functionality">
      <change afterPath="$PROJECT_DIR$/Defect_Tracking_Setup_Guide.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Excel_Structure_Guide.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Framework_Architecture_Guide.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/data/SnackHack.xlsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/setup-defect-tracking.bat" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/setup-defect-tracking.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/test/java/api/ApiTestBase.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/test/java/api/DeleteApiTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/test/java/api/FilterApiTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/test/java/api/GetAllApiTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/test/java/api/GetByIdApiTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/test/java/api/PatchApiTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/test/java/api/PostApiTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/test/java/api/PutApiTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/test/java/api/helpers/PostWithDynamicRequestBody.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/test/java/basic/Post/PostWithDynamicRequestBody.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/test/java/utils/DefectConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/test/java/utils/DefectDetails.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/test/java/utils/DefectTracker.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/test/java/utils/DynamicDataGenerator.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/test/java/utils/TestConfiguration.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/test/resources/test-config.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.classpath" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.factorypath" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/compiler.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/encodings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/jarRepositories.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/misc.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/vcs.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.project" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/config.properties" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/data/Automation Candidate Onboarding (new).xlsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/hs_err_pid20348.log" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/hs_err_pid32732.log" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/hs_err_pid33328.log" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/hs_err_pid37356.log" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/logs/app.2023-04-03.0.log.gz" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/logs/app.2023-04-05.0.log.gz" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/logs/app.2023-04-06.0.log.gz" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/logs/test.log" beforeDir="false" afterPath="$PROJECT_DIR$/logs/test.log" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/replay_pid20348.log" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/replay_pid32732.log" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/replay_pid33328.log" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/replay_pid37356.log" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/org/example/Main.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/org/example/Main.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/basic/ApiMethod.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/test/java/basic/ApiMethod.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/basic/BasicTestCase.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/basic/CsvfileTestCases.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/basic/Delete/DeleteBasic.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/basic/Delete/DeleteWithAccessToken.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/basic/Delete/DeleteWithHeader.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/basic/Get/GetBasic.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/basic/Get/GetWithAccessToken.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/basic/Get/GetWithHeader.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/basic/Post/PostBasic.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/test/java/basic/Post/PostBasic.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/basic/Post/PostWithAccessToken.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/test/java/basic/Post/PostWithAccessToken.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/basic/Post/PostWithHeader.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/test/java/basic/Post/PostWithHeader.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/basic/Put/PutBasic.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/basic/Put/PutWithAccessToken.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/basic/Put/PutWithHeader.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/postTest/DeleteTestCases.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/postTest/GetByIdTestCases.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/postTest/GetTestCases.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/postTest/PostTestCases.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/postTest/PutTestCases.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/testNG/ReportGenerator.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/test/java/testNG/ReportGenerator.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/utils/ConfigReader.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/utils/Excel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/utils/ExcelUtils.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/test/java/utils/ExcelUtils.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/RestassuredapiTesting-1.0-SNAPSHOT.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/org/example/Main.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/org/example/Main.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/maven-archiver/pom.properties" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/testCompile/default-testCompile/createdFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/testCompile/default-testCompile/createdFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/testCompile/default-testCompile/inputFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/testCompile/default-testCompile/inputFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/surefire-reports/2023-04-06T11-58-28_925-jvmRun1.dump" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/surefire-reports/2023-04-06T11-58-28_925-jvmRun1.dumpstream" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/test-classes/basic/ApiMethod.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/test-classes/basic/ApiMethod.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/test-classes/basic/BasicTestCase.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/test-classes/basic/CsvfileTestCases.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/test-classes/basic/Delete/DeleteBasic.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/test-classes/basic/Delete/DeleteWithAccessToken.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/test-classes/basic/Delete/DeleteWithHeader.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/test-classes/basic/Get/GetBasic.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/test-classes/basic/Get/GetWithAccessToken.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/test-classes/basic/Get/GetWithHeader.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/test-classes/basic/Post/PostBasic.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/test-classes/basic/Post/PostBasic.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/test-classes/basic/Post/PostWithAccessToken.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/test-classes/basic/Post/PostWithAccessToken.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/test-classes/basic/Post/PostWithHeader.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/test-classes/basic/Post/PostWithHeader.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/test-classes/basic/Put/PutBasic.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/test-classes/basic/Put/PutWithAccessToken.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/test-classes/basic/Put/PutWithHeader.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/test-classes/postTest/GetTestCases.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/test-classes/testNG/ReportGenerator.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/test-classes/testNG/ReportGenerator.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/test-classes/utils/ConfigReader.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/test-classes/utils/Excel.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/test-classes/utils/ExcelUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/test-classes/utils/ExcelUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/test_report.html" beforeDir="false" afterPath="$PROJECT_DIR$/test_report.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/testng.xml" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CodeInsightWorkspaceSettings">
    <option name="optimizeImportsOnTheFly" value="true" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <excluded-from-favorite>
      <branch-storage>
        <map>
          <entry type="LOCAL">
            <value>
              <list>
                <branch-info repo="$PROJECT_DIR$" source="master" />
              </list>
            </value>
          </entry>
        </map>
      </branch-storage>
    </excluded-from-favorite>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$USER_HOME$/AppData/Local/Temp/a3964c08-ddae-4194-8b45-85e141effb14_BasicFramework (4).zip.b14/BasicFramework (2)/BasicFramework/src/test/java/utils/ExcelUtils.java" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2XLSKY3iE4duEfqg0sCzdyJ4iF6" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ASKED_ADD_EXTERNAL_FILES": "true",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "TestNG.CoreService.TC_01.executor": "Run",
    "TestNG.CoreService.TC_02.executor": "Run",
    "TestNG.OrderService.TC_01.executor": "Run",
    "TestNG.OrderService.TC_02.executor": "Run",
    "TestNG.ProductsApiTest.testCreateProduct.executor": "Run",
    "git-widget-placeholder": "dev1",
    "last_opened_file_path": "D:/RestAssuredApiTesting/SnackHackApiAutomation/data",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "project.propVCSSupport.Mappings"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\RestAssuredApiTesting\SnackHackApiAutomation\data" />
      <recent name="D:\RestAssuredApiTesting\BasicFramework_RFilings - Copy\data" />
      <recent name="D:\BasicFramework_RFilings - Copy\data" />
      <recent name="D:\BasicFramework_RFilings\data" />
      <recent name="F:\BasicFramework (2)\BasicFramework\data" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="basic" />
    </key>
  </component>
  <component name="RunManager" selected="TestNG.ProductsApiTest.testCreateProduct">
    <configuration name="CoreService.TC_01" type="TestNG" nameIsGenerated="true">
      <module name="RestassuredapiTesting" />
      <shortenClasspath name="NONE" />
      <useClassPathOnly />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="testCases.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="SUITE_NAME" value="" />
      <option name="PACKAGE_NAME" value="testCases" />
      <option name="MAIN_CLASS_NAME" value="testCases.CoreService" />
      <option name="METHOD_NAME" value="TC_01" />
      <option name="GROUP_NAME" value="" />
      <option name="TEST_OBJECT" value="METHOD" />
      <option name="PARAMETERS" value="" />
      <option name="OUTPUT_DIRECTORY" value="" />
      <option name="TEST_SEARCH_SCOPE">
        <value defaultName="moduleWithDependencies" />
      </option>
      <option name="PROPERTIES_FILE" value="" />
      <properties />
      <listeners />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CoreService.TC_02" type="TestNG" temporary="true" nameIsGenerated="true">
      <module name="RestassuredapiTesting" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="testCases.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="testCases" />
      <option name="MAIN_CLASS_NAME" value="testCases.CoreService" />
      <option name="METHOD_NAME" value="TC_02" />
      <option name="TEST_OBJECT" value="METHOD" />
      <properties />
      <listeners />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CoreService.TC_17" type="TestNG" temporary="true" nameIsGenerated="true">
      <module name="RestassuredapiTesting" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="testCases.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="testCases" />
      <option name="MAIN_CLASS_NAME" value="testCases.CoreService" />
      <option name="METHOD_NAME" value="TC_17" />
      <option name="TEST_OBJECT" value="METHOD" />
      <properties />
      <listeners />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Photo.TC_01" type="TestNG" temporary="true" nameIsGenerated="true">
      <module name="RestassuredapiTesting" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="testCases.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="testCases" />
      <option name="MAIN_CLASS_NAME" value="testCases.Photo" />
      <option name="METHOD_NAME" value="TC_01" />
      <option name="TEST_OBJECT" value="METHOD" />
      <properties />
      <listeners />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Photo.TC_02" type="TestNG" temporary="true" nameIsGenerated="true">
      <module name="RestassuredapiTesting" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="testCases.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="testCases" />
      <option name="MAIN_CLASS_NAME" value="testCases.Photo" />
      <option name="METHOD_NAME" value="TC_02" />
      <option name="TEST_OBJECT" value="METHOD" />
      <properties />
      <listeners />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ProductsApiTest.testCreateProduct" type="TestNG" temporary="true" nameIsGenerated="true">
      <module name="RestassuredapiTesting" />
      <shortenClasspath name="NONE" />
      <useClassPathOnly />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="testCases.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <option name="ALTERNATIVE_JRE_PATH" value="21" />
      <option name="SUITE_NAME" value="" />
      <option name="PACKAGE_NAME" value="testCases" />
      <option name="MAIN_CLASS_NAME" value="testCases.ProductsApiTest" />
      <option name="METHOD_NAME" value="testCreateProduct" />
      <option name="GROUP_NAME" value="" />
      <option name="TEST_OBJECT" value="METHOD" />
      <option name="PARAMETERS" value="" />
      <option name="OUTPUT_DIRECTORY" value="" />
      <option name="TEST_SEARCH_SCOPE">
        <value defaultName="moduleWithDependencies" />
      </option>
      <option name="PROPERTIES_FILE" value="" />
      <properties />
      <listeners />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="TestNG.CoreService.TC_01" />
      <item itemvalue="TestNG.Photo.TC_01" />
      <item itemvalue="TestNG.Photo.TC_02" />
      <item itemvalue="TestNG.ProductsApiTest.testCreateProduct" />
      <item itemvalue="TestNG.CoreService.TC_02" />
      <item itemvalue="TestNG.CoreService.TC_17" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="TestNG.ProductsApiTest.testCreateProduct" />
        <item itemvalue="TestNG.Photo.TC_02" />
        <item itemvalue="TestNG.Photo.TC_01" />
        <item itemvalue="TestNG.CoreService.TC_02" />
        <item itemvalue="TestNG.CoreService.TC_17" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="3d4f6578-92ee-41f4-afda-fa56b64f3ff6" name="Changes" comment="" />
      <created>1698406804935</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1698406804935</updated>
    </task>
    <task id="LOCAL-00001" summary="fist commit">
      <option name="closed" value="true" />
      <created>1700478168932</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1700478168932</updated>
    </task>
    <task id="LOCAL-00002" summary="adding logout functionality">
      <option name="closed" value="true" />
      <created>1700650460570</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1700650460570</updated>
    </task>
    <option name="localTasksCounter" value="3" />
    <servers />
  </component>
  <component name="UnknownFeatures">
    <option featureType="com.intellij.fileTypeFactory" implementationName="*.xlsx" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="ADD_EXTERNAL_FILES_SILENTLY" value="true" />
    <MESSAGE value="fist commit" />
    <MESSAGE value="adding logout functionality" />
    <option name="LAST_COMMIT_MESSAGE" value="adding logout functionality" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/test/java/utils/ExcelUtils.java</url>
          <line>53</line>
          <option name="timeStamp" value="11" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="TestNG">
        <watch expression="logger" language="JAVA" />
        <watch expression="logger" language="JAVA" />
      </configuration>
    </watches-manager>
  </component>
</project>