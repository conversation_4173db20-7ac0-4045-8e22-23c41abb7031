# ========================================
# Dynamic Test Configuration
# NO HARDCODED VALUES IN CODE - ALL HERE
# ========================================

# Excel File Configuration
excel.file.path=data/R Filings.xlsx
excel.sheet.keywords=service,api,test,core,contact,finance

# Excel Column Mappings (1-based)
excel.column.url=6
excel.column.body=7
excel.column.expected=8
excel.column.actual=9
excel.column.status=10
excel.column.defectid=11

# API Configuration
base.url=http://localhost:9762

# Authentication Configuration
auth.sheet.name=Core Service
auth.row.number=13

# Test Execution Configuration
test.start.row=2
test.parallel.threads=1
test.timeout.seconds=300

# Database Configuration
database.url=*****************************************
database.username=postgres
database.password=password
database.schema=core
database.port=5432

# Validation Configuration
validation.database.enabled=true
validation.api.response.enabled=true
validation.foreign.keys.enabled=true

# Defect Tracking Configuration
defect.tracking.enabled=true
defect.auto.assign=true
defect.include.screenshots=false
defect.include.logs=true
defect.include.request.response=true

# Dynamic Data Generation Configuration
dynamic.data.generation.enabled=true
dynamic.data.preserve.foreign.keys=true
dynamic.data.preserve.static.values=true

# Logging Configuration
log.level=INFO
log.detailed.enabled=false
log.api.requests=true
log.api.responses=true
log.database.queries=false

# Retry Configuration
retry.max.attempts=3
retry.delay.seconds=2
retry.on.failure=true

# Performance Configuration
performance.monitoring.enabled=false
performance.threshold.seconds=30

# Environment Specific Settings
environment=test
tenant.id.default={{tenantId}}
auth.token.default={{auth}}

# Test Data Configuration
test.data.cleanup.enabled=false
test.data.backup.enabled=false

# Notification Configuration
notification.email.enabled=false
notification.slack.enabled=false
notification.teams.enabled=false

# Report Configuration
report.html.enabled=true
report.excel.enabled=true
report.json.enabled=false

# Security Configuration
security.mask.sensitive.data=true
security.log.credentials=false

# ========================================
# Environment Variable Overrides
# ========================================
# You can override any property using environment variables:
# 
# EXCEL_FILE_PATH=path/to/your/excel.xlsx
# BASE_URL=http://your-api-server:8080
# EXCEL_COL_URL=6
# EXCEL_COL_BODY=7
# EXCEL_COL_EXPECTED=8
# EXCEL_COL_ACTUAL=9
# EXCEL_COL_STATUS=10
# EXCEL_COL_DEFECTID=11
# AUTH_SHEET_NAME=Your Sheet Name
# AUTH_ROW_NUMBER=13
# TEST_START_ROW=2
# DATABASE_URL=*****************************************
# DATABASE_USERNAME=your_username
# DATABASE_PASSWORD=your_password
# ========================================

# ========================================
# Usage Examples:
# ========================================
# 
# 1. Windows Environment Variables:
# set EXCEL_FILE_PATH=C:\path\to\excel.xlsx
# set BASE_URL=http://localhost:8080
# set EXCEL_COL_DEFECTID=12
# 
# 2. Linux/Mac Environment Variables:
# export EXCEL_FILE_PATH=/path/to/excel.xlsx
# export BASE_URL=http://localhost:8080
# export EXCEL_COL_DEFECTID=12
# 
# 3. Maven Command Line:
# mvn test -DEXCEL_FILE_PATH=path/to/excel.xlsx -DBASE_URL=http://localhost:8080
# 
# 4. IDE Run Configuration:
# Add environment variables in your IDE run configuration
# ========================================
