package utils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

import org.json.JSONException;
import org.json.JSONObject;

public class ValidationUtils {

    public static List<String> validateMandatoryFields(JSONObject jsonObject, List<String> mandatoryFields) throws J<PERSON>NException {
        List<String> errors = new ArrayList<>();

        for (String field : mandatoryFields) {
            if (!jsonObject.has(field) || jsonObject.get(field) == null || jsonObject.getString(field).trim().isEmpty()) {
                errors.add(field + " should not be null or empty");
            }
        }

        return errors;
    }

    public static List<String> validateEmailFormat(JSONObject jsonObject, String field) {
        List<String> errors = new ArrayList<>();
        if (jsonObject.has(field)) {
            String email = jsonObject.optString(field);
            if (!email.isEmpty() && !Pattern.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+$", email)) {
                errors.add("Invalid email format in field: " + field + " => " + email);
            }
        }
        return errors;
    }

    public static List<String> validatePhoneFormat(JSONObject jsonObject, String field) {
        List<String> errors = new ArrayList<>();
        if (jsonObject.has(field)) {
            String phone = jsonObject.optString(field);
            if (!phone.isEmpty() && !Pattern.matches("^\\+?[0-9]{10,13}$", phone)) {
                errors.add("Invalid phone number format in field: " + field + " => " + phone);
            }
        }
        return errors;
    }

    public static List<String> validateDateFormat(JSONObject jsonObject, String field, String datePattern) {
        List<String> errors = new ArrayList<>();
        if (jsonObject.has(field)) {
            String date = jsonObject.optString(field);
            if (!date.isEmpty() && !Pattern.matches(datePattern, date)) {
                errors.add("Invalid date format in field: " + field + " => " + date);
            }
        }
        return errors;
    }

    public static List<String> validateFieldEquality(JSONObject postJson, JSONObject getJson, List<String> fieldsToCompare) {
        List<String> errors = new ArrayList<>();
        for (String field : fieldsToCompare) {
            String postValue = postJson.optString(field, null);
            String getValue = getJson.optString(field, null);
            if (postValue != null && getValue != null && !postValue.equals(getValue)) {
                errors.add("Mismatch in field [" + field + "]: POST = " + postValue + ", GET = " + getValue);
            }
        }
        return errors;
    }
}
