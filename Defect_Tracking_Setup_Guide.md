# Defect Tracking Setup Guide

## 🎯 Overview
This guide shows how to configure defect tracking credentials for automatic bug creation when API tests fail.

## 📍 Configuration File Location
```
src/test/resources/defect-config.properties
```

## 🔧 Plane Bug Tracking Setup (Your Requirement)

### Step 1: Get Plane Credentials

#### 1.1 Plane URL
- Your Plane instance URL
- Example: `https://mycompany.plane.so` or `https://plane.mycompany.com`

#### 1.2 Workspace ID
1. Login to your Plane instance
2. Go to **Workspace Settings**
3. Copy the **Workspace ID** from the URL or settings page
4. Example: `ws_abc123def456`

#### 1.3 Project ID  
1. Navigate to your project in Plane
2. Go to **Project Settings**
3. Copy the **Project ID**
4. Example: `proj_xyz789abc123`

#### 1.4 API Key
1. Go to **User Settings** → **API Tokens**
2. Click **Generate New Token**
3. Give it a name like "API Testing Framework"
4. Copy the generated API key
5. Example: `plane_api_key_abc123xyz789`

### Step 2: Update Configuration File

Edit `src/test/resources/defect-config.properties`:

```properties
# Plane Configuration (Bug Tracking Application)
plane.url=https://your-actual-plane-instance.com
plane.workspace.id=your-actual-workspace-id
plane.project.id=your-actual-project-id
plane.api.key=your-actual-plane-api-key
```

### Step 3: Example Real Configuration

```properties
# Plane Configuration - REAL EXAMPLE
plane.url=https://mycompany.plane.so
plane.workspace.id=ws_abc123def456
plane.project.id=proj_xyz789abc123
plane.api.key=plane_api_key_abc123xyz789
```

## 🔧 Alternative Bug Tracking Systems

### Jira Configuration

```properties
# Jira Configuration
jira.url=https://mycompany.atlassian.net
jira.project.key=APITEST
jira.username=<EMAIL>
jira.password=your-jira-password
# OR use API token instead of password
jira.auth.token=your-jira-api-token
```

#### How to Get Jira API Token:
1. Go to https://id.atlassian.com/manage-profile/security/api-tokens
2. Click **Create API token**
3. Give it a label like "API Testing"
4. Copy the token

### Azure DevOps Configuration

```properties
# Azure DevOps Configuration
azure.url=https://dev.azure.com/myorganization
azure.project=MyProject
azure.auth.token=your-azure-personal-access-token
```

#### How to Get Azure DevOps PAT:
1. Go to Azure DevOps → User Settings → Personal Access Tokens
2. Click **New Token**
3. Select scopes: **Work Items (Read & Write)**
4. Copy the token

### Bugzilla Configuration

```properties
# Bugzilla Configuration
bugzilla.url=https://bugzilla.mycompany.com
bugzilla.product=MyProduct
bugzilla.api.key=your-bugzilla-api-key
```

## 🧪 Testing Your Configuration

### Test Defect Creation

Run this test to verify your configuration:

```bash
mvn test -Dtest=ComprehensiveApiTestSuite#testPostOperationDemo
```

### Check Logs

Look for these log messages:
```
INFO  utils.DefectTracker - Creating defect in plane system
INFO  utils.DefectTracker - Plane defect created successfully: PLANE-12345
```

### Verify in Plane

1. Login to your Plane instance
2. Go to your project
3. Check **Issues** section
4. Look for automatically created defects

## 🔒 Security Best Practices

### 1. Environment Variables (Recommended)

Instead of hardcoding credentials, use environment variables:

```properties
# Use environment variables for security
plane.url=${PLANE_URL}
plane.workspace.id=${PLANE_WORKSPACE_ID}
plane.project.id=${PLANE_PROJECT_ID}
plane.api.key=${PLANE_API_KEY}
```

Set environment variables:
```bash
# Windows
set PLANE_URL=https://mycompany.plane.so
set PLANE_WORKSPACE_ID=ws_abc123def456
set PLANE_PROJECT_ID=proj_xyz789abc123
set PLANE_API_KEY=plane_api_key_abc123xyz789

# Linux/Mac
export PLANE_URL=https://mycompany.plane.so
export PLANE_WORKSPACE_ID=ws_abc123def456
export PLANE_PROJECT_ID=proj_xyz789abc123
export PLANE_API_KEY=plane_api_key_abc123xyz789
```

### 2. Separate Config Files

Create different config files for different environments:

```
src/test/resources/defect-config-dev.properties
src/test/resources/defect-config-test.properties
src/test/resources/defect-config-prod.properties
```

### 3. Git Ignore

Add to `.gitignore` to avoid committing credentials:
```
src/test/resources/defect-config.properties
*.properties
```

## 🎯 Defect Creation Example

When a test fails, the framework automatically creates a defect like this:

### Defect Title:
```
API Test Failure: API_POST_Row_15
```

### Defect Description:
```
**API Test Case Failed**

**Endpoint:** /core/api/CountryMaster/save
**Expected Result:** Success
**Actual Result:** Status: 400 - Validation Error: countryShortName is required
**Error Details:** API validation failed for required field

**Steps to Reproduce:**
1. Execute API test for endpoint: /core/api/CountryMaster/save
2. Verify response matches expected result
3. Observe the failure

**Environment:** Test
**Generated by:** Automated API Testing Framework
```

### Defect Labels:
- `api-test-failure`
- `automated`

## 🔄 Defect Workflow

1. **Test Fails** → Framework detects failure
2. **Create Defect** → Calls Plane API to create issue
3. **Get Defect ID** → Extracts created defect ID (e.g., PLANE-12345)
4. **Update Excel** → Stores defect ID in Excel column K
5. **Log Details** → Logs defect creation for tracking

## ✅ Verification Checklist

- [ ] Plane URL is correct and accessible
- [ ] Workspace ID is valid
- [ ] Project ID exists in the workspace
- [ ] API key has proper permissions
- [ ] Test defect creation works
- [ ] Defects appear in Plane project
- [ ] Excel gets updated with defect IDs

## 🚨 Troubleshooting

### Common Issues:

1. **"No such host is known"**
   - Check Plane URL is correct
   - Verify network connectivity

2. **"Unauthorized"**
   - Verify API key is correct
   - Check API key permissions

3. **"Project not found"**
   - Verify workspace ID and project ID
   - Check user has access to project

4. **"Defect creation failed"**
   - Check Plane API documentation
   - Verify required fields are provided

### Fallback Behavior:

If defect creation fails, framework:
1. Logs the error
2. Generates fallback defect ID: `FALLBACK-20250526-103401`
3. Continues test execution
4. Updates Excel with fallback ID

**Your defect tracking is now ready! 🎉**
