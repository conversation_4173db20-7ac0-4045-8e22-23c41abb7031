package utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Database utility class for API testing framework
 * Provides database operations and validation methods
 */
public class DatabaseUtils {
    private static final Logger logger = LoggerFactory.getLogger(DatabaseUtils.class);
    private Connection connection;
    private final ObjectMapper objectMapper;
    
    public DatabaseUtils(Logger logger) {
        this.objectMapper = new ObjectMapper();
        initializeConnection();
    }
    
    /**
     * Initialize database connection
     */
    private void initializeConnection() {
        try {
            String url = ConfigManager.getDatabaseUrl();
            String username = ConfigManager.getDatabaseUsername();
            String password = ConfigManager.getDatabasePassword();
            
            connection = DriverManager.getConnection(url, username, password);
            logger.info("Database connection established successfully");
        } catch (SQLException e) {
            logger.error("Failed to establish database connection: " + e.getMessage());
            connection = null;
        }
    }
    
    /**
     * Check if table exists in database
     */
    public boolean tableExists(String tableName) {
        if (connection == null) {
            return false;
        }
        
        try {
            DatabaseMetaData metaData = connection.getMetaData();
            ResultSet resultSet = metaData.getTables(null, null, tableName, new String[]{"TABLE"});
            return resultSet.next();
        } catch (SQLException e) {
            logger.error("Error checking if table exists: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Find table with specific ID
     */
    public String findTableWithId(String id, String tableName) {
        if (connection == null) {
            return "";
        }
        
        try {
            String query = "SELECT COUNT(*) FROM " + tableName + " WHERE id = ?";
            PreparedStatement stmt = connection.prepareStatement(query);
            stmt.setString(1, id);
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next() && rs.getInt(1) > 0) {
                return tableName;
            }
        } catch (SQLException e) {
            logger.error("Error finding table with ID: " + e.getMessage());
        }
        
        return "";
    }
    
    /**
     * Get record by ID from table
     */
    public String getRecordById(String tableName, String id) {
        if (connection == null) {
            return "{}";
        }
        
        try {
            String query = "SELECT * FROM " + tableName + " WHERE id = ?";
            PreparedStatement stmt = connection.prepareStatement(query);
            stmt.setString(1, id);
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                return resultSetToJson(rs);
            }
        } catch (SQLException e) {
            logger.error("Error getting record by ID: " + e.getMessage());
        }
        
        return "{}";
    }
    
    /**
     * Get record by filter
     */
    public String getRecordByFilter(String tableName, String filterKey, String filterValue) {
        if (connection == null) {
            return "{}";
        }
        
        try {
            String query = "SELECT * FROM " + tableName + " WHERE " + filterKey + " = ?";
            PreparedStatement stmt = connection.prepareStatement(query);
            stmt.setString(1, filterValue);
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                return resultSetToJson(rs);
            }
        } catch (SQLException e) {
            logger.error("Error getting record by filter: " + e.getMessage());
        }
        
        return "{}";
    }
    
    /**
     * Get all records from table with limit
     */
    public String getAllRecords(String tableName, int limit, int offset) {
        if (connection == null) {
            return "[]";
        }
        
        try {
            String query = "SELECT * FROM " + tableName + " LIMIT ? OFFSET ?";
            PreparedStatement stmt = connection.prepareStatement(query);
            stmt.setInt(1, limit);
            stmt.setInt(2, offset);
            ResultSet rs = stmt.executeQuery();
            
            List<Map<String, Object>> records = new ArrayList<>();
            while (rs.next()) {
                records.add(resultSetToMap(rs));
            }
            
            return objectMapper.writeValueAsString(records);
        } catch (Exception e) {
            logger.error("Error getting all records: " + e.getMessage());
        }
        
        return "[]";
    }
    
    /**
     * Convert ResultSet to JSON string
     */
    private String resultSetToJson(ResultSet rs) throws SQLException {
        Map<String, Object> record = resultSetToMap(rs);
        try {
            return objectMapper.writeValueAsString(record);
        } catch (Exception e) {
            logger.error("Error converting ResultSet to JSON: " + e.getMessage());
            return "{}";
        }
    }
    
    /**
     * Convert ResultSet to Map
     */
    private Map<String, Object> resultSetToMap(ResultSet rs) throws SQLException {
        Map<String, Object> record = new HashMap<>();
        ResultSetMetaData metaData = rs.getMetaData();
        int columnCount = metaData.getColumnCount();
        
        for (int i = 1; i <= columnCount; i++) {
            String columnName = metaData.getColumnName(i);
            Object value = rs.getObject(i);
            record.put(columnName, value);
        }
        
        return record;
    }
    
    /**
     * Execute custom query
     */
    public String executeQuery(String query) {
        if (connection == null) {
            return "[]";
        }
        
        try {
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(query);
            
            List<Map<String, Object>> records = new ArrayList<>();
            while (rs.next()) {
                records.add(resultSetToMap(rs));
            }
            
            return objectMapper.writeValueAsString(records);
        } catch (Exception e) {
            logger.error("Error executing query: " + e.getMessage());
        }
        
        return "[]";
    }
    
    /**
     * Close database connection
     */
    public void closeConnection() {
        if (connection != null) {
            try {
                connection.close();
                logger.info("Database connection closed");
            } catch (SQLException e) {
                logger.error("Error closing database connection: " + e.getMessage());
            }
        }
    }
    
    /**
     * Check if connection is valid
     */
    public boolean isConnectionValid() {
        if (connection == null) {
            return false;
        }
        
        try {
            return connection.isValid(5); // 5 second timeout
        } catch (SQLException e) {
            return false;
        }
    }
}
