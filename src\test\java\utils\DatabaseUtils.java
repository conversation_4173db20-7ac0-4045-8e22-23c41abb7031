package utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;

import java.io.InputStream;
import java.sql.*;
import java.util.Date;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Utility class for database operations
 */
public class DatabaseUtils {
    private Logger logger;
    private String jdbcUrl;
    private String username;
    private String password;
    private int connectionTimeout = 30;
    private Properties properties;

    /**
     * Constructor with logger
     * @param logger Logger for logging
     */
    public DatabaseUtils(Logger logger) {
        this.logger = logger;
        loadProperties();
    }

    /**
     * Load database properties from config.properties
     */
    private void loadProperties() {
        properties = new Properties();

        // Set default values
        String defaultJdbcUrl = "******************************************";
        String defaultUsername = "postgres";
        String defaultPassword = "postgres";
        int defaultConnectionTimeout = 30;

        // Try to load from config.properties if it exists
        try {
            InputStream inputStream = getClass().getClassLoader().getResourceAsStream("config.properties");
            if (inputStream != null) {
                properties.load(inputStream);

                // Load database connection properties
                jdbcUrl = properties.getProperty("JDBC_URL", defaultJdbcUrl);
                username = properties.getProperty("JDBC_USER", defaultUsername);
                password = properties.getProperty("JDBC_PASSWORD", defaultPassword);
                String timeoutStr = properties.getProperty("JDBC_CONNECTION_TIMEOUT");
                if (timeoutStr != null && !timeoutStr.isEmpty()) {
                    try {
                        connectionTimeout = Integer.parseInt(timeoutStr);
                    } catch (NumberFormatException e) {
                        logger.warn("Invalid connection timeout value in config.properties: {}, using default", timeoutStr);
                        connectionTimeout = defaultConnectionTimeout;
                    }
                } else {
                    connectionTimeout = defaultConnectionTimeout;
                }

                logger.info("Loaded database properties from config.properties: URL={}, User={}, Timeout={}", jdbcUrl, username, connectionTimeout);
            } else {
                logger.warn("config.properties file not found, using default database properties");

                // Set default values
                jdbcUrl = defaultJdbcUrl;
                username = defaultUsername;
                password = defaultPassword;
                connectionTimeout = defaultConnectionTimeout;

                logger.info("Using default database properties: URL={}, User={}, Timeout={}", jdbcUrl, username, connectionTimeout);
            }
        } catch (Exception e) {
            logger.warn("Error loading database properties: {}, using default properties", e.getMessage());

            // Set default values
            jdbcUrl = defaultJdbcUrl;
            username = defaultUsername;
            password = defaultPassword;
            connectionTimeout = defaultConnectionTimeout;

            logger.info("Using default database properties: URL={}, User={}, Timeout={}", jdbcUrl, username, connectionTimeout);
        }
    }

    /**
     * Get a database connection
     * @return Connection object
     * @throws SQLException if connection fails
     */
    public Connection getConnection() throws SQLException {
        // Check if database properties are set
        if (jdbcUrl == null || username == null || password == null) {
            logger.warn("Database connection properties not set, using default properties");

            // Set default values (using 5432 for testing database connection failures)
            jdbcUrl = "******************************************";
            username = "postgres";
            password = "postgres";
            connectionTimeout = 30;

            logger.info("Using default database properties: URL={}, User={}, Timeout={}", jdbcUrl, username, connectionTimeout);
        }

        try {
            // Set connection properties
            Properties connectionProps = new Properties();
            connectionProps.setProperty("user", username);
            connectionProps.setProperty("password", password);
            connectionProps.setProperty("connectTimeout", String.valueOf(connectionTimeout));

            // Get connection
            Connection conn = DriverManager.getConnection(jdbcUrl, connectionProps);
            logger.info("Connected to database: {}", jdbcUrl);
            return conn;
        } catch (SQLException e) {
            logger.warn("Failed to connect to database: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * Query the database for a record by ID and table name
     * @param tableName The table name
     * @param idColumnName The ID column name
     * @param id The ID value
     * @return JSON string representation of the record
     */
    public String getRecordByIdAsJson(String tableName, String idColumnName, String id) {
        // Always use the core schema for the table
        String schema = "core";

        // Check if the table exists in the core schema
        if (!tableExists(tableName)) {
            logger.error("Table '{}' does not exist in the '{}' schema", tableName, schema);
            return "{}";
        }

        // Query the table in the core schema
        String sql = "SELECT * FROM " + schema + "." + tableName + " WHERE " + idColumnName + " = ?";

        try (Connection conn = getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            // Set parameters
            if (id.matches("\\d+")) {
                // If ID is numeric, use it as a number
                stmt.setInt(1, Integer.parseInt(id));
            } else {
                // Otherwise use it as a string
                stmt.setString(1, id);
            }

            logger.info("Executing SQL: {} with ID: {}", sql, id);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    // Convert result set to JSON
                    String json = resultSetToJson(rs);
                    logger.info("Found record in database: {}", json);
                    return json;
                } else {
                    logger.warn("No record found in database for ID: {}", id);
                    return "{}";
                }
            }
        } catch (Exception e) {
            logger.error("Error querying database: {}", e.getMessage());
            return "{}";
        }
    }

    /**
     * Query the database for records by filter key and value
     * @param tableName The table name
     * @param filterKey The filter key (column name)
     * @param filterValue The filter value
     * @return JSON string representation of the records
     */
    public String getRecordByFilter(String tableName, String filterKey, String filterValue) {
        // Always use the core schema for the table
        String schema = "core";

        // Check if the table exists in the core schema
        if (!tableExists(tableName)) {
            logger.error("Table '{}' does not exist in the '{}' schema", tableName, schema);
            return "[]";
        }

        // Convert filter key from camelCase to snake_case if needed
        String columnName = camelToSnakeCase(filterKey);

        // Query the table in the core schema
        String sql = "SELECT * FROM " + schema + "." + tableName + " WHERE " + columnName + " = ?";

        try (Connection conn = getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            // Set parameters
            if (filterValue.matches("\\d+")) {
                // If value is numeric, use it as a number
                stmt.setInt(1, Integer.parseInt(filterValue));
            } else {
                // Otherwise use it as a string
                stmt.setString(1, filterValue);
            }

            logger.info("Executing SQL: {} with filter {}={}", sql, columnName, filterValue);

            try (ResultSet rs = stmt.executeQuery()) {
                // Convert result set to JSON array
                String json = resultSetToJsonArray(rs);
                logger.info("Found records in database: {}", json);
                return json;
            }
        } catch (Exception e) {
            logger.error("Error querying database with filter: {}", e.getMessage());
            return "[]";
        }
    }

    /**
     * Query a table in a specific schema
     * @param tableName The table name
     * @param idColumnName The ID column name
     * @param id The ID value
     * @param schema The schema name
     * @return JSON string representation of the record
     */
    private String queryTableInSchema(String tableName, String idColumnName, String id, String schema) {
        try {
            String sql = "SELECT * FROM " + schema + "." + tableName + " WHERE " + idColumnName + " = ?";

            try (Connection conn = getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {

                // Set parameters
                if (id.matches("\\d+")) {
                    // If ID is numeric, use it as a number
                    stmt.setInt(1, Integer.parseInt(id));
                } else {
                    // Otherwise use it as a string
                    stmt.setString(1, id);
                }

                logger.info("Executing SQL: {} with ID: {}", sql, id);

                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        // Convert result set to JSON
                        String json = resultSetToJson(rs);
                        logger.info("Found record in database in schema {}: {}", schema, json);
                        return json;
                    } else {
                        logger.debug("No record found in schema {} for ID: {}", schema, id);
                        return "{}";
                    }
                }
            }
        } catch (Exception e) {
            logger.debug("Error querying schema {}: {}", schema, e.getMessage());
            return "{}";
        }
    }

    /**
     * Find the correct table name by checking in different schemas
     * @param originalTableName The original table name that doesn't exist
     * @param id The ID to look for
     * @return The correct table name if found, empty string otherwise
     */
    private String findCorrectTableName(String originalTableName, String id) {
        // Only use the exact table name from the Excel sheet
        // Check if the table exists in the core schema
        if (tableExistsInSchema(originalTableName, "core")) {
            logger.info("Table '{}' exists in the core schema", originalTableName);
            String fullTableName = "core." + originalTableName;

            // Check if the ID exists in this table
            if (idExistsInTable(fullTableName, id)) {
                logger.info("Found ID {} in table {}", id, fullTableName);
                return fullTableName;
            } else {
                logger.warn("Table '{}' exists in the core schema but does not contain ID {}", originalTableName, id);
            }
        } else {
            logger.warn("Table '{}' does not exist in the core schema", originalTableName);
        }

        // If not found in core schema, check in public schema
        if (tableExistsInSchema(originalTableName, "public")) {
            logger.info("Table '{}' exists in the public schema", originalTableName);
            String fullTableName = "public." + originalTableName;

            // Check if the ID exists in this table
            if (idExistsInTable(fullTableName, id)) {
                logger.info("Found ID {} in table {}", id, fullTableName);
                return fullTableName;
            } else {
                logger.warn("Table '{}' exists in the public schema but does not contain ID {}", originalTableName, id);
            }
        } else {
            logger.warn("Table '{}' does not exist in the public schema", originalTableName);
        }

        // If we still haven't found the table, return empty string
        logger.error("Table '{}' does not exist in either schema or does not contain ID {}", originalTableName, id);
        return "";
    }

    /**
     * Check if an ID exists in a table
     * @param tableName The table name
     * @param id The ID to check
     * @return True if the ID exists, false otherwise
     */
    private boolean idExistsInTable(String tableName, String id) {
        String sql = "SELECT 1 FROM " + tableName + " WHERE id = ?";

        try (Connection conn = getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            if (id.matches("\\d+")) {
                stmt.setInt(1, Integer.parseInt(id));
            } else {
                stmt.setString(1, id);
            }

            try (ResultSet rs = stmt.executeQuery()) {
                return rs.next();
            }
        } catch (Exception e) {
            logger.error("Error checking if ID {} exists in table {}: {}", id, tableName, e.getMessage());
            return false;
        }
    }

    /**
     * Find any table that contains the given ID
     * @param id The ID to look for
     * @param tableName The table name to check
     * @return The table name if found, empty string otherwise
     */
    public String findTableWithId(String id, String tableName) {
        logger.info("Checking if ID {} exists in table {}", id, tableName);

        // Only check in the core schema for the specified table
        String schema = "core";
        String fullTableName = schema + "." + tableName;

        if (tableExistsInSchema(tableName, schema)) {
            logger.info("Table '{}' exists in the '{}' schema", tableName, schema);

            // Check if the ID exists in this table
            if (idExistsInTable(fullTableName, id)) {
                logger.info("Found ID {} in table {}", id, fullTableName);
                return fullTableName;
            } else {
                logger.warn("Table '{}' exists in the '{}' schema but does not contain ID {}", tableName, schema, id);
            }
        } else {
            logger.warn("Table '{}' does not exist in the '{}' schema", tableName, schema);
        }

        // If not found, return empty string
        logger.error("Could not find ID {} in table {}", id, fullTableName);
        return "";
    }

    /**
     * Check if an ID exists in a specific column of a table
     * @param tableName The table name
     * @param columnName The column name
     * @param id The ID to check
     * @return True if the ID exists, false otherwise
     */
    private boolean idExistsInTableColumn(String tableName, String columnName, String id) {
        if (!tableExists(tableName)) {
            return false;
        }

        String sql = "SELECT 1 FROM " + tableName + " WHERE " + columnName + " = ?";

        try (Connection conn = getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            if (id.matches("\\d+")) {
                stmt.setInt(1, Integer.parseInt(id));
            } else {
                stmt.setString(1, id);
            }

            try (ResultSet rs = stmt.executeQuery()) {
                return rs.next();
            }
        } catch (Exception e) {
            logger.error("Error checking if ID {} exists in table {} column {}: {}", id, tableName, columnName, e.getMessage());
            return false;
        }
    }

    /**
     * Convert a ResultSet to a JSON string
     * @param rs ResultSet to convert
     * @return JSON string
     * @throws SQLException if ResultSet access fails
     */
    private String resultSetToJson(ResultSet rs) throws SQLException {
        ResultSetMetaData metaData = rs.getMetaData();
        int columnCount = metaData.getColumnCount();

        ObjectMapper mapper = new ObjectMapper();
        ObjectNode jsonObject = mapper.createObjectNode();

        for (int i = 1; i <= columnCount; i++) {
            String columnName = metaData.getColumnName(i);
            String camelCaseColumnName = snakeToCamelCase(columnName);

            // Handle different column types
            Object value = rs.getObject(i);
            if (value == null) {
                jsonObject.putNull(camelCaseColumnName);
            } else if (value instanceof Integer) {
                jsonObject.put(camelCaseColumnName, (Integer) value);
            } else if (value instanceof Long) {
                jsonObject.put(camelCaseColumnName, (Long) value);
            } else if (value instanceof Double) {
                jsonObject.put(camelCaseColumnName, (Double) value);
            } else if (value instanceof Boolean) {
                jsonObject.put(camelCaseColumnName, (Boolean) value);
            } else if (value instanceof Date) {
                jsonObject.put(camelCaseColumnName, value.toString());
            } else if (value instanceof Timestamp) {
                jsonObject.put(camelCaseColumnName, value.toString());
            } else {
                jsonObject.put(camelCaseColumnName, value.toString());
            }
        }

        try {
            return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(jsonObject);
        } catch (Exception e) {
            logger.error("Error converting ResultSet to JSON: {}", e.getMessage());
            return "{}";
        }
    }

    /**
     * Convert a ResultSet to a JSON array string
     * @param rs ResultSet to convert
     * @return JSON array string
     * @throws SQLException if ResultSet access fails
     */
    private String resultSetToJsonArray(ResultSet rs) throws SQLException {
        ResultSetMetaData metaData = rs.getMetaData();
        int columnCount = metaData.getColumnCount();

        ObjectMapper mapper = new ObjectMapper();
        com.fasterxml.jackson.databind.node.ArrayNode jsonArray = mapper.createArrayNode();

        while (rs.next()) {
            ObjectNode jsonObject = mapper.createObjectNode();

            for (int i = 1; i <= columnCount; i++) {
                String columnName = metaData.getColumnName(i);
                String camelCaseColumnName = snakeToCamelCase(columnName);

                // Handle different column types
                Object value = rs.getObject(i);
                if (value == null) {
                    jsonObject.putNull(camelCaseColumnName);
                } else if (value instanceof Integer) {
                    jsonObject.put(camelCaseColumnName, (Integer) value);
                } else if (value instanceof Long) {
                    jsonObject.put(camelCaseColumnName, (Long) value);
                } else if (value instanceof Double) {
                    jsonObject.put(camelCaseColumnName, (Double) value);
                } else if (value instanceof Boolean) {
                    jsonObject.put(camelCaseColumnName, (Boolean) value);
                } else if (value instanceof Date) {
                    jsonObject.put(camelCaseColumnName, value.toString());
                } else if (value instanceof Timestamp) {
                    jsonObject.put(camelCaseColumnName, value.toString());
                } else {
                    jsonObject.put(camelCaseColumnName, value.toString());
                }
            }

            jsonArray.add(jsonObject);
        }

        try {
            return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(jsonArray);
        } catch (Exception e) {
            logger.error("Error converting ResultSet to JSON array: {}", e.getMessage());
            return "[]";
        }
    }

    /**
     * Convert snake_case to camelCase
     * @param snakeCase String in snake_case
     * @return String in camelCase
     */
    public String snakeToCamelCase(String snakeCase) {
        if (snakeCase == null || snakeCase.isEmpty()) {
            return snakeCase;
        }

        // Use regex to find all occurrences of underscore followed by a character
        Pattern pattern = Pattern.compile("_([a-zA-Z])");
        Matcher matcher = pattern.matcher(snakeCase);
        StringBuffer result = new StringBuffer();

        // Replace each match with the uppercase version of the character
        while (matcher.find()) {
            matcher.appendReplacement(result, matcher.group(1).toUpperCase());
        }
        matcher.appendTail(result);

        return result.toString();
    }

    /**
     * Extract the first record from a JSON array response
     * @param jsonArrayResponse JSON array response
     * @return First record as JSON string
     */
    public String extractFirstRecordFromResponse(String jsonArrayResponse) {
        return extractRecordFromResponse(jsonArrayResponse, 0);
    }

    /**
     * Extract a record at the specified index from a JSON array response
     * @param jsonArrayResponse JSON array response
     * @param index Index of the record to extract (0-based)
     * @return Record at the specified index as JSON string
     */
    public String extractRecordFromResponse(String jsonArrayResponse, int index) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(jsonArrayResponse);

            // Check if this is an error response
            if (rootNode.has("status") && rootNode.has("error")) {
                int status = rootNode.get("status").asInt();
                if (status >= 400) {
                    logger.error("Response is an error: status={}, error={}",
                        status, rootNode.get("error").asText());

                    // Create a simple JSON object with the error information
                    ObjectNode errorNode = mapper.createObjectNode();
                    errorNode.put("error", rootNode.get("error").asText());
                    errorNode.put("status", status);
                    if (rootNode.has("message")) {
                        errorNode.put("message", rootNode.get("message").asText());
                    }
                    if (rootNode.has("path")) {
                        errorNode.put("path", rootNode.get("path").asText());
                    }

                    return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(errorNode);
                }
            }

            // Check if the response is an array
            if (rootNode.isArray()) {
                if (rootNode.size() > index) {
                    // Get the record at the specified index
                    JsonNode record = rootNode.get(index);
                    logger.info("Extracted record at index {} from response: {}", index, record.toString());
                    return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(record);
                } else {
                    logger.warn("Index {} is out of bounds for array of size {}", index, rootNode.size());
                    // If index is out of bounds, return the first record if available
                    if (rootNode.size() > 0) {
                        JsonNode record = rootNode.get(0);
                        logger.info("Using first record instead: {}", record.toString());
                        return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(record);
                    }
                }
            } else if (rootNode.has("content") && rootNode.get("content").isArray()) {
                // Handle paginated response with content field
                JsonNode contentNode = rootNode.get("content");
                if (contentNode.size() > index) {
                    JsonNode record = contentNode.get(index);
                    logger.info("Extracted record at index {} from paginated response: {}", index, record.toString());
                    return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(record);
                } else {
                    logger.warn("Index {} is out of bounds for content array of size {}", index, contentNode.size());
                    // If index is out of bounds, return the first record if available
                    if (contentNode.size() > 0) {
                        JsonNode record = contentNode.get(0);
                        logger.info("Using first record from content instead: {}", record.toString());
                        return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(record);
                    }
                }
            } else if (rootNode.has("data") && rootNode.get("data").isArray()) {
                // Handle response with data field
                JsonNode dataNode = rootNode.get("data");
                if (dataNode.size() > index) {
                    JsonNode record = dataNode.get(index);
                    logger.info("Extracted record at index {} from data field: {}", index, record.toString());
                    return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(record);
                } else {
                    logger.warn("Index {} is out of bounds for data array of size {}", index, dataNode.size());
                    // If index is out of bounds, return the first record if available
                    if (dataNode.size() > 0) {
                        JsonNode record = dataNode.get(0);
                        logger.info("Using first record from data instead: {}", record.toString());
                        return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(record);
                    }
                }
            } else if (rootNode.has("items") && rootNode.get("items").isArray()) {
                // Handle response with items field
                JsonNode itemsNode = rootNode.get("items");
                if (itemsNode.size() > index) {
                    JsonNode record = itemsNode.get(index);
                    logger.info("Extracted record at index {} from items field: {}", index, record.toString());
                    return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(record);
                } else {
                    logger.warn("Index {} is out of bounds for items array of size {}", index, itemsNode.size());
                    // If index is out of bounds, return the first record if available
                    if (itemsNode.size() > 0) {
                        JsonNode record = itemsNode.get(0);
                        logger.info("Using first record from items instead: {}", record.toString());
                        return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(record);
                    }
                }
            } else if (rootNode.has("results") && rootNode.get("results").isArray()) {
                // Handle response with results field
                JsonNode resultsNode = rootNode.get("results");
                if (resultsNode.size() > index) {
                    JsonNode record = resultsNode.get(index);
                    logger.info("Extracted record at index {} from results field: {}", index, record.toString());
                    return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(record);
                } else {
                    logger.warn("Index {} is out of bounds for results array of size {}", index, resultsNode.size());
                    // If index is out of bounds, return the first record if available
                    if (resultsNode.size() > 0) {
                        JsonNode record = resultsNode.get(0);
                        logger.info("Using first record from results instead: {}", record.toString());
                        return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(record);
                    }
                }
            } else {
                // If not an array or empty array, return as is
                logger.warn("Response is not an array or is empty: {}", jsonArrayResponse);
                return jsonArrayResponse;
            }

            // If we get here, we couldn't extract a record
            logger.error("Could not extract record at index {} from response", index);
            return "{}";
        } catch (Exception e) {
            logger.error("Error extracting record at index {} from response: {}", index, e.getMessage());
            return jsonArrayResponse;
        }
    }

    /**
     * Extract ID from a JSON object
     * @param jsonObject JSON object
     * @return ID as string
     */
    public String extractIdFromJson(String jsonObject) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(jsonObject);

            // Try common ID field names
            String[] idFields = {"id", "ID", "Id", "_id", "uuid", "key"};
            for (String field : idFields) {
                if (rootNode.has(field)) {
                    String id = rootNode.get(field).asText();
                    logger.info("Extracted ID '{}' from field '{}'", id, field);
                    return id;
                }
            }

            logger.warn("No ID field found in JSON: {}", jsonObject);
            return "";
        } catch (Exception e) {
            logger.error("Error extracting ID from JSON: {}", e.getMessage());
            return "";
        }
    }

    /**
     * Infer table name from endpoint
     * @param endpoint API endpoint
     * @return Table name
     */
    public String inferTableNameFromEndpoint(String endpoint) {
        if (endpoint == null || endpoint.isEmpty()) {
            return "";
        }

        // Extract the entity name from the endpoint
        // Format: /service/api/EntityName/operation
        String[] parts = endpoint.split("/");
        String entityName = "";

        for (int i = 0; i < parts.length; i++) {
            if (i > 0 && parts[i-1].equalsIgnoreCase("api") && i < parts.length - 1) {
                entityName = parts[i];
                break;
            }
        }

        if (entityName.isEmpty() && parts.length > 2) {
            // Try the second-to-last segment if we couldn't find it after "api"
            entityName = parts[parts.length - 2];
        }

        // Try different table name formats
        String[] possibleTableNames = {
            entityName.toLowerCase(),                  // countrymaster
            camelToSnakeCase(entityName),              // country_master
            entityName.toLowerCase() + "_master",      // countrymaster_master
            camelToSnakeCase(entityName) + "_master",  // country_master_master
            "master_" + entityName.toLowerCase(),      // master_countrymaster
            "master_" + camelToSnakeCase(entityName),  // master_country_master
            entityName.toLowerCase().replace("master", ""), // country (if entityName was CountryMaster)
            camelToSnakeCase(entityName).replace("_master", "") // country (if entityName was CountryMaster)
        };

        // Try to find a table that exists
        for (String tableName : possibleTableNames) {
            if (tableExists(tableName)) {
                logger.info("Found existing table '{}' for entity '{}'", tableName, entityName);
                return tableName;
            }
        }

        // If we couldn't find an existing table, use a default format
        String defaultTableName = camelToSnakeCase(entityName).replace("_master", "");
        logger.warn("Could not find existing table for entity '{}', using default table name: {}",
            entityName, defaultTableName);

        return defaultTableName;
    }

    /**
     * Check if a table exists in the database
     * @param tableName Table name to check
     * @return True if the table exists, false otherwise
     */
    public boolean tableExists(String tableName) {
        if (tableName == null || tableName.isEmpty()) {
            return false;
        }

        // Only check in the core schema as specified
        boolean exists = tableExistsInSchema(tableName, "core");

        if (exists) {
            logger.info("Table '{}' exists in the core schema", tableName);
        } else {
            logger.warn("Table '{}' does not exist in the core schema", tableName);
        }

        return exists;
    }

    /**
     * Check if a table exists in a specific schema
     * @param tableName Table name to check
     * @param schemaName Schema name to check
     * @return True if the table exists in the schema, false otherwise
     */
    private boolean tableExistsInSchema(String tableName, String schemaName) {
        if (tableName == null || tableName.isEmpty() || schemaName == null || schemaName.isEmpty()) {
            return false;
        }

        // First try using information_schema
        String sql = "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = ? AND table_name = ?)";

        try (Connection conn = getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, schemaName);
            stmt.setString(2, tableName);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    boolean exists = rs.getBoolean(1);
                    if (exists) {
                        logger.info("Table '{}' exists in the '{}' schema", tableName, schemaName);
                        return true;
                    } else {
                        logger.debug("Table '{}' does not exist in the '{}' schema according to information_schema", tableName, schemaName);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Error checking if table '{}' exists in schema '{}' using information_schema: {}", tableName, schemaName, e.getMessage());
        }

        // If information_schema query fails, try direct query
        try (Connection conn = getConnection();
             Statement stmt = conn.createStatement()) {

            // Try to execute a simple query that won't return any data
            String directSql = "SELECT 1 FROM " + schemaName + "." + tableName + " WHERE 1=0";
            stmt.executeQuery(directSql);

            // If we get here, the table exists
            logger.info("Table '{}' exists in the '{}' schema (verified by direct query)", tableName, schemaName);
            return true;
        } catch (Exception e) {
            logger.debug("Table '{}' does not exist in the '{}' schema (verified by direct query): {}", tableName, schemaName, e.getMessage());
        }

        // If we get here, the table doesn't exist
        logger.warn("Table '{}' does not exist in the '{}' schema", tableName, schemaName);
        return false;
    }

    /**
     * Convert camelCase to snake_case
     * @param camelCase String in camelCase
     * @return String in snake_case
     */
    public String camelToSnakeCase(String camelCase) {
        if (camelCase == null || camelCase.isEmpty()) {
            return camelCase;
        }

        // Replace capital letters with underscore + lowercase letter
        String result = camelCase.replaceAll("([A-Z])", "_$1").toLowerCase();

        // Remove "master" suffix if present
        if (result.endsWith("_master")) {
            result = result.substring(0, result.length() - 7);
        }

        return result;
    }
}