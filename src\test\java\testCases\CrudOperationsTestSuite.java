package testCases;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.annotations.*;
import utils.TestConfiguration;
import utils.ExcelUtils;
import utils.DatabaseValidationUtils;
import utils.DefectTracker;

/**
 * Comprehensive CRUD Operations Test Suite
 * Executes Photos and Products API tests with database validation
 */
@Test
public class CrudOperationsTestSuite {
    private static final Logger logger = LoggerFactory.getLogger(CrudOperationsTestSuite.class);
    
    private TestConfiguration config;
    private ExcelUtils excelUtils;
    private DatabaseValidationUtils dbUtils;
    private DefectTracker defectTracker;
    
    @BeforeSuite
    public void suiteSetup() {
        logger.info("=== CRUD Operations Test Suite Setup ===");
        
        // Initialize configuration
        config = TestConfiguration.getInstance();
        config.displayConfiguration();
        
        // Validate configuration
        if (!config.validateConfiguration()) {
            throw new RuntimeException("Invalid test configuration. Please check the configuration file.");
        }
        
        // Initialize utilities
        excelUtils = new ExcelUtils();
        dbUtils = new DatabaseValidationUtils();
        defectTracker = new DefectTracker();
        
        // Reset defect counters for clean test run
        DefectTracker.resetAllDefectCounters();
        
        logger.info("Test suite setup completed successfully");
    }
    
    @BeforeTest
    public void testSetup() {
        logger.info("=== Test Setup ===");
        
        // Verify Excel file accessibility
        String excelPath = config.getExcelFilePath();
        if (!verifyExcelFile(excelPath)) {
            throw new RuntimeException("Excel file not accessible: " + excelPath);
        }
        
        // Verify database connectivity if enabled
        if (config.isDatabaseValidationEnabled()) {
            if (!verifyDatabaseConnectivity()) {
                logger.warn("Database connectivity issues detected. Database validation will be skipped.");
            }
        }
        
        logger.info("Test setup completed");
    }
    
    @Test(priority = 1, groups = {"photos", "crud", "post"})
    public void executePhotosApiTests() {
        logger.info("=== Executing Photos API Tests ===");
        
        try {
            PhotosApiTest photosTest = new PhotosApiTest();
            photosTest.setup();
            
            // Execute all Photos API test methods
            photosTest.testCreatePhoto();
            photosTest.testUpdatePhoto();
            photosTest.testGetAllPhotos();
            photosTest.testGetPhotoById();
            photosTest.testDeletePhoto();
            
            logger.info("Photos API tests completed successfully");
            
        } catch (Exception e) {
            logger.error("Error executing Photos API tests: " + e.getMessage());
            throw new RuntimeException("Photos API tests failed", e);
        }
    }
    
    @Test(priority = 2, groups = {"products", "crud", "post"})
    public void executeProductsApiTests() {
        logger.info("=== Executing Products API Tests ===");
        
        try {
            ProductsApiTest productsTest = new ProductsApiTest();
            productsTest.setup();
            
            // Execute all Products API test methods
            productsTest.testCreateProduct();
            productsTest.testUpdateProduct();
            productsTest.testGetAllProducts();
            productsTest.testGetProductById();
            productsTest.testDeleteProduct();
            
            logger.info("Products API tests completed successfully");
            
        } catch (Exception e) {
            logger.error("Error executing Products API tests: " + e.getMessage());
            throw new RuntimeException("Products API tests failed", e);
        }
    }
    
    @Test(priority = 3, groups = {"validation", "database"}, dependsOnGroups = {"photos", "products"})
    public void validateDatabaseConsistency() {
        logger.info("=== Validating Database Consistency ===");
        
        if (!config.isDatabaseValidationEnabled()) {
            logger.info("Database validation is disabled. Skipping consistency check.");
            return;
        }
        
        try {
            // Validate Photos table consistency
            validatePhotosTableConsistency();
            
            // Validate Products table consistency
            validateProductsTableConsistency();
            
            logger.info("Database consistency validation completed");
            
        } catch (Exception e) {
            logger.error("Database consistency validation failed: " + e.getMessage());
            // Don't fail the entire suite for database validation issues
        }
    }
    
    @AfterTest
    public void testCleanup() {
        logger.info("=== Test Cleanup ===");
        
        try {
            // Generate test summary report
            generateTestSummaryReport();
            
            // Clean up test data if needed
            cleanupTestData();
            
            logger.info("Test cleanup completed");
            
        } catch (Exception e) {
            logger.error("Error during test cleanup: " + e.getMessage());
        }
    }
    
    @AfterSuite
    public void suiteCleanup() {
        logger.info("=== Test Suite Cleanup ===");
        
        try {
            // Generate final defect report
            generateDefectReport();
            
            // Close database connections
            if (dbUtils != null) {
                // Close any open connections
            }
            
            logger.info("Test suite cleanup completed");
            
        } catch (Exception e) {
            logger.error("Error during suite cleanup: " + e.getMessage());
        }
    }
    
    /**
     * Verify Excel file accessibility
     */
    private boolean verifyExcelFile(String excelPath) {
        try {
            // Check if file exists and is readable
            java.io.File file = new java.io.File(excelPath);
            if (!file.exists()) {
                logger.error("Excel file does not exist: {}", excelPath);
                return false;
            }
            
            if (!file.canRead()) {
                logger.error("Excel file is not readable: {}", excelPath);
                return false;
            }
            
            // Try to read a cell to verify file format
            String testValue = excelUtils.getCellData(excelPath, "Photos", 1, 1);
            logger.info("Excel file verification successful. Test read: {}", testValue);
            
            return true;
            
        } catch (Exception e) {
            logger.error("Excel file verification failed: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Verify database connectivity
     */
    private boolean verifyDatabaseConnectivity() {
        try {
            // Test database connection
            String testResult = dbUtils.getAllRecordsFromTable("photos", 1, 0);
            logger.info("Database connectivity verified. Test query result: {}", testResult);
            return true;
            
        } catch (Exception e) {
            logger.error("Database connectivity verification failed: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Validate Photos table consistency
     */
    private void validatePhotosTableConsistency() {
        logger.info("Validating Photos table consistency...");
        
        try {
            // Get sample records from database
            String dbRecords = dbUtils.getAllRecordsFromTable("photos", 5, 0);
            
            if (!"[]".equals(dbRecords)) {
                logger.info("Photos table contains data. Consistency check passed.");
            } else {
                logger.warn("Photos table appears to be empty.");
            }
            
        } catch (Exception e) {
            logger.error("Photos table consistency validation failed: " + e.getMessage());
        }
    }
    
    /**
     * Validate Products table consistency
     */
    private void validateProductsTableConsistency() {
        logger.info("Validating Products table consistency...");
        
        try {
            // Get sample records from database
            String dbRecords = dbUtils.getAllRecordsFromTable("products", 5, 0);
            
            if (!"[]".equals(dbRecords)) {
                logger.info("Products table contains data. Consistency check passed.");
            } else {
                logger.warn("Products table appears to be empty.");
            }
            
        } catch (Exception e) {
            logger.error("Products table consistency validation failed: " + e.getMessage());
        }
    }
    
    /**
     * Generate test summary report
     */
    private void generateTestSummaryReport() {
        logger.info("=== Test Summary Report ===");
        
        try {
            // Count defects generated for Photos
            int photosDefectCount = DefectTracker.getCurrentDefectCount("PHOTO");
            logger.info("Photos API Tests - Defects Generated: {}", photosDefectCount);
            
            // Count defects generated for Products
            int productsDefectCount = DefectTracker.getCurrentDefectCount("PRODUCT");
            logger.info("Products API Tests - Defects Generated: {}", productsDefectCount);
            
            // Total defects
            int totalDefects = photosDefectCount + productsDefectCount;
            logger.info("Total Defects Generated: {}", totalDefects);
            
            if (totalDefects == 0) {
                logger.info("🎉 All tests passed successfully! No defects generated.");
            } else {
                logger.warn("⚠️ {} defects were generated during test execution.", totalDefects);
            }
            
        } catch (Exception e) {
            logger.error("Error generating test summary report: " + e.getMessage());
        }
    }
    
    /**
     * Generate defect report
     */
    private void generateDefectReport() {
        logger.info("=== Defect Report ===");
        
        try {
            if (config.isDefectTrackingEnabled()) {
                logger.info("Defect tracking is enabled. Check Excel sheets for defect IDs.");
                logger.info("Defect format: D_TableName_001, D_TableName_002, etc.");
            } else {
                logger.info("Defect tracking is disabled.");
            }
            
        } catch (Exception e) {
            logger.error("Error generating defect report: " + e.getMessage());
        }
    }
    
    /**
     * Clean up test data
     */
    private void cleanupTestData() {
        logger.info("Cleaning up test data...");
        
        try {
            // Here you could add logic to clean up test data created during the test run
            // For example, delete test records from database
            
            logger.info("Test data cleanup completed");
            
        } catch (Exception e) {
            logger.error("Error during test data cleanup: " + e.getMessage());
        }
    }
}
