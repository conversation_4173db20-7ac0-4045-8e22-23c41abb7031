package utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * Configuration manager for the application
 */
public class ConfigManager {
    private static final Logger logger = LoggerFactory.getLogger(ConfigManager.class);
    private static Properties properties = new Properties();
    private static boolean isInitialized = false;

    /**
     * Initialize the configuration manager
     */
    public static void init() {
        if (isInitialized) {
            return;
        }

        try (InputStream input = new FileInputStream(Constants.CONFIG_FILE)) {
            properties.load(input);
            isInitialized = true;
            logger.info("Configuration loaded from {}", Constants.CONFIG_FILE);
        } catch (IOException e) {
            logger.warn("Could not load configuration from {}: {}", Constants.CONFIG_FILE, e.getMessage());
            // Load default values
            loadDefaultValues();
        }
    }

    /**
     * Load default values
     */
    private static void loadDefaultValues() {
        properties.setProperty(Constants.PROP_DEFAULT_ID, Constants.DEFAULT_ID);
        properties.setProperty(Constants.PROP_DEFAULT_ENTITY, Constants.DEFAULT_ENTITY_NAME);
        properties.setProperty(Constants.PROP_API_BASE_PATH, Constants.API_BASE_PATH);
        logger.info("Loaded default configuration values");
    }

    /**
     * Get a property value
     * @param key Property key
     * @return Property value
     */
    public static String getProperty(String key) {
        if (!isInitialized) {
            init();
        }
        return properties.getProperty(key);
    }

    /**
     * Get a property value with a default value
     * @param key Property key
     * @param defaultValue Default value
     * @return Property value
     */
    public static String getProperty(String key, String defaultValue) {
        if (!isInitialized) {
            init();
        }
        return properties.getProperty(key, defaultValue);
    }

    /**
     * Get the default ID
     * @return Default ID
     */
    public static String getDefaultId() {
        return getProperty(Constants.PROP_DEFAULT_ID, Constants.DEFAULT_ID);
    }

    /**
     * Get the default entity name
     * @return Default entity name
     */
    public static String getDefaultEntityName() {
        return getProperty(Constants.PROP_DEFAULT_ENTITY, Constants.DEFAULT_ENTITY_NAME);
    }

    /**
     * Get the API base path
     * @return API base path
     */
    public static String getApiBasePath() {
        return getProperty(Constants.PROP_API_BASE_PATH, Constants.API_BASE_PATH);
    }
}
