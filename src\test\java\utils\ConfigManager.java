package utils;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.Properties;

/**
 * Configuration Manager for API testing framework
 * Provides access to configuration properties
 */
public class ConfigManager {
    private static Properties properties;
    private static final String CONFIG_FILE_PATH = "crud-config.properties";
    
    static {
        loadProperties();
    }
    
    /**
     * Load properties from configuration file
     */
    private static void loadProperties() {
        properties = new Properties();
        try {
            // Try to load from classpath first
            try (FileInputStream fis = new FileInputStream(CONFIG_FILE_PATH)) {
                properties.load(fis);
            } catch (IOException e) {
                // If file not found, use default values
                setDefaultProperties();
            }
        } catch (Exception e) {
            setDefaultProperties();
        }
    }
    
    /**
     * Set default properties if config file is not found
     */
    private static void setDefaultProperties() {
        properties.setProperty("base.url", "http://localhost:8080");
        properties.setProperty("excel.file.path", "data/SnackHack.xlsx");
        properties.setProperty("database.url", "*************************************");
        properties.setProperty("database.username", "root");
        properties.setProperty("database.password", "password");
        properties.setProperty("api.base.path", "/api/");
        properties.setProperty("default.entity.name", "photos");
    }
    
    /**
     * Get base URL for API
     */
    public static String getBaseUrl() {
        return properties.getProperty("base.url", "http://localhost:8080");
    }
    
    /**
     * Get Excel file path
     */
    public static String getExcelFilePath() {
        return properties.getProperty("excel.file.path", "data/SnackHack.xlsx");
    }
    
    /**
     * Get database URL
     */
    public static String getDatabaseUrl() {
        return properties.getProperty("database.url", "*************************************");
    }
    
    /**
     * Get database username
     */
    public static String getDatabaseUsername() {
        return properties.getProperty("database.username", "root");
    }
    
    /**
     * Get database password
     */
    public static String getDatabasePassword() {
        return properties.getProperty("database.password", "password");
    }
    
    /**
     * Get API base path
     */
    public static String getApiBasePath() {
        return properties.getProperty("api.base.path", "/api/");
    }
    
    /**
     * Get default entity name
     */
    public static String getDefaultEntityName() {
        return properties.getProperty("default.entity.name", "photos");
    }
    
    /**
     * Get property value by key
     */
    public static String getProperty(String key) {
        return properties.getProperty(key);
    }
    
    /**
     * Get property value by key with default value
     */
    public static String getProperty(String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }
    
    /**
     * Get boolean property value
     */
    public static boolean getBooleanProperty(String key, boolean defaultValue) {
        String value = properties.getProperty(key);
        if (value == null) {
            return defaultValue;
        }
        return Boolean.parseBoolean(value);
    }
    
    /**
     * Get integer property value
     */
    public static int getIntProperty(String key, int defaultValue) {
        String value = properties.getProperty(key);
        if (value == null) {
            return defaultValue;
        }
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }
    
    /**
     * Check if database validation is enabled
     */
    public static boolean isDatabaseValidationEnabled() {
        return getBooleanProperty("database.validation.enabled", true);
    }
    
    /**
     * Check if defect tracking is enabled
     */
    public static boolean isDefectTrackingEnabled() {
        return getBooleanProperty("defect.tracking.enabled", true);
    }
    
    /**
     * Get request timeout in seconds
     */
    public static int getRequestTimeout() {
        return getIntProperty("request.timeout", 30);
    }
    
    /**
     * Get retry count
     */
    public static int getRetryCount() {
        return getIntProperty("retry.count", 3);
    }
}
