package utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * Dynamic Test Configuration - NO HARDCODED VALUES
 * All configuration comes from properties files or environment variables
 */
public class TestConfiguration {
    private static final Logger logger = LoggerFactory.getLogger(TestConfiguration.class);
    private static final String CONFIG_FILE = "test-config.properties";
    
    private static TestConfiguration instance;
    private final Properties properties;
    
    private TestConfiguration() {
        this.properties = new Properties();
        loadConfiguration();
    }
    
    public static TestConfiguration getInstance() {
        if (instance == null) {
            synchronized (TestConfiguration.class) {
                if (instance == null) {
                    instance = new TestConfiguration();
                }
            }
        }
        return instance;
    }
    
    private void loadConfiguration() {
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(CONFIG_FILE)) {
            if (inputStream == null) {
                logger.warn("Configuration file '{}' not found, using environment variables", CONFIG_FILE);
                loadFromEnvironment();
                return;
            }
            
            properties.load(inputStream);
            logger.info("Loaded test configuration from {}", CONFIG_FILE);
            
            // Override with environment variables if present
            overrideWithEnvironmentVariables();
            
        } catch (IOException e) {
            logger.error("Error loading test configuration: {}", e.getMessage());
            loadFromEnvironment();
        }
    }
    
    private void loadFromEnvironment() {
        // Load from environment variables as fallback
        setPropertyFromEnv("excel.file.path", "EXCEL_FILE_PATH", "data/R Filings.xlsx");
        setPropertyFromEnv("base.url", "BASE_URL", "http://localhost:9762");
        setPropertyFromEnv("auth.sheet.name", "AUTH_SHEET_NAME", "Core Service");
        setPropertyFromEnv("auth.row.number", "AUTH_ROW_NUMBER", "13");
        
        // Column mappings
        setPropertyFromEnv("excel.column.url", "EXCEL_COL_URL", "6");
        setPropertyFromEnv("excel.column.body", "EXCEL_COL_BODY", "7");
        setPropertyFromEnv("excel.column.expected", "EXCEL_COL_EXPECTED", "8");
        setPropertyFromEnv("excel.column.actual", "EXCEL_COL_ACTUAL", "9");
        setPropertyFromEnv("excel.column.status", "EXCEL_COL_STATUS", "10");
        setPropertyFromEnv("excel.column.defectid", "EXCEL_COL_DEFECTID", "11");
        
        // Test execution settings
        setPropertyFromEnv("test.start.row", "TEST_START_ROW", "2");
        setPropertyFromEnv("test.parallel.threads", "TEST_PARALLEL_THREADS", "1");
        setPropertyFromEnv("test.timeout.seconds", "TEST_TIMEOUT_SECONDS", "300");
        
        logger.info("Loaded configuration from environment variables");
    }
    
    private void overrideWithEnvironmentVariables() {
        // Override properties with environment variables if they exist
        overrideIfEnvExists("excel.file.path", "EXCEL_FILE_PATH");
        overrideIfEnvExists("base.url", "BASE_URL");
        overrideIfEnvExists("auth.sheet.name", "AUTH_SHEET_NAME");
        overrideIfEnvExists("auth.row.number", "AUTH_ROW_NUMBER");
        
        overrideIfEnvExists("excel.column.url", "EXCEL_COL_URL");
        overrideIfEnvExists("excel.column.body", "EXCEL_COL_BODY");
        overrideIfEnvExists("excel.column.expected", "EXCEL_COL_EXPECTED");
        overrideIfEnvExists("excel.column.actual", "EXCEL_COL_ACTUAL");
        overrideIfEnvExists("excel.column.status", "EXCEL_COL_STATUS");
        overrideIfEnvExists("excel.column.defectid", "EXCEL_COL_DEFECTID");
        
        overrideIfEnvExists("test.start.row", "TEST_START_ROW");
        overrideIfEnvExists("test.parallel.threads", "TEST_PARALLEL_THREADS");
        overrideIfEnvExists("test.timeout.seconds", "TEST_TIMEOUT_SECONDS");
    }
    
    private void setPropertyFromEnv(String propertyKey, String envKey, String defaultValue) {
        String envValue = System.getenv(envKey);
        properties.setProperty(propertyKey, envValue != null ? envValue : defaultValue);
    }
    
    private void overrideIfEnvExists(String propertyKey, String envKey) {
        String envValue = System.getenv(envKey);
        if (envValue != null) {
            properties.setProperty(propertyKey, envValue);
            logger.debug("Overridden {} with environment variable {}", propertyKey, envKey);
        }
    }
    
    // Excel Configuration
    public String getExcelFilePath() {
        return properties.getProperty("excel.file.path");
    }
    
    public int getUrlColumn() {
        return Integer.parseInt(properties.getProperty("excel.column.url"));
    }
    
    public int getBodyColumn() {
        return Integer.parseInt(properties.getProperty("excel.column.body"));
    }
    
    public int getExpectedResultColumn() {
        return Integer.parseInt(properties.getProperty("excel.column.expected"));
    }
    
    public int getActualResultColumn() {
        return Integer.parseInt(properties.getProperty("excel.column.actual"));
    }
    
    public int getStatusColumn() {
        return Integer.parseInt(properties.getProperty("excel.column.status"));
    }
    
    public int getDefectIdColumn() {
        return Integer.parseInt(properties.getProperty("excel.column.defectid"));
    }
    
    // API Configuration
    public String getBaseUrl() {
        return properties.getProperty("base.url");
    }
    
    // Authentication Configuration
    public String getAuthSheetName() {
        return properties.getProperty("auth.sheet.name");
    }
    
    public int getAuthRowNumber() {
        return Integer.parseInt(properties.getProperty("auth.row.number"));
    }
    
    // Test Execution Configuration
    public int getTestStartRow() {
        return Integer.parseInt(properties.getProperty("test.start.row"));
    }
    
    public int getParallelThreads() {
        return Integer.parseInt(properties.getProperty("test.parallel.threads"));
    }
    
    public int getTestTimeoutSeconds() {
        return Integer.parseInt(properties.getProperty("test.timeout.seconds"));
    }
    
    // Sheet Detection Configuration
    public String[] getTestSheetKeywords() {
        String keywords = properties.getProperty("test.sheet.keywords", "service,api,test,core,contact,finance");
        return keywords.split(",");
    }
    
    // Database Configuration
    public String getDatabaseUrl() {
        return properties.getProperty("database.url", "*****************************************");
    }
    
    public String getDatabaseUsername() {
        return properties.getProperty("database.username", "postgres");
    }
    
    public String getDatabasePassword() {
        return properties.getProperty("database.password", "password");
    }
    
    public String getDatabaseSchema() {
        return properties.getProperty("database.schema", "core");
    }
    
    // Validation Configuration
    public boolean isDatabaseValidationEnabled() {
        return Boolean.parseBoolean(properties.getProperty("validation.database.enabled", "true"));
    }
    
    public boolean isDefectTrackingEnabled() {
        return Boolean.parseBoolean(properties.getProperty("defect.tracking.enabled", "true"));
    }
    
    public boolean isDynamicDataGenerationEnabled() {
        return Boolean.parseBoolean(properties.getProperty("dynamic.data.generation.enabled", "true"));
    }
    
    // Logging Configuration
    public String getLogLevel() {
        return properties.getProperty("log.level", "INFO");
    }
    
    public boolean isDetailedLoggingEnabled() {
        return Boolean.parseBoolean(properties.getProperty("log.detailed.enabled", "false"));
    }
    
    // Generic property getter
    public String getProperty(String key) {
        return properties.getProperty(key);
    }
    
    public String getProperty(String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }
    
    public int getIntProperty(String key, int defaultValue) {
        try {
            return Integer.parseInt(properties.getProperty(key, String.valueOf(defaultValue)));
        } catch (NumberFormatException e) {
            logger.warn("Invalid integer value for property {}, using default: {}", key, defaultValue);
            return defaultValue;
        }
    }
    
    public boolean getBooleanProperty(String key, boolean defaultValue) {
        return Boolean.parseBoolean(properties.getProperty(key, String.valueOf(defaultValue)));
    }
    
    // Configuration validation
    public boolean validateConfiguration() {
        boolean isValid = true;
        
        // Validate required properties
        if (getExcelFilePath() == null || getExcelFilePath().trim().isEmpty()) {
            logger.error("Excel file path is not configured");
            isValid = false;
        }
        
        if (getBaseUrl() == null || getBaseUrl().trim().isEmpty()) {
            logger.error("Base URL is not configured");
            isValid = false;
        }
        
        // Validate column numbers are positive
        if (getUrlColumn() <= 0 || getBodyColumn() <= 0 || getStatusColumn() <= 0) {
            logger.error("Invalid column configuration - columns must be positive numbers");
            isValid = false;
        }
        
        return isValid;
    }
    
    // Display current configuration
    public void displayConfiguration() {
        logger.info("=== Test Configuration ===");
        logger.info("Excel File: {}", getExcelFilePath());
        logger.info("Base URL: {}", getBaseUrl());
        logger.info("Auth Sheet: {}", getAuthSheetName());
        logger.info("Auth Row: {}", getAuthRowNumber());
        logger.info("Columns - URL: {}, Body: {}, Expected: {}, Actual: {}, Status: {}, DefectID: {}", 
                   getUrlColumn(), getBodyColumn(), getExpectedResultColumn(), 
                   getActualResultColumn(), getStatusColumn(), getDefectIdColumn());
        logger.info("Test Start Row: {}", getTestStartRow());
        logger.info("Database Validation: {}", isDatabaseValidationEnabled());
        logger.info("Defect Tracking: {}", isDefectTrackingEnabled());
        logger.info("Dynamic Data Generation: {}", isDynamicDataGenerationEnabled());
    }
}
