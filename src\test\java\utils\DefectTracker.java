package utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Defect Tracker integration for bug tracking applications
 * Supports multiple bug tracking systems (Jira, Azure DevOps, Bugzilla, etc.)
 */
public class DefectTracker {
    private static final Logger logger = LoggerFactory.getLogger(DefectTracker.class);
    private final ObjectMapper mapper;
    private final DefectConfig config;

    // Thread-safe counter for each table to ensure unique defect IDs
    private static final ConcurrentHashMap<String, AtomicInteger> defectCounters = new ConcurrentHashMap<>();

    public DefectTracker() {
        this.mapper = new ObjectMapper();
        this.config = DefectConfig.getInstance();
    }

    /**
     * Create defect in configured bug tracking system
     */
    public String createDefect(DefectDetails defect) {
        try {
            String bugTrackingSystem = config.getBugTrackingSystem();
            logger.info("Creating defect in {} system", bugTrackingSystem);

            switch (bugTrackingSystem.toLowerCase()) {
                case "jira":
                    return createJiraDefect(defect);
                case "azure":
                case "azuredevops":
                    return createAzureDevOpsDefect(defect);
                case "bugzilla":
                    return createBugzillaDefect(defect);
                case "plane":
                    return createPlaneDefect(defect);
                case "mock":
                default:
                    return createMockDefect(defect);
            }

        } catch (Exception e) {
            logger.error("Error creating defect: {}", e.getMessage());
            return generateTableBasedDefectId(defect);
        }
    }

    /**
     * Create defect in Jira
     */
    private String createJiraDefect(DefectDetails defect) {
        try {
            Map<String, Object> jiraPayload = new HashMap<>();
            Map<String, Object> fields = new HashMap<>();

            // Jira required fields
            fields.put("project", Map.of("key", config.getJiraProjectKey()));
            fields.put("summary", defect.getTitle());
            fields.put("description", defect.getDescription());
            fields.put("issuetype", Map.of("name", "Bug"));
            fields.put("priority", Map.of("name", defect.getPriority()));

            // Custom fields
            if (defect.getComponent() != null) {
                fields.put("components", new Object[]{Map.of("name", defect.getComponent())});
            }

            jiraPayload.put("fields", fields);

            Response response = RestAssured.given()
                .header("Authorization", "Basic " + config.getJiraAuthToken())
                .header("Content-Type", "application/json")
                .body(mapper.writeValueAsString(jiraPayload))
                .post(config.getJiraUrl() + "/rest/api/2/issue");

            if (response.getStatusCode() == 201) {
                String responseBody = response.getBody().asString();
                String defectId = mapper.readTree(responseBody).get("key").asText();
                logger.info("Jira defect created successfully: {}", defectId);
                return defectId;
            } else {
                logger.error("Failed to create Jira defect: {}", response.getBody().asString());
                return generateTableBasedDefectId(defect);
            }

        } catch (Exception e) {
            logger.error("Error creating Jira defect: {}", e.getMessage());
            return generateTableBasedDefectId(defect);
        }
    }

    /**
     * Create defect in Azure DevOps
     */
    private String createAzureDevOpsDefect(DefectDetails defect) {
        try {
            // Azure DevOps work item creation
            Map<String, Object> azurePayload = new HashMap<>();
            azurePayload.put("op", "add");
            azurePayload.put("path", "/fields/System.Title");
            azurePayload.put("value", defect.getTitle());

            // Add more fields as needed
            Object[] operations = {
                Map.of("op", "add", "path", "/fields/System.Title", "value", defect.getTitle()),
                Map.of("op", "add", "path", "/fields/System.Description", "value", defect.getDescription()),
                Map.of("op", "add", "path", "/fields/Microsoft.VSTS.Common.Severity", "value", defect.getSeverity()),
                Map.of("op", "add", "path", "/fields/Microsoft.VSTS.Common.Priority", "value", defect.getPriority())
            };

            Response response = RestAssured.given()
                .header("Authorization", "Basic " + config.getAzureAuthToken())
                .header("Content-Type", "application/json-patch+json")
                .body(mapper.writeValueAsString(operations))
                .post(config.getAzureUrl() + "/" + config.getAzureProject() + "/_apis/wit/workitems/$Bug?api-version=6.0");

            if (response.getStatusCode() == 200) {
                String responseBody = response.getBody().asString();
                String defectId = mapper.readTree(responseBody).get("id").asText();
                logger.info("Azure DevOps defect created successfully: {}", defectId);
                return "ADO-" + defectId;
            } else {
                logger.error("Failed to create Azure DevOps defect: {}", response.getBody().asString());
                return generateTableBasedDefectId(defect);
            }

        } catch (Exception e) {
            logger.error("Error creating Azure DevOps defect: {}", e.getMessage());
            return generateTableBasedDefectId(defect);
        }
    }

    /**
     * Create defect in Bugzilla
     */
    private String createBugzillaDefect(DefectDetails defect) {
        try {
            Map<String, Object> bugzillaPayload = new HashMap<>();
            bugzillaPayload.put("product", config.getBugzillaProduct());
            bugzillaPayload.put("component", defect.getComponent());
            bugzillaPayload.put("summary", defect.getTitle());
            bugzillaPayload.put("description", defect.getDescription());
            bugzillaPayload.put("severity", defect.getSeverity());
            bugzillaPayload.put("priority", defect.getPriority());
            bugzillaPayload.put("version", "unspecified");

            Response response = RestAssured.given()
                .header("Authorization", "Bearer " + config.getBugzillaApiKey())
                .header("Content-Type", "application/json")
                .body(mapper.writeValueAsString(bugzillaPayload))
                .post(config.getBugzillaUrl() + "/rest/bug");

            if (response.getStatusCode() == 200) {
                String responseBody = response.getBody().asString();
                String defectId = mapper.readTree(responseBody).get("id").asText();
                logger.info("Bugzilla defect created successfully: {}", defectId);
                return "BZ-" + defectId;
            } else {
                logger.error("Failed to create Bugzilla defect: {}", response.getBody().asString());
                return generateTableBasedDefectId(defect);
            }

        } catch (Exception e) {
            logger.error("Error creating Bugzilla defect: {}", e.getMessage());
            return generateTableBasedDefectId(defect);
        }
    }

    /**
     * Create defect in Plane (bug tracking application)
     */
    private String createPlaneDefect(DefectDetails defect) {
        try {
            Map<String, Object> planePayload = new HashMap<>();
            planePayload.put("name", defect.getTitle());
            planePayload.put("description", defect.getDescription());
            planePayload.put("priority", defect.getPriority().toLowerCase());
            planePayload.put("state", "backlog");
            planePayload.put("project", config.getPlaneProjectId());

            // Add labels
            planePayload.put("labels", new String[]{"api-test-failure", "automated"});

            Response response = RestAssured.given()
                .header("Authorization", "Bearer " + config.getPlaneApiKey())
                .header("Content-Type", "application/json")
                .body(mapper.writeValueAsString(planePayload))
                .post(config.getPlaneUrl() + "/api/v1/workspaces/" + config.getPlaneWorkspaceId() +
                      "/projects/" + config.getPlaneProjectId() + "/issues/");

            if (response.getStatusCode() == 201) {
                String responseBody = response.getBody().asString();
                String defectId = mapper.readTree(responseBody).get("id").asText();
                logger.info("Plane defect created successfully: {}", defectId);
                return "PLANE-" + defectId;
            } else {
                logger.error("Failed to create Plane defect: {}", response.getBody().asString());
                return generateTableBasedDefectId(defect);
            }

        } catch (Exception e) {
            logger.error("Error creating Plane defect: {}", e.getMessage());
            return generateTableBasedDefectId(defect);
        }
    }

    /**
     * Create mock defect for testing with D_TableName_001 format
     */
    private String createMockDefect(DefectDetails defect) {
        String defectId = generateTableBasedDefectId(defect);
        logger.info("Mock defect created: {} - {}", defectId, defect.getTitle());

        // Log defect details for testing
        logger.info("Defect Details:");
        logger.info("  Title: {}", defect.getTitle());
        logger.info("  Description: {}", defect.getDescription());
        logger.info("  Severity: {}", defect.getSeverity());
        logger.info("  Priority: {}", defect.getPriority());
        logger.info("  Component: {}", defect.getComponent());

        return defectId;
    }

    /**
     * Generate table-based defect ID in format D_TableName_001
     */
    private String generateTableBasedDefectId(DefectDetails defect) {
        try {
            // Extract table name from defect title or component
            String tableName = extractTableNameFromDefect(defect);

            // Generate sequential number (in real implementation, this would be from database)
            String sequentialNumber = generateSequentialNumber(tableName);

            return String.format("D_%s_%s", tableName, sequentialNumber);

        } catch (Exception e) {
            logger.error("Error generating table-based defect ID: {}", e.getMessage());
            return generateFallbackDefectId();
        }
    }

    /**
     * Extract table name from defect details - PRIORITY: Excel Column 2
     */
    private String extractTableNameFromDefect(DefectDetails defect) {
        // FIRST PRIORITY: Use table name from Excel column 2
        String tableName = defect.getTableName();
        if (tableName != null && !tableName.trim().isEmpty() && !"UnknownTable".equals(tableName)) {
            logger.info("📋 Using table name from Excel column 2: '{}'", tableName);
            return tableName.trim().replaceAll("[^a-zA-Z0-9]", "");
        }

        // SECOND PRIORITY: Try to extract from component
        String component = defect.getComponent();
        if (component != null && !component.equals("API Testing") && !component.trim().isEmpty()) {
            logger.info("📋 Using table name from component: '{}'", component);
            return component.replaceAll("\\s+", "");
        }

        // THIRD PRIORITY: Try to extract from title
        String title = defect.getTitle();
        if (title != null && title.contains("API Test Failure:")) {
            // Extract entity name from title like "API Test Failure: API_POST_Row_14"
            if (title.contains("_Row_")) {
                String[] parts = title.split("_");
                if (parts.length >= 2) {
                    String operation = parts[1]; // POST, GET, PUT, DELETE
                    logger.info("📋 Using table name from title operation: '{}'", operation);
                    return operation + "Operation";
                }
            }
        }

        // FOURTH PRIORITY: Try to extract from description
        String description = defect.getDescription();
        if (description != null && description.contains("**Endpoint:**")) {
            String endpoint = extractEndpointFromDescription(description);
            String endpointTableName = extractTableNameFromEndpoint(endpoint);
            if (!"UnknownTable".equals(endpointTableName)) {
                logger.info("📋 Using table name from endpoint: '{}'", endpointTableName);
                return endpointTableName;
            }
        }

        // Default fallback
        logger.warn("📋 Using fallback table name: 'UnknownTable'");
        return "UnknownTable";
    }

    /**
     * Extract endpoint from defect description
     */
    private String extractEndpointFromDescription(String description) {
        try {
            String[] lines = description.split("\n");
            for (String line : lines) {
                if (line.startsWith("**Endpoint:**")) {
                    return line.replace("**Endpoint:**", "").trim();
                }
            }
        } catch (Exception e) {
            logger.debug("Error extracting endpoint from description: {}", e.getMessage());
        }
        return "unknown";
    }

    /**
     * Extract table name from endpoint
     */
    private String extractTableNameFromEndpoint(String endpoint) {
        try {
            // Extract from endpoint like "/order/api/BundleProduct/save"
            String[] parts = endpoint.split("/");
            for (int i = 0; i < parts.length; i++) {
                if ("api".equals(parts[i]) && i + 1 < parts.length) {
                    return parts[i + 1]; // Return "BundleProduct"
                }
            }
        } catch (Exception e) {
            logger.debug("Error extracting table name from endpoint: {}", e.getMessage());
        }
        return "UnknownTable";
    }

    /**
     * Generate sequential number for defect ID - ENSURES NO DUPLICATES
     */
    private String generateSequentialNumber(String tableName) {
        // Get or create counter for this table
        AtomicInteger counter = defectCounters.computeIfAbsent(tableName, k -> new AtomicInteger(0));

        // Increment and get next sequential number
        int sequentialNum = counter.incrementAndGet();

        logger.info("🔢 Generated sequential number {} for table '{}'", sequentialNum, tableName);

        return String.format("%03d", sequentialNum);
    }

    /**
     * Reset defect counter for a specific table (for testing purposes)
     */
    public static void resetDefectCounter(String tableName) {
        defectCounters.remove(tableName);
        logger.info("🔄 Reset defect counter for table '{}'", tableName);
    }

    /**
     * Reset all defect counters (for testing purposes)
     */
    public static void resetAllDefectCounters() {
        defectCounters.clear();
        logger.info("🔄 Reset all defect counters");
    }

    /**
     * Get current defect count for a table
     */
    public static int getCurrentDefectCount(String tableName) {
        AtomicInteger counter = defectCounters.get(tableName);
        return counter != null ? counter.get() : 0;
    }

    /**
     * Generate fallback defect ID when creation fails
     */
    private String generateFallbackDefectId() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss"));
        return "D_FALLBACK_" + timestamp;
    }

    /**
     * Update defect status
     */
    public boolean updateDefectStatus(String defectId, String status) {
        try {
            logger.info("Updating defect {} status to {}", defectId, status);

            String bugTrackingSystem = config.getBugTrackingSystem();

            switch (bugTrackingSystem.toLowerCase()) {
                case "jira":
                    return updateJiraDefectStatus(defectId, status);
                case "azure":
                case "azuredevops":
                    return updateAzureDefectStatus(defectId, status);
                case "plane":
                    return updatePlaneDefectStatus(defectId, status);
                default:
                    logger.info("Mock update: Defect {} status changed to {}", defectId, status);
                    return true;
            }

        } catch (Exception e) {
            logger.error("Error updating defect status: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Update Jira defect status
     */
    private boolean updateJiraDefectStatus(String defectId, String status) {
        // Implementation for Jira status update
        logger.info("Jira defect {} status updated to {}", defectId, status);
        return true;
    }

    /**
     * Update Azure DevOps defect status
     */
    private boolean updateAzureDefectStatus(String defectId, String status) {
        // Implementation for Azure DevOps status update
        logger.info("Azure DevOps defect {} status updated to {}", defectId, status);
        return true;
    }

    /**
     * Update Plane defect status
     */
    private boolean updatePlaneDefectStatus(String defectId, String status) {
        // Implementation for Plane status update
        logger.info("Plane defect {} status updated to {}", defectId, status);
        return true;
    }

    /**
     * Generate defect ID for API test failures
     * This method is used by the API test classes for simple defect ID generation
     *
     * @param module The module being tested (e.g., PHOTO, PRODUCT)
     * @param testType The type of test (e.g., POST, PUT, GET, DELETE, API_TEST)
     * @param errorMessage The error message from the failed test
     * @return Unique defect ID
     */
    public String generateDefectId(String module, String testType, String errorMessage) {
        try {
            // Create a simple DefectDetails object for compatibility
            DefectDetails defect = new DefectDetails();
            defect.setTitle(String.format("API Test Failure: %s_%s", module, testType));
            defect.setDescription(errorMessage);
            defect.setComponent(module);
            defect.setSeverity("Medium");
            defect.setPriority("Medium");
            defect.setTableName(module);

            // Use the existing table-based defect ID generation
            return generateTableBasedDefectId(defect);

        } catch (Exception e) {
            logger.error("Error generating defect ID: " + e.getMessage());
            return generateFallbackDefectId();
        }
    }
}
