package utils;

/**
 * Constants used throughout the application
 */
public class Constants {
    // API related constants
    public static final String DEFAULT_ENTITY_NAME = "CountryMaster";
    public static final String DEFAULT_ID = "1008";
    public static final String API_BASE_PATH = "/core/api/";

    // Excel column names
    public static final String STATUS_PASSED = "Passed";
    public static final String STATUS_FAILED = "Failed";
    public static final String STATUS_COMPLETED = "Completed";

    // JSON field names
    public static final String FIELD_ID = "id";
    public static final String FIELD_ENDPOINT = "endpoint";
    public static final String FIELD_TYPE = "type";
    public static final String FIELD_PAYLOAD = "payload";
    public static final String FIELD_AUTH = "auth";
    public static final String FIELD_TENANT_ID = "tenantId";
    public static final String FIELD_DATA = "data";

    // API operation types
    public static final String TYPE_GET = "get";
    public static final String TYPE_POST = "post";
    public static final String TYPE_PUT = "put";
    public static final String TYPE_PATCH = "patch";
    public static final String TYPE_DELETE = "delete";

    // Endpoint patterns
    public static final String PATTERN_UPDATE_PARTIAL_ENTITY = "/updatePartialEntity/";
    public static final String PATTERN_API = "/api/";
    public static final String PATTERN_FILTER = "/filter";
    public static final String PATTERN_SAVE = "/save";
    public static final String PATTERN_UPDATE = "/update";
    public static final String PATTERN_LIST = "/list";

    // Result messages
    public static final String MSG_PATCH_GETBYID_MATCH = "PATCH and GetById responses match";
    public static final String MSG_PATCH_GETBYID_MISMATCH = "PATCH and GetById responses do not match";
    public static final String MSG_DB_API_MATCH = "Database record matches API response";
    public static final String MSG_DB_API_MISMATCH = "Database record does not match API response";
    public static final String MSG_PATCH_FAILED = "PATCH request failed with status code: ";
    public static final String MSG_PATCH_FAILED_GETBYID_COMPLETED = "PATCH request failed but GetById request completed";
    public static final String MSG_GET_RESPONSE_NULL = "GET response is null";
    public static final String MSG_PATCH_RESPONSE_NULL = "PATCH response is null";
    public static final String MSG_EXTRACT_ID_FAILED = "Failed to extract ID from PATCH response";

    // Config properties
    public static final String CONFIG_FILE = "config.properties";
    public static final String PROP_DEFAULT_ID = "default.id";
    public static final String PROP_DEFAULT_ENTITY = "default.entity";
    public static final String PROP_API_BASE_PATH = "api.base.path";
}
