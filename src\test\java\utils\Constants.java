package utils;

/**
 * Constants class for API testing framework
 * Contains all constant values used across the framework
 */
public class Constants {
    
    // Status constants
    public static final String STATUS_PASSED = "Passed";
    public static final String STATUS_FAILED = "Failed";
    public static final String STATUS_SKIPPED = "Skipped";
    
    // Message constants
    public static final String MSG_DB_API_MATCH = "API response matches database record";
    public static final String MSG_DB_API_MISMATCH = "API response does not match database record";
    public static final String MSG_STATUS_CODE_MATCH = "Status code matches expected";
    public static final String MSG_STATUS_CODE_MISMATCH = "Status code does not match expected";
    
    // Field constants for JSON
    public static final String FIELD_ENDPOINT = "endpoint";
    public static final String FIELD_TYPE = "type";
    public static final String FIELD_PAYLOAD = "payload";
    public static final String FIELD_AUTH = "auth";
    
    // HTTP method types
    public static final String TYPE_GET = "GET";
    public static final String TYPE_POST = "POST";
    public static final String TYPE_PUT = "PUT";
    public static final String TYPE_DELETE = "DELETE";
    public static final String TYPE_PATCH = "PATCH";
    
    // API endpoints
    public static final String ENDPOINT_PHOTOS = "/api/photos";
    public static final String ENDPOINT_PRODUCTS = "/api/products";
    public static final String ENDPOINT_AUTH = "/api/auth/login";
    
    // Database constants
    public static final String DB_SCHEMA_CORE = "core";
    public static final String DB_TABLE_PHOTOS = "photos";
    public static final String DB_TABLE_PRODUCTS = "products";
    
    // Excel column constants
    public static final int COL_URL = 3;
    public static final int COL_BODY = 4;
    public static final int COL_EXPECTED = 5;
    public static final int COL_ACTUAL = 6;
    public static final int COL_STATUS = 7;
    public static final int COL_DEFECT_ID = 8;
    
    // Configuration constants
    public static final String CONFIG_FILE = "crud-config.properties";
    public static final String EXCEL_FILE = "data/SnackHack.xlsx";
    public static final String BASE_URL = "http://localhost:8080";
    
    // Test data constants
    public static final int AUTH_ROW = 13;
    public static final int TEST_START_ROW = 14;
    
    // Error messages
    public static final String ERROR_EXCEL_NOT_FOUND = "Excel file not found";
    public static final String ERROR_DB_CONNECTION = "Database connection failed";
    public static final String ERROR_AUTH_FAILED = "Authentication failed";
    public static final String ERROR_API_CALL_FAILED = "API call failed";
    
    // Success messages
    public static final String SUCCESS_TEST_PASSED = "Test passed successfully";
    public static final String SUCCESS_DB_VALIDATED = "Database validation successful";
    public static final String SUCCESS_AUTH_COMPLETED = "Authentication completed";
    
    // Validation constants
    public static final int MAX_RETRY_ATTEMPTS = 3;
    public static final int REQUEST_TIMEOUT = 30000; // 30 seconds
    public static final int DB_QUERY_TIMEOUT = 10000; // 10 seconds
    
    // Private constructor to prevent instantiation
    private Constants() {
        throw new UnsupportedOperationException("Constants class cannot be instantiated");
    }
}
