# CRUD Operations API Testing Framework

## Overview

This comprehensive API testing framework provides automated testing for CRUD (Create, Read, Update, Delete) operations on Photos and Products tables with database validation, Excel integration, and defect tracking.

## Features

### 🚀 Core Features
- **Separate Classes for Each CRUD Operation**: PhotosApiTest and ProductsApiTest
- **Excel Sheet Integration**: Test data and results stored in Excel
- **Database Validation**: Foreign key queries and data consistency checks
- **Status Code Validation**: Expected vs Actual status code comparison
- **Constraint Testing**: Null and unique constraint validation
- **Faker Data Generation**: Dynamic test data using Java Faker
- **Defect ID Generation**: Automatic defect tracking for failed tests

### 📊 Test Coverage

#### POST API Testing
- ✅ Successful creation with status code validation
- ✅ Request body and response body validation
- ✅ Extract ID from response and validate with GetById API
- ✅ Null constraint validation
- ✅ Unique constraint validation
- ✅ Excel sheet updates for each test case

#### PUT API Testing
- ✅ Use POST response ID for update operations
- ✅ Status code validation
- ✅ Response validation with GetById API
- ✅ Null and unique constraint validation
- ✅ Excel sheet updates

#### GET All API Testing
- ✅ Status code validation
- ✅ Database validation (query database and match with API response)
- ✅ Index-based testing (0, 1, 2, 3...n)
- ✅ Excel sheet updates

#### GET By ID API Testing
- ✅ Status code validation
- ✅ Database validation with foreign key handling
- ✅ Excel sheet updates

#### DELETE API Testing
- ✅ Status code validation
- ✅ Verify deletion by hitting GetById API
- ✅ Excel sheet updates

## Project Structure

```
src/test/java/
├── testCases/
│   ├── PhotosApiTest.java          # Photos table CRUD operations
│   ├── ProductsApiTest.java        # Products table CRUD operations
│   └── CrudOperationsTestSuite.java # Comprehensive test suite
├── utils/
│   ├── DatabaseValidationUtils.java # Database validation utilities
│   ├── DefectTracker.java          # Defect tracking and ID generation
│   ├── DynamicDataGenerator.java   # Faker data generation
│   ├── TestConfiguration.java     # Configuration management
│   └── ExcelUtils.java            # Excel file operations
└── data/
    └── SnackHack.xlsx             # Test data and results
```

## Configuration

### Excel Sheet Structure

The framework expects the following columns in your Excel sheets:

| Column | Purpose |
|--------|---------|
| 3 | URL |
| 4 | Request Body |
| 5 | Expected Result |
| 6 | Actual Result |
| 7 | Status |
| 8 | Defect ID |

### Configuration File (config.properties)

```properties
# Excel Configuration
excel.file.path=data/SnackHack.xlsx
excel.column.url=3
excel.column.body=4
excel.column.expected=5
excel.column.actual=6
excel.column.status=7
excel.column.defectid=8

# API Configuration
base.url=http://localhost:8080

# Database Configuration
database.url=*************************************
database.username=root
database.password=password
database.driver=com.mysql.cj.jdbc.Driver

# Feature Flags
database.validation.enabled=true
defect.tracking.enabled=true
```

## Usage

### Running Individual Test Classes

#### Photos API Tests
```java
PhotosApiTest photosTest = new PhotosApiTest();
photosTest.setup();
photosTest.testCreatePhoto();
photosTest.testUpdatePhoto();
photosTest.testGetAllPhotos();
photosTest.testGetPhotoById();
photosTest.testDeletePhoto();
```

#### Products API Tests
```java
ProductsApiTest productsTest = new ProductsApiTest();
productsTest.setup();
productsTest.testCreateProduct();
productsTest.testUpdateProduct();
productsTest.testGetAllProducts();
productsTest.testGetProductById();
productsTest.testDeleteProduct();
```

### Running Complete Test Suite

#### Using TestNG XML
```bash
mvn test -DsuiteXmlFile=testng-crud-operations.xml
```

#### Using Test Suite Class
```java
CrudOperationsTestSuite suite = new CrudOperationsTestSuite();
suite.suiteSetup();
suite.executePhotosApiTests();
suite.executeProductsApiTests();
suite.validateDatabaseConsistency();
suite.suiteCleanup();
```

## Test Data Generation

The framework uses Java Faker to generate realistic test data:

### Photos Data
```json
{
  "title": "Generated photo title",
  "description": "Generated description",
  "url": "https://generated-url.com",
  "thumbnailUrl": "https://generated-thumbnail.com",
  "albumId": 42,
  "userId": 7,
  "tags": "tag1,tag2,tag3",
  "createdBy": "testuser123"
}
```

### Products Data
```json
{
  "name": "Generated Product Name",
  "description": "Generated product description",
  "price": 299.99,
  "category": "Electronics",
  "brand": "Generated Brand",
  "sku": "PRD-1234-AB",
  "stock": 50,
  "isActive": true,
  "weight": 2.5
}
```

## Database Validation

### Foreign Key Handling
The framework automatically handles foreign key relationships:

```sql
-- Photos with foreign keys
SELECT p.*, 
       a.title as album_title,
       u.username, u.email
FROM photos p
LEFT JOIN albums a ON p.album_id = a.id
LEFT JOIN users u ON p.user_id = u.id
WHERE p.id = ?

-- Products with foreign keys
SELECT p.*, 
       c.name as category_name,
       b.name as brand_name,
       s.name as supplier_name
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
LEFT JOIN brands b ON p.brand_id = b.id
LEFT JOIN suppliers s ON p.supplier_id = s.id
WHERE p.id = ?
```

## Defect Tracking

### Defect ID Format
- **Format**: `D_TableName_001`, `D_TableName_002`, etc.
- **Examples**: `D_PHOTO_001`, `D_PRODUCT_001`

### Defect Generation Scenarios
- Status code mismatches
- Null constraint violations
- Unique constraint violations
- Database validation failures
- API response validation failures

## Excel Integration

### Test Results
- ✅ **PASS**: Green background in status column
- ❌ **FAIL**: Red background in status column with defect ID

### Data Flow
1. Read test data from Excel
2. Generate dynamic data using Faker
3. Execute API calls
4. Validate responses
5. Update Excel with results
6. Generate defect IDs for failures

## Best Practices

### Test Data Management
- Use Faker for dynamic data generation
- Avoid hardcoded test data
- Clean up test data after execution

### Error Handling
- Comprehensive exception handling
- Detailed logging for debugging
- Graceful failure handling

### Database Validation
- Always validate API responses against database
- Handle foreign key relationships properly
- Use transactions for data consistency

## Troubleshooting

### Common Issues

#### Excel File Access
```
Error: Excel file not accessible
Solution: Check file path and permissions
```

#### Database Connection
```
Error: Database connection failed
Solution: Verify database URL, credentials, and connectivity
```

#### Authentication Issues
```
Error: Authentication failed
Solution: Check auth token generation and validity
```

### Debug Mode
Enable debug logging in configuration:
```properties
log.level=DEBUG
log.detailed.enabled=true
```

## Dependencies

### Required Libraries
- TestNG
- RestAssured
- Apache POI (Excel)
- Jackson (JSON)
- Java Faker
- MySQL Connector (or your database driver)
- SLF4J (Logging)

### Maven Dependencies
```xml
<dependencies>
    <dependency>
        <groupId>org.testng</groupId>
        <artifactId>testng</artifactId>
        <version>7.8.0</version>
    </dependency>
    <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>rest-assured</artifactId>
        <version>5.3.2</version>
    </dependency>
    <dependency>
        <groupId>com.github.javafaker</groupId>
        <artifactId>javafaker</artifactId>
        <version>1.0.2</version>
    </dependency>
</dependencies>
```

## Contributing

1. Follow the existing code structure
2. Add comprehensive logging
3. Include proper error handling
4. Update documentation for new features
5. Add unit tests for utility classes

## Support

For issues and questions:
1. Check the logs for detailed error messages
2. Verify configuration settings
3. Ensure database connectivity
4. Validate Excel file structure
