<?xml version="1.0" encoding="UTF-8"?>
<testng-results ignored="2" total="3" passed="1" failed="0" skipped="0">
  <reporter-output>
  </reporter-output>
  <suite started-at="2025-05-26T17:01:28 IST" name="Surefire suite" finished-at="2025-05-26T17:01:48 IST" duration-ms="19514">
    <groups>
    </groups>
    <test started-at="2025-05-26T17:01:28 IST" name="Surefire test" finished-at="2025-05-26T17:01:48 IST" duration-ms="19514">
      <class name="testCases.ForceDefectGenerationTest">
        <test-method is-config="true" signature="setup()[pri:0, instance:testCases.ForceDefectGenerationTest@1ffaf86]" started-at="2025-05-26T17:01:28 IST" name="setup" finished-at="2025-05-26T17:01:33 IST" duration-ms="4646" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setup -->
        <test-method signature="testMultipleDefectGenerationForSameTable()[pri:0, instance:testCases.ForceDefectGenerationTest@1ffaf86]" started-at="2025-05-26T17:01:33 IST" name="testMultipleDefectGenerationForSameTable" finished-at="2025-05-26T17:01:48 IST" duration-ms="14837" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testMultipleDefectGenerationForSameTable -->
      </class> <!-- testCases.ForceDefectGenerationTest -->
    </test> <!-- Surefire test -->
  </suite> <!-- Surefire suite -->
</testng-results>
