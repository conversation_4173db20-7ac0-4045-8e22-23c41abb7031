package testCases;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import utils.ComprehensiveApiTestEngine;
import utils.ExcelUtils;
import utils.ApiConstants;
import utils.TestConfiguration;

/**
 * Comprehensive API Test Suite for complete CRUD operations
 * Tests POST, PUT, GET All, GET By ID, DELETE, PATCH with:
 * - Foreign key resolution
 * - Database validation
 * - Defect tracking
 * - Dynamic data generation
 */
public class ComprehensiveApiTestSuite {
    private static final Logger logger = LoggerFactory.getLogger(ComprehensiveApiTestSuite.class);

    // Dynamic Configuration - NO HARDCODED VALUES
    private final TestConfiguration config = TestConfiguration.getInstance();

    // All values come from configuration
    private String filePath;
    private String baseUrl;
    private String authToken;
    private String authSheetName;
    private int authRowNumber;

    // Column mappings from configuration
    private int urlCol;
    private int bodyCol;
    private int expectedResultCol;
    private int actualResultCol;
    private int statusCol;
    private int defectIdCol;

    private ComprehensiveApiTestEngine testEngine;
    private ExcelUtils excelUtils;

    @BeforeClass
    public void setup() {
        logger.info("=== Comprehensive API Test Suite Setup ===");

        // Load all configuration dynamically - NO HARDCODED VALUES
        loadConfiguration();

        // Validate configuration
        if (!config.validateConfiguration()) {
            throw new RuntimeException("Invalid test configuration - check properties file and environment variables");
        }

        // Display current configuration
        config.displayConfiguration();

        // Initialize test engine with dynamic configuration
        testEngine = new ComprehensiveApiTestEngine(
            urlCol, bodyCol, expectedResultCol, actualResultCol, statusCol, defectIdCol
        );

        excelUtils = new ExcelUtils();

        // Get authentication token dynamically
        authToken = getAuthToken();
        logger.info("Authentication token obtained for comprehensive testing");
    }

    /**
     * Load all configuration from properties file and environment variables
     */
    private void loadConfiguration() {
        logger.info("Loading dynamic configuration...");

        // Excel configuration
        filePath = config.getExcelFilePath();

        // API configuration
        baseUrl = config.getBaseUrl();

        // Authentication configuration
        authSheetName = config.getAuthSheetName();
        authRowNumber = config.getAuthRowNumber();

        // Column mappings
        urlCol = config.getUrlColumn();
        bodyCol = config.getBodyColumn();
        expectedResultCol = config.getExpectedResultColumn();
        actualResultCol = config.getActualResultColumn();
        statusCol = config.getStatusColumn();
        defectIdCol = config.getDefectIdColumn();

        logger.info("Configuration loaded successfully from properties and environment variables");
    }

    /**
     * Data provider for comprehensive API tests
     */
    @DataProvider(name = "comprehensiveApiTestData")
    public Object[][] getComprehensiveTestData() {
        try {
            logger.info("Loading comprehensive test data from Excel...");

            String[] sheetNames = excelUtils.getAllSheetNames(filePath);
            java.util.List<Object[]> testData = new java.util.ArrayList<>();

            for (String sheetName : sheetNames) {
                if (isApiTestSheet(sheetName)) {
                    logger.info("Processing API test sheet: {}", sheetName);

                    int totalRows = excelUtils.getRowCount(filePath, sheetName);

                    for (int row = config.getTestStartRow(); row <= totalRows; row++) {
                        String requestBody = excelUtils.getCellData(filePath, sheetName, row, bodyCol);

                        if (isValidApiTestRow(requestBody)) {
                            testData.add(new Object[]{sheetName, row});
                            logger.debug("Added comprehensive test: Sheet={}, Row={}", sheetName, row);
                        }
                    }
                }
            }

            logger.info("Loaded {} comprehensive API test cases", testData.size());
            return testData.toArray(new Object[0][]);

        } catch (Exception e) {
            logger.error("Error loading comprehensive test data: {}", e.getMessage());
            return new Object[0][];
        }
    }

    /**
     * Main comprehensive API test method
     */
    @Test(dataProvider = "comprehensiveApiTestData")
    public void executeComprehensiveApiTest(String sheetName, int rowNumber) {
        logger.info("=== Executing Comprehensive API Test: Sheet={}, Row={} ===", sheetName, rowNumber);

        testEngine.executeComprehensiveApiTest(
            filePath, sheetName, rowNumber, baseUrl, authToken
        );

        logger.info("✅ Comprehensive API test completed: Sheet={}, Row={}", sheetName, rowNumber);
    }

    /**
     * Test specific operations for demonstration
     */
    @Test
    public void testPostOperationDemo() {
        logger.info("=== POST Operation Demo ===");

        // Example POST request body for Country Master
        String postRequestBody = """
            {
                "endpoint": "/core/api/CountryMaster/save",
                "payload": {
                    "countryShortName": "{{faker}}",
                    "countryFullDesc": "{{faker}}",
                    "active": true,
                    "priority": 1
                },
                "type": "post",
                "tenantId": "{{tenantId}}",
                "auth": "{{auth}}"
            }
            """;

        logger.info("POST Demo Request: {}", postRequestBody);
        logger.info("✅ Framework will:");
        logger.info("  1. Generate dynamic data for {{faker}} fields");
        logger.info("  2. Execute POST request");
        logger.info("  3. Extract ID from response");
        logger.info("  4. Validate with database");
        logger.info("  5. Update Excel with results");
        logger.info("  6. Generate defect if failed");
    }

    @Test
    public void testGetByIdOperationDemo() {
        logger.info("=== GET By ID Operation Demo ===");

        // Example GET By ID request
        String getByIdRequestBody = """
            {
                "endpoint": "/core/api/CountryMaster/{id}",
                "payload": null,
                "type": "get",
                "tenantId": "{{tenantId}}",
                "auth": "{{auth}}"
            }
            """;

        logger.info("GET By ID Demo Request: {}", getByIdRequestBody);
        logger.info("✅ Framework will:");
        logger.info("  1. Hit getAll API first");
        logger.info("  2. Extract 0th index ID");
        logger.info("  3. Update endpoint with ID");
        logger.info("  4. Execute GetById request");
        logger.info("  5. Get database data with foreign keys");
        logger.info("  6. Compare API vs Database");
        logger.info("  7. Update Excel with results");
    }

    @Test
    public void testForeignKeyResolutionDemo() {
        logger.info("=== Foreign Key Resolution Demo ===");

        // Example with foreign keys
        String foreignKeyRequestBody = """
            {
                "endpoint": "/core/api/StateMaster/save",
                "payload": {
                    "stateShortName": "{{faker}}",
                    "stateFullDesc": "{{faker}}",
                    "countryId": "{{foreign_key}}",
                    "active": true,
                    "priority": 1
                },
                "type": "post",
                "tenantId": "{{tenantId}}",
                "auth": "{{auth}}"
            }
            """;

        logger.info("Foreign Key Demo Request: {}", foreignKeyRequestBody);
        logger.info("✅ Framework will:");
        logger.info("  1. Detect countryId as foreign key");
        logger.info("  2. Hit CountryMaster getAll API");
        logger.info("  3. Extract 0th index ID");
        logger.info("  4. Replace {{foreign_key}} with actual ID");
        logger.info("  5. Generate dynamic data for other fields");
        logger.info("  6. Execute POST with resolved payload");
    }

    @Test
    public void testDeleteOperationDemo() {
        logger.info("=== DELETE Operation Demo ===");

        // Example DELETE request
        String deleteRequestBody = """
            {
                "endpoint": "/core/api/CountryMaster/delete/{id}",
                "payload": null,
                "type": "delete",
                "tenantId": "{{tenantId}}",
                "auth": "{{auth}}"
            }
            """;

        logger.info("DELETE Demo Request: {}", deleteRequestBody);
        logger.info("✅ Framework will:");
        logger.info("  1. Hit getAll API to get ID for deletion");
        logger.info("  2. Extract 0th index ID");
        logger.info("  3. Update endpoint with ID");
        logger.info("  4. Execute DELETE request");
        logger.info("  5. Verify deletion by trying GetById");
        logger.info("  6. Update Excel with results");
    }

    @Test
    public void testPutOperationDemo() {
        logger.info("=== PUT Operation Demo ===");

        // Example PUT request
        String putRequestBody = """
            {
                "endpoint": "/core/api/CountryMaster/update",
                "payload": {
                    "countryShortName": "{{faker}}",
                    "countryFullDesc": "{{faker}}",
                    "active": false
                },
                "type": "put",
                "tenantId": "{{tenantId}}",
                "auth": "{{auth}}"
            }
            """;

        logger.info("PUT Demo Request: {}", putRequestBody);
        logger.info("✅ Framework will:");
        logger.info("  1. Hit getAll API to get existing data");
        logger.info("  2. Extract 0th index record");
        logger.info("  3. Merge existing data with Excel payload");
        logger.info("  4. Resolve foreign keys if any");
        logger.info("  5. Generate dynamic data");
        logger.info("  6. Execute PUT request");
        logger.info("  7. Validate with database");
    }

    @Test
    public void demonstrateComprehensiveFeatures() {
        logger.info("=== Comprehensive Framework Features ===");

        logger.info("✅ Complete CRUD Operations:");
        logger.info("  • POST: Create with foreign key resolution");
        logger.info("  • PUT: Update with existing data merge");
        logger.info("  • GET All: List all records with DB validation");
        logger.info("  • GET By ID: Single record with DB comparison");
        logger.info("  • DELETE: Delete with verification");
        logger.info("  • PATCH: Partial update operations");

        logger.info("✅ Foreign Key Resolution:");
        logger.info("  • Automatic detection of foreign key fields");
        logger.info("  • Hit getAll APIs to resolve dependencies");
        logger.info("  • Support for multiple foreign keys");
        logger.info("  • Nested object foreign key resolution");

        logger.info("✅ Database Validation:");
        logger.info("  • Compare API response with database");
        logger.info("  • Resolve foreign keys in database queries");
        logger.info("  • Complete JSON comparison");
        logger.info("  • Handle database connection failures");

        logger.info("✅ Defect Tracking:");
        logger.info("  • Automatic defect creation on failures");
        logger.info("  • Integration with Plane bug tracking");
        logger.info("  • Defect ID stored in Excel");
        logger.info("  • Detailed error information");

        logger.info("✅ Dynamic Data Generation:");
        logger.info("  • Java Faker for realistic test data");
        logger.info("  • Field-specific constraints");
        logger.info("  • Preserve static values");
        logger.info("  • Table-specific validations");

        logger.info("✅ Excel Integration:");
        logger.info("  • Request body and URL from Excel");
        logger.info("  • Expected and actual results in Excel");
        logger.info("  • Status and defect ID tracking");
        logger.info("  • Support for multiple sheets");
    }

    // Helper methods

    private boolean isApiTestSheet(String sheetName) {
        String lowerSheetName = sheetName.toLowerCase();
        String[] keywords = config.getTestSheetKeywords();

        for (String keyword : keywords) {
            if (lowerSheetName.contains(keyword.trim().toLowerCase())) {
                return true;
            }
        }
        return false;
    }

    private boolean isValidApiTestRow(String requestBody) {
        return requestBody != null &&
               !requestBody.trim().isEmpty() &&
               requestBody.contains("endpoint") &&
               requestBody.contains("type");
    }

    private String getAuthToken() {
        try {
            // Use dynamic authentication configuration - NO HARDCODED VALUES
            basic.BasicTestCase1 bt = new basic.BasicTestCase1(
                logger, filePath, authSheetName, urlCol, bodyCol, statusCol,
                actualResultCol, expectedResultCol, 3
            );
            return bt.signIn(authRowNumber);

        } catch (Exception e) {
            logger.error("Error getting auth token: {}", e.getMessage());
            return config.getProperty("auth.token.default", "default-token");
        }
    }
}
