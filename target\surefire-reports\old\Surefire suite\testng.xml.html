<html><head><title>testng.xml for Surefire suite</title></head><body><tt>&lt;?xml&nbsp;version="1.0"&nbsp;encoding="UTF-8"?&gt;
<br/>&lt;!DOCTYPE&nbsp;suite&nbsp;SYSTEM&nbsp;"https://testng.org/testng-1.0.dtd"&gt;
<br/>&lt;suite&nbsp;thread-count="1"&nbsp;name="Surefire&nbsp;suite"&nbsp;verbose="0"&gt;
<br/>&nbsp;&nbsp;&lt;test&nbsp;thread-count="1"&nbsp;name="Surefire&nbsp;test"&nbsp;verbose="0"&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&lt;method-selectors&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;method-selector&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;selector-class&nbsp;name="org.apache.maven.surefire.testng.utils.MethodSelector"&nbsp;priority="10000"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;/method-selector&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&lt;/method-selectors&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&lt;classes&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="testCases.ForceDefectGenerationTest"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&lt;/classes&gt;
<br/>&nbsp;&nbsp;&lt;/test&gt;&nbsp;&lt;!--&nbsp;Surefire&nbsp;test&nbsp;--&gt;
<br/>&lt;/suite&gt;&nbsp;&lt;!--&nbsp;Surefire&nbsp;suite&nbsp;--&gt;
<br/></tt></body></html>