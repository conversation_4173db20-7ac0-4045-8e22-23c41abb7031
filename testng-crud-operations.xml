<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">

<suite name="CRUD Operations Test Suite" verbose="1" parallel="false">
    
    <parameter name="environment" value="test"/>
    <parameter name="browser" value="api"/>
    
    <!-- Listeners for enhanced reporting -->
    <listeners>
        <listener class-name="org.testng.reporters.EmailableReporter"/>
        <listener class-name="org.testng.reporters.JUnitReportReporter"/>
    </listeners>
    
    <!-- Photos API Tests -->
    <test name="Photos API CRUD Tests" preserve-order="true">
        <parameter name="module" value="photos"/>
        
        <groups>
            <run>
                <include name="photos"/>
                <include name="crud"/>
                <include name="post"/>
                <include name="put"/>
                <include name="get"/>
                <include name="delete"/>
            </run>
        </groups>
        
        <classes>
            <class name="testCases.PhotosApiTest">
                <methods>
                    <include name="testCreatePhoto"/>
                    <include name="testUpdatePhoto"/>
                    <include name="testGetAllPhotos"/>
                    <include name="testGetPhotoById"/>
                    <include name="testDeletePhoto"/>
                </methods>
            </class>
        </classes>
    </test>
    
    <!-- Products API Tests -->
    <test name="Products API CRUD Tests" preserve-order="true">
        <parameter name="module" value="products"/>
        
        <groups>
            <run>
                <include name="products"/>
                <include name="crud"/>
                <include name="post"/>
                <include name="put"/>
                <include name="get"/>
                <include name="delete"/>
            </run>
        </groups>
        
        <classes>
            <class name="testCases.ProductsApiTest">
                <methods>
                    <include name="testCreateProduct"/>
                    <include name="testUpdateProduct"/>
                    <include name="testGetAllProducts"/>
                    <include name="testGetProductById"/>
                    <include name="testDeleteProduct"/>
                </methods>
            </class>
        </classes>
    </test>
    
    <!-- Comprehensive Test Suite -->
    <test name="CRUD Operations Suite" preserve-order="true">
        <parameter name="suite" value="comprehensive"/>
        
        <groups>
            <run>
                <include name="photos"/>
                <include name="products"/>
                <include name="validation"/>
                <include name="database"/>
            </run>
        </groups>
        
        <classes>
            <class name="testCases.CrudOperationsTestSuite">
                <methods>
                    <include name="executePhotosApiTests"/>
                    <include name="executeProductsApiTests"/>
                    <include name="validateDatabaseConsistency"/>
                </methods>
            </class>
        </classes>
    </test>
    
</suite>
