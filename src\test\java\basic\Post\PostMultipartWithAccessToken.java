package basic.Post;

import basic.ApiMethod;
import io.restassured.response.Response;
import lombok.*;
import org.slf4j.Logger;
import utils.ExcelUtils;

import java.io.File;

import static io.restassured.RestAssured.given;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class PostMultipartWithAccessToken implements ApiMethod {
    private Logger logger;
    private String filePath;
    private String sheetName;
    private int url;
    private int body;  // In case you need other request data like form params
    private String accessToken;  // Token used for authentication
    private String csvFilePath;  // Path of the CSV file to be uploaded

    public Response post(int rowNum) {
        ExcelUtils excelUtils = new ExcelUtils();

        // Check if the row and cell data is valid before accessing
        String uri = excelUtils.getCellData(filePath, sheetName, rowNum, url);
        if (uri == null || uri.isEmpty()) {
            logger.error("URL not found in row " + rowNum);
            throw new RuntimeException("URL not found in row " + rowNum);
        }

        String body1 = excelUtils.getCellData(filePath, sheetName, rowNum, body);
        if (body1 == null || body1.isEmpty()) {
            logger.error("Body not found in row " + rowNum);
            throw new RuntimeException("Body not found in row " + rowNum);
        }

        // Create a File object for the CSV file
        File csvFile = new File(csvFilePath);
        if (!csvFile.exists()) {
            logger.error("CSV file not found: " + csvFilePath);
            throw new RuntimeException("CSV file not found: " + csvFilePath);
        }

        // Log the request details
        logger.info("Posting to URI: " + uri);
        logger.info("Posting Body: " + body1);
        logger.info("Uploading CSV: " + csvFile.getName());

        // Make the POST multipart request with the CSV file
        Response response = given()
                .multiPart("file", csvFile)  // Multipart file upload
                .when()
                .log().all()  // Log the request details
                .post(uri);

        // Log the response details
        String responseBody = response.getBody().asString();
        logger.info("Request Headers: {}", response.getHeaders());
        logger.info("Response Body: {}", responseBody);
        logger.info("Response Cookies: {}", response.getDetailedCookies());
        logger.info("Request Status: {}", response.getStatusCode());
        logger.info("Response ContentType: {}", response.getContentType());

        return response;
    }
}
