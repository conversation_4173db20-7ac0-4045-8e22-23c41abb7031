<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">

<suite name="Simple CRUD Operations Test Suite" parallel="false">
    <parameter name="environment" value="test"/>
    
    <!-- Photos API CRUD Tests -->
    <test name="Photos API CRUD Operations" preserve-order="true">
        <parameter name="entity" value="photos"/>
        <classes>
            <class name="testCases.PhotosApiTestNew">
                <methods>
                    <include name="testPhotosCrudOperations"/>
                    <include name="testPhotosConstraintValidations"/>
                </methods>
            </class>
        </classes>
    </test>
    
    <!-- Products API CRUD Tests -->
    <test name="Products API CRUD Operations" preserve-order="true">
        <parameter name="entity" value="products"/>
        <classes>
            <class name="testCases.ProductsApiTestNew">
                <methods>
                    <include name="testProductsCrudOperations"/>
                    <include name="testProductsConstraintValidations"/>
                </methods>
            </class>
        </classes>
    </test>
    
    <!-- Listeners for reporting -->
    <listeners>
        <listener class-name="org.testng.reporters.EmailableReporter"/>
        <listener class-name="org.testng.reporters.JUnitReportReporter"/>
    </listeners>
</suite>
