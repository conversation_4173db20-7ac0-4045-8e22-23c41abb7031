package utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * Universal API Handler for any application with dynamic request body structures
 * Supports POST, PUT, GET, DELETE, PATCH operations with automatic payload handling
 */
public class UniversalApiHandler {
    private static final Logger logger = LoggerFactory.getLogger(UniversalApiHandler.class);
    private final ObjectMapper mapper;
    private final DynamicDataGenerator dataGenerator;
    private final DefectTracker defectTracker;

    public UniversalApiHandler() {
        this.mapper = new ObjectMapper();
        this.dataGenerator = new DynamicDataGenerator();
        this.defectTracker = new DefectTracker();
    }

    /**
     * Process any API request from Excel structure
     */
    public ApiResponse processApiRequest(String excelRequestBody, String baseUrl, String authToken) {
        try {
            logger.info("Processing universal API request: {}", excelRequestBody);

            // Parse Excel request structure
            JsonNode requestNode = mapper.readTree(excelRequestBody);

            // Extract API details
            String endpoint = requestNode.get("endpoint").asText();
            String httpMethod = requestNode.get("type").asText().toUpperCase();
            String tenantId = extractTenantId(requestNode);
            logger.info("Extracted tenant ID: '{}'", tenantId);

            // Build full URL
            String fullUrl = buildFullUrl(baseUrl, endpoint);
            logger.info("Full URL: {}, Method: {}", fullUrl, httpMethod);

            // Handle different HTTP methods
            switch (httpMethod) {
                case "POST":
                    return handlePostRequest(requestNode, fullUrl, authToken, tenantId);
                case "PUT":
                    return handlePutRequest(requestNode, fullUrl, authToken, tenantId);
                case "GET":
                    return handleGetRequest(requestNode, fullUrl, authToken, tenantId);
                case "DELETE":
                    return handleDeleteRequest(requestNode, fullUrl, authToken, tenantId);
                case "PATCH":
                    return handlePatchRequest(requestNode, fullUrl, authToken, tenantId);
                default:
                    throw new IllegalArgumentException("Unsupported HTTP method: " + httpMethod);
            }

        } catch (Exception e) {
            logger.error("Error processing API request: {}", e.getMessage());
            return new ApiResponse(500, "Error: " + e.getMessage(), null);
        }
    }

    /**
     * Handle POST request with dynamic payload generation
     */
    private ApiResponse handlePostRequest(JsonNode requestNode, String url, String authToken, String tenantId) {
        try {
            // Generate dynamic payload if it has {{faker}} placeholders
            JsonNode payload = requestNode.get("payload");
            if (payload != null) {
                String payloadStr = mapper.writeValueAsString(payload);
                String dynamicPayload = dataGenerator.processExcelJsonStructure(payloadStr);
                payload = mapper.readTree(dynamicPayload);
            }

            // Make POST request
            RequestSpecification request = RestAssured.given()
                .header("Authorization", "Bearer " + authToken)
                .header("Content-Type", "application/json");

            if (tenantId != null) {
                logger.info("Adding X-TenantID header: '{}'", tenantId);
                request.header("X-TenantID", tenantId);
            } else {
                logger.info("No tenant ID header added (tenantId is null)");
            }

            if (payload != null) {
                request.body(mapper.writeValueAsString(payload));
            }

            Response response = request.post(url);

            logger.info("POST Response - Status: {}, Body: {}", response.getStatusCode(), response.getBody().asString());
            return new ApiResponse(response.getStatusCode(), response.getBody().asString(), response.getHeaders());

        } catch (Exception e) {
            logger.error("Error in POST request: {}", e.getMessage());
            return new ApiResponse(500, "POST Error: " + e.getMessage(), null);
        }
    }

    /**
     * Handle PUT request - First get existing data, then update
     */
    private ApiResponse handlePutRequest(JsonNode requestNode, String url, String authToken, String tenantId) {
        try {
            logger.info("Handling PUT request - First getting existing data");

            // Step 1: Get existing data from getAll endpoint
            String getAllUrl = buildGetAllUrl(url);
            ApiResponse getAllResponse = makeGetRequest(getAllUrl, authToken, tenantId);

            if (getAllResponse.getStatusCode() != 200) {
                logger.error("Failed to get existing data for PUT: {}", getAllResponse.getBody());
                return getAllResponse;
            }

            // Step 2: Extract 0th index payload
            JsonNode existingData = extractZeroIndexPayload(getAllResponse.getBody());
            if (existingData == null) {
                logger.error("No existing data found for PUT operation");
                return new ApiResponse(400, "No existing data found for PUT", null);
            }

            // Step 3: Update Excel with existing payload (this would be done by calling code)
            logger.info("Existing payload for PUT: {}", mapper.writeValueAsString(existingData));

            // Step 4: Merge with any {{faker}} updates from Excel
            JsonNode requestPayload = requestNode.get("payload");
            JsonNode finalPayload = mergePayloads(existingData, requestPayload);

            // Step 5: Make PUT request
            RequestSpecification request = RestAssured.given()
                .header("Authorization", "Bearer " + authToken)
                .header("Content-Type", "application/json");

            if (tenantId != null) {
                request.header("X-TenantID", tenantId);
            }

            request.body(mapper.writeValueAsString(finalPayload));
            Response response = request.put(url);

            logger.info("PUT Response - Status: {}, Body: {}", response.getStatusCode(), response.getBody().asString());
            return new ApiResponse(response.getStatusCode(), response.getBody().asString(), response.getHeaders());

        } catch (Exception e) {
            logger.error("Error in PUT request: {}", e.getMessage());
            return new ApiResponse(500, "PUT Error: " + e.getMessage(), null);
        }
    }

    /**
     * Handle GET request
     */
    private ApiResponse handleGetRequest(JsonNode requestNode, String url, String authToken, String tenantId) {
        try {
            return makeGetRequest(url, authToken, tenantId);
        } catch (Exception e) {
            logger.error("Error in GET request: {}", e.getMessage());
            return new ApiResponse(500, "GET Error: " + e.getMessage(), null);
        }
    }

    /**
     * Handle DELETE request
     */
    private ApiResponse handleDeleteRequest(JsonNode requestNode, String url, String authToken, String tenantId) {
        try {
            RequestSpecification request = RestAssured.given()
                .header("Authorization", "Bearer " + authToken)
                .header("Content-Type", "application/json");

            if (tenantId != null) {
                request.header("X-TenantID", tenantId);
            }

            Response response = request.delete(url);

            logger.info("DELETE Response - Status: {}, Body: {}", response.getStatusCode(), response.getBody().asString());
            return new ApiResponse(response.getStatusCode(), response.getBody().asString(), response.getHeaders());

        } catch (Exception e) {
            logger.error("Error in DELETE request: {}", e.getMessage());
            return new ApiResponse(500, "DELETE Error: " + e.getMessage(), null);
        }
    }

    /**
     * Handle PATCH request
     */
    private ApiResponse handlePatchRequest(JsonNode requestNode, String url, String authToken, String tenantId) {
        try {
            JsonNode payload = requestNode.get("payload");
            if (payload != null) {
                String payloadStr = mapper.writeValueAsString(payload);
                String dynamicPayload = dataGenerator.processExcelJsonStructure(payloadStr);
                payload = mapper.readTree(dynamicPayload);
            }

            RequestSpecification request = RestAssured.given()
                .header("Authorization", "Bearer " + authToken)
                .header("Content-Type", "application/json");

            if (tenantId != null) {
                request.header("X-TenantID", tenantId);
            }

            if (payload != null) {
                request.body(mapper.writeValueAsString(payload));
            }

            Response response = request.patch(url);

            logger.info("PATCH Response - Status: {}, Body: {}", response.getStatusCode(), response.getBody().asString());
            return new ApiResponse(response.getStatusCode(), response.getBody().asString(), response.getHeaders());

        } catch (Exception e) {
            logger.error("Error in PATCH request: {}", e.getMessage());
            return new ApiResponse(500, "PATCH Error: " + e.getMessage(), null);
        }
    }

    /**
     * Make GET request
     */
    private ApiResponse makeGetRequest(String url, String authToken, String tenantId) {
        RequestSpecification request = RestAssured.given()
            .header("Authorization", "Bearer " + authToken)
            .header("Content-Type", "application/json");

        if (tenantId != null) {
            request.header("X-TenantID", tenantId);
        }

        Response response = request.get(url);

        logger.info("GET Response - Status: {}, Body: {}", response.getStatusCode(), response.getBody().asString());
        return new ApiResponse(response.getStatusCode(), response.getBody().asString(), response.getHeaders());
    }

    /**
     * Build full URL from base URL and endpoint
     */
    private String buildFullUrl(String baseUrl, String endpoint) {
        if (baseUrl.endsWith("/") && endpoint.startsWith("/")) {
            return baseUrl + endpoint.substring(1);
        } else if (!baseUrl.endsWith("/") && !endpoint.startsWith("/")) {
            return baseUrl + "/" + endpoint;
        } else {
            return baseUrl + endpoint;
        }
    }

    /**
     * Build getAll URL from save/update URL
     */
    private String buildGetAllUrl(String saveUrl) {
        // Convert /save to /getAll or /update to /getAll
        return saveUrl.replaceAll("/(save|update)$", "/getAll");
    }

    /**
     * Extract tenant ID from request
     */
    private String extractTenantId(JsonNode requestNode) {
        if (requestNode.has("tenantId")) {
            String tenantId = requestNode.get("tenantId").asText();
            // If it's a placeholder, return null to skip header
            // If it's an actual value, use it as X-Tenant-ID header
            if ("{{tenantId}}".equals(tenantId)) {
                return null; // Skip header, rely on JWT token
            } else {
                return tenantId; // Use as X-Tenant-ID header
            }
        }
        return null;
    }

    /**
     * Extract 0th index payload from getAll response
     */
    private JsonNode extractZeroIndexPayload(String responseBody) {
        try {
            JsonNode responseNode = mapper.readTree(responseBody);

            // Handle different response structures
            if (responseNode.isArray() && responseNode.size() > 0) {
                return responseNode.get(0);
            } else if (responseNode.has("data") && responseNode.get("data").isArray()) {
                JsonNode dataArray = responseNode.get("data");
                return dataArray.size() > 0 ? dataArray.get(0) : null;
            } else if (responseNode.has("content") && responseNode.get("content").isArray()) {
                JsonNode contentArray = responseNode.get("content");
                return contentArray.size() > 0 ? contentArray.get(0) : null;
            }

            return null;
        } catch (Exception e) {
            logger.error("Error extracting 0th index payload: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Merge existing payload with Excel updates
     */
    private JsonNode mergePayloads(JsonNode existingPayload, JsonNode excelPayload) {
        try {
            if (excelPayload == null) {
                return existingPayload;
            }

            // Process Excel payload for {{faker}} placeholders
            String excelPayloadStr = mapper.writeValueAsString(excelPayload);
            String processedPayloadStr = dataGenerator.processExcelJsonStructure(excelPayloadStr);
            JsonNode processedPayload = mapper.readTree(processedPayloadStr);

            // Merge processed payload with existing payload
            ObjectNode mergedPayload = existingPayload.deepCopy();
            processedPayload.fields().forEachRemaining(entry -> {
                mergedPayload.set(entry.getKey(), entry.getValue());
            });

            return mergedPayload;

        } catch (Exception e) {
            logger.error("Error merging payloads: {}", e.getMessage());
            return existingPayload;
        }
    }

    /**
     * Generate defect when test case fails
     */
    public String generateDefectForFailure(String testCaseName, String endpoint, String expectedResult,
                                         String actualResult, String errorDetails) {
        try {
            DefectDetails defect = new DefectDetails();
            defect.setTitle("API Test Failure: " + testCaseName);
            defect.setDescription(buildDefectDescription(endpoint, expectedResult, actualResult, errorDetails));
            defect.setSeverity("Medium");
            defect.setPriority("High");
            defect.setComponent("API Testing");
            defect.setEnvironment("Test");

            String defectId = defectTracker.createDefect(defect);
            logger.info("Generated defect ID: {} for failed test case: {}", defectId, testCaseName);

            return defectId;

        } catch (Exception e) {
            logger.error("Error generating defect: {}", e.getMessage());
            return "DEFECT_CREATION_FAILED";
        }
    }

    /**
     * Generate defect when test case fails with table name from Excel column 2
     */
    public String generateDefectForFailureWithTableName(String testCaseName, String endpoint, String expectedResult,
                                                       String actualResult, String errorDetails, String tableName) {
        try {
            DefectDetails defect = new DefectDetails();
            defect.setTitle("API Test Failure: " + testCaseName);
            defect.setDescription(buildDefectDescriptionWithTableName(endpoint, expectedResult, actualResult, errorDetails, tableName));
            defect.setSeverity("Medium");
            defect.setPriority("High");
            defect.setComponent(tableName); // Use table name as component
            defect.setEnvironment("Test");
            defect.setTableName(tableName); // Set table name for defect ID generation

            String defectId = defectTracker.createDefect(defect);
            logger.info("Generated defect ID: {} for failed test case: {} (Table: {})", defectId, testCaseName, tableName);

            return defectId;

        } catch (Exception e) {
            logger.error("Error generating defect: {}", e.getMessage());
            return "DEFECT_CREATION_FAILED";
        }
    }

    /**
     * Build defect description
     */
    private String buildDefectDescription(String endpoint, String expectedResult, String actualResult, String errorDetails) {
        StringBuilder description = new StringBuilder();
        description.append("**API Test Case Failed**\n\n");
        description.append("**Endpoint:** ").append(endpoint).append("\n");
        description.append("**Expected Result:** ").append(expectedResult).append("\n");
        description.append("**Actual Result:** ").append(actualResult).append("\n");
        description.append("**Error Details:** ").append(errorDetails).append("\n\n");
        description.append("**Steps to Reproduce:**\n");
        description.append("1. Execute API test for endpoint: ").append(endpoint).append("\n");
        description.append("2. Verify response matches expected result\n");
        description.append("3. Observe the failure\n\n");
        description.append("**Environment:** Test\n");
        description.append("**Generated by:** Automated API Testing Framework");

        return description.toString();
    }

    /**
     * Build defect description with table name
     */
    private String buildDefectDescriptionWithTableName(String endpoint, String expectedResult, String actualResult, String errorDetails, String tableName) {
        StringBuilder description = new StringBuilder();
        description.append("**API Test Case Failed**\n\n");
        description.append("**Table/Entity:** ").append(tableName).append("\n");
        description.append("**Endpoint:** ").append(endpoint).append("\n");
        description.append("**Expected Result:** ").append(expectedResult).append("\n");
        description.append("**Actual Result:** ").append(actualResult).append("\n");
        description.append("**Error Details:** ").append(errorDetails).append("\n\n");
        description.append("**Steps to Reproduce:**\n");
        description.append("1. Execute API test for endpoint: ").append(endpoint).append("\n");
        description.append("2. Verify response matches expected result\n");
        description.append("3. Observe the failure\n\n");
        description.append("**Environment:** Test\n");
        description.append("**Table/Entity:** ").append(tableName).append("\n");
        description.append("**Generated by:** Automated API Testing Framework");

        return description.toString();
    }
}
