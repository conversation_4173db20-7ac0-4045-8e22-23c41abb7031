# Framework Architecture Guide - Zero Hardcoded Data

## 🎯 Architecture Overview

Your framework is now **100% dynamic** with **ZERO hardcoded data** outside the constants file. Here's how it's structured:

## 📁 File Structure & Responsibilities

### **1. Constants File (ONLY Hardcoded Data)**
```
src/test/java/utils/ApiConstants.java
```
**Contains ALL hardcoded data:**
- ✅ API endpoints for all entities
- ✅ Request body templates for all operations
- ✅ Payload templates for all entities
- ✅ Foreign key mappings
- ✅ Database table mappings
- ✅ URL configurations
- ✅ Default values

### **2. Configuration Files (Dynamic Settings)**
```
src/test/resources/test-config.properties
src/test/resources/defect-config.properties
```
**Contains configurable settings:**
- ✅ Excel file paths
- ✅ Column mappings
- ✅ Database connections
- ✅ Environment settings
- ✅ Feature toggles

### **3. Framework Classes (NO Hardcoded Data)**
```
src/test/java/utils/TestConfiguration.java
src/test/java/utils/ComprehensiveApiTestEngine.java
src/test/java/testCases/ComprehensiveApiTestSuite.java
```
**Pure logic - no hardcoded values:**
- ✅ All values from configuration
- ✅ All templates from constants
- ✅ All settings from properties

## 🔧 How It Works

### **Step 1: Constants File Provides Templates**
```java
// ApiConstants.java - ALL hardcoded data here
public static String buildPostRequestBody(String entityName) {
    EntityEndpoints endpoints = getEntityEndpoints(entityName);
    String payloadTemplate = getEntityPayloadTemplate(entityName);
    return String.format(POST_TEMPLATE, endpoints.getSaveEndpoint(), payloadTemplate);
}
```

### **Step 2: Configuration Provides Settings**
```java
// TestConfiguration.java - loads from properties
public String getExcelFilePath() {
    return properties.getProperty("excel.file.path");
}

public int getBodyColumn() {
    return Integer.parseInt(properties.getProperty("excel.column.body"));
}
```

### **Step 3: Framework Uses Both Dynamically**
```java
// ComprehensiveApiTestSuite.java - NO hardcoded values
private void loadConfiguration() {
    filePath = config.getExcelFilePath();        // From properties
    baseUrl = config.getBaseUrl();              // From properties
    bodyCol = config.getBodyColumn();           // From properties
}
```

## 📊 Complete Data Flow

### **1. Test Execution Flow:**
```
Excel Data → Configuration → Constants → Framework → API → Database → Results
```

### **2. Request Body Generation:**
```
Entity Name → ApiConstants.buildPostRequestBody() → Template + Endpoint → Complete Request
```

### **3. Configuration Loading:**
```
Properties File → Environment Variables → TestConfiguration → Framework Classes
```

## 🎯 Examples of Dynamic Usage

### **Example 1: Adding New Entity**
**Only update ApiConstants.java:**
```java
// Add to ENTITY_ENDPOINTS
ENTITY_ENDPOINTS.put("NewEntity", new EntityEndpoints(
    "/api/NewEntity/save",
    "/api/NewEntity/update", 
    "/api/NewEntity/getAll",
    "/api/NewEntity/{id}",
    "/api/NewEntity/delete/{id}",
    "/api/NewEntity/patch/{id}"
));

// Add to ENTITY_PAYLOAD_TEMPLATES
ENTITY_PAYLOAD_TEMPLATES.put("NewEntity", """
    {
        "name": "{{faker}}",
        "description": "{{faker}}",
        "parentId": "{{foreign_key}}"
    }
    """);
```

**Framework automatically supports it - NO code changes needed!**

### **Example 2: Changing Excel Structure**
**Only update test-config.properties:**
```properties
# Change column mappings
excel.column.body=8
excel.column.status=11
excel.column.defectid=12
```

**Framework automatically adapts - NO code changes needed!**

### **Example 3: Different Environment**
**Only set environment variables:**
```bash
export BASE_URL=http://staging-server:8080
export EXCEL_FILE_PATH=/path/to/staging-excel.xlsx
export DATABASE_URL=******************************************
```

**Framework automatically uses new settings - NO code changes needed!**

## ✅ Zero Hardcoded Data Verification

### **❌ Before (Hardcoded):**
```java
private String filePath = "data/R Filings.xlsx";           // HARDCODED
private String baseUrl = "http://localhost:9762";          // HARDCODED
private int bodyCol = 7;                                    // HARDCODED
private int statusCol = 10;                                 // HARDCODED

for (int row = 2; row <= totalRows; row++) {               // HARDCODED
    // Hardcoded start row
}

if (lowerSheetName.contains("service") ||                  // HARDCODED
    lowerSheetName.contains("api")) {                      // HARDCODED
    // Hardcoded sheet detection
}
```

### **✅ After (Dynamic):**
```java
private String filePath = config.getExcelFilePath();       // FROM CONFIG
private String baseUrl = config.getBaseUrl();              // FROM CONFIG  
private int bodyCol = config.getBodyColumn();              // FROM CONFIG
private int statusCol = config.getStatusColumn();          // FROM CONFIG

for (int row = config.getTestStartRow(); row <= totalRows; row++) { // FROM CONFIG
    // Dynamic start row
}

String[] keywords = config.getTestSheetKeywords();         // FROM CONFIG
for (String keyword : keywords) {                          // DYNAMIC LOOP
    // Dynamic sheet detection
}
```

## 🔧 Configuration Hierarchy

### **1. Default Values (in properties file)**
```properties
excel.file.path=data/R Filings.xlsx
base.url=http://localhost:9762
excel.column.body=7
```

### **2. Environment Variables Override**
```bash
export EXCEL_FILE_PATH=/custom/path/excel.xlsx
export BASE_URL=http://custom-server:8080
export EXCEL_COL_BODY=8
```

### **3. System Properties Override**
```bash
mvn test -DEXCEL_FILE_PATH=/another/path.xlsx -DBASE_URL=http://test-server:9000
```

## 🎯 Benefits of This Architecture

### **✅ Maintainability:**
- Add new entities: Only update constants file
- Change settings: Only update properties file
- No code changes for configuration updates

### **✅ Flexibility:**
- Different environments: Different property files
- Different Excel structures: Different column mappings
- Different APIs: Different endpoint configurations

### **✅ Scalability:**
- Support unlimited entities
- Support unlimited API operations
- Support unlimited Excel structures

### **✅ Testability:**
- Easy to test with different configurations
- Easy to mock different environments
- Easy to validate different scenarios

## 📋 Quick Reference

### **To Add New Entity:**
1. Update `ApiConstants.java` with endpoints and payload template
2. Framework automatically supports all CRUD operations

### **To Change Excel Structure:**
1. Update `test-config.properties` with new column numbers
2. Framework automatically adapts

### **To Change Environment:**
1. Set environment variables or update properties file
2. Framework automatically uses new settings

### **To Add New Operation:**
1. Add template to `ApiConstants.java`
2. Add builder method to `ApiConstants.java`
3. Framework automatically supports it

**Your framework is now 100% dynamic with zero hardcoded data! 🎉**

## 🔍 Verification Commands

### **Test Dynamic Configuration:**
```bash
# Test with different Excel file
export EXCEL_FILE_PATH=/path/to/different.xlsx
mvn test

# Test with different base URL  
export BASE_URL=http://different-server:8080
mvn test

# Test with different columns
export EXCEL_COL_BODY=8
export EXCEL_COL_STATUS=11
mvn test
```

### **Test Constants Usage:**
```bash
# All request bodies come from constants
mvn test -Dtest=ComprehensiveApiTestSuite#testPostOperationDemo

# All endpoints come from constants  
mvn test -Dtest=ComprehensiveApiTestSuite#demonstrateComprehensiveFeatures
```

**Everything is configurable, nothing is hardcoded! ✅**
