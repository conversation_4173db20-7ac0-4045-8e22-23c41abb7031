package runner;

import org.testng.TestNG;
import org.testng.xml.XmlClass;
import org.testng.xml.XmlSuite;
import org.testng.xml.XmlTest;
import testCases.PhotosApiTest;
import testCases.ProductsApiTest;
import testCases.CrudOperationsTestSuite;
import testCases.AuthenticationTest;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Test Runner for CRUD Operations
 * Provides multiple ways to execute the CRUD operations test suite
 */
public class CrudTestRunner {

    public static void main(String[] args) {
        System.out.println("=== CRUD Operations Test Runner ===");
        
        if (args.length > 0) {
            String testType = args[0].toLowerCase();
            
            switch (testType) {
                case "photos":
                    runPhotosTests();
                    break;
                case "products":
                    runProductsTests();
                    break;
                case "auth":
                    runAuthenticationTests();
                    break;
                case "suite":
                    runCompleteSuite();
                    break;
                case "xml":
                    runFromXmlFile();
                    break;
                default:
                    printUsage();
                    runCompleteSuite(); // Default to complete suite
            }
        } else {
            System.out.println("No arguments provided. Running complete test suite...");
            runCompleteSuite();
        }
    }

    /**
     * Run Photos API tests only
     */
    public static void runPhotosTests() {
        System.out.println("🔄 Running Photos API Tests...");
        
        TestNG testng = new TestNG();
        testng.setTestClasses(new Class[]{PhotosApiTest.class});
        testng.run();
        
        System.out.println("✅ Photos API Tests completed!");
    }

    /**
     * Run Products API tests only
     */
    public static void runProductsTests() {
        System.out.println("🔄 Running Products API Tests...");
        
        TestNG testng = new TestNG();
        testng.setTestClasses(new Class[]{ProductsApiTest.class});
        testng.run();
        
        System.out.println("✅ Products API Tests completed!");
    }

    /**
     * Run Authentication tests only
     */
    public static void runAuthenticationTests() {
        System.out.println("🔄 Running Authentication Tests...");
        
        TestNG testng = new TestNG();
        testng.setTestClasses(new Class[]{AuthenticationTest.class});
        testng.run();
        
        System.out.println("✅ Authentication Tests completed!");
    }

    /**
     * Run complete CRUD operations test suite
     */
    public static void runCompleteSuite() {
        System.out.println("🔄 Running Complete CRUD Operations Test Suite...");
        
        TestNG testng = new TestNG();
        testng.setTestClasses(new Class[]{
            AuthenticationTest.class,
            PhotosApiTest.class,
            ProductsApiTest.class,
            CrudOperationsTestSuite.class
        });
        testng.run();
        
        System.out.println("✅ Complete CRUD Operations Test Suite completed!");
    }

    /**
     * Run tests using XML configuration file
     */
    public static void runFromXmlFile() {
        System.out.println("🔄 Running Tests from XML Configuration...");
        
        TestNG testng = new TestNG();
        List<String> suites = Arrays.asList("testng-crud-operations.xml");
        testng.setTestSuites(suites);
        testng.run();
        
        System.out.println("✅ XML Configuration Tests completed!");
    }

    /**
     * Create and run a custom test suite programmatically
     */
    public static void runCustomSuite() {
        System.out.println("🔄 Running Custom Test Suite...");
        
        // Create TestNG suite programmatically
        XmlSuite suite = new XmlSuite();
        suite.setName("CRUD Operations Custom Suite");
        suite.setParallel(XmlSuite.ParallelMode.NONE);
        
        // Create test for Photos API
        XmlTest photosTest = new XmlTest(suite);
        photosTest.setName("Photos API CRUD Tests");
        List<XmlClass> photosClasses = new ArrayList<>();
        photosClasses.add(new XmlClass(PhotosApiTest.class));
        photosTest.setXmlClasses(photosClasses);
        
        // Create test for Products API
        XmlTest productsTest = new XmlTest(suite);
        productsTest.setName("Products API CRUD Tests");
        List<XmlClass> productsClasses = new ArrayList<>();
        productsClasses.add(new XmlClass(ProductsApiTest.class));
        productsTest.setXmlClasses(productsClasses);
        
        // Create test for Complete Suite
        XmlTest suiteTest = new XmlTest(suite);
        suiteTest.setName("CRUD Operations Suite");
        List<XmlClass> suiteClasses = new ArrayList<>();
        suiteClasses.add(new XmlClass(CrudOperationsTestSuite.class));
        suiteTest.setXmlClasses(suiteClasses);
        
        // Run the suite
        TestNG testng = new TestNG();
        List<XmlSuite> suites = new ArrayList<>();
        suites.add(suite);
        testng.setXmlSuites(suites);
        testng.run();
        
        System.out.println("✅ Custom Test Suite completed!");
    }

    /**
     * Run specific test methods
     */
    public static void runSpecificMethods() {
        System.out.println("🔄 Running Specific Test Methods...");
        
        // This is an example of how to run specific methods
        // You can customize this based on your needs
        
        TestNG testng = new TestNG();
        
        // Create a suite programmatically with specific methods
        XmlSuite suite = new XmlSuite();
        suite.setName("Specific Methods Suite");
        
        XmlTest test = new XmlTest(suite);
        test.setName("Specific CRUD Methods");
        
        // Add specific classes and methods
        XmlClass photosClass = new XmlClass(PhotosApiTest.class);
        photosClass.setIncludedMethods(Arrays.asList("testCreatePhoto", "testGetAllPhotos"));
        
        XmlClass productsClass = new XmlClass(ProductsApiTest.class);
        productsClass.setIncludedMethods(Arrays.asList("testCreateProduct", "testGetAllProducts"));
        
        test.setXmlClasses(Arrays.asList(photosClass, productsClass));
        
        testng.setXmlSuites(Arrays.asList(suite));
        testng.run();
        
        System.out.println("✅ Specific Test Methods completed!");
    }

    /**
     * Print usage instructions
     */
    private static void printUsage() {
        System.out.println("\n📋 Usage Instructions:");
        System.out.println("java -cp ... runner.CrudTestRunner [option]");
        System.out.println("\nOptions:");
        System.out.println("  photos    - Run Photos API tests only");
        System.out.println("  products  - Run Products API tests only");
        System.out.println("  auth      - Run Authentication tests only");
        System.out.println("  suite     - Run complete CRUD operations suite");
        System.out.println("  xml       - Run tests from XML configuration file");
        System.out.println("  (no args) - Run complete test suite (default)");
        System.out.println("\nExamples:");
        System.out.println("  mvn exec:java -Dexec.mainClass=\"runner.CrudTestRunner\" -Dexec.args=\"photos\"");
        System.out.println("  mvn exec:java -Dexec.mainClass=\"runner.CrudTestRunner\" -Dexec.args=\"suite\"");
        System.out.println();
    }

    /**
     * Run tests with custom configuration
     */
    public static void runWithCustomConfig(String configFile) {
        System.out.println("🔄 Running Tests with Custom Configuration: " + configFile);
        
        // Set system property for configuration file
        System.setProperty("config.file", configFile);
        
        // Run the complete suite
        runCompleteSuite();
        
        System.out.println("✅ Tests with Custom Configuration completed!");
    }

    /**
     * Run tests for a specific environment
     */
    public static void runForEnvironment(String environment) {
        System.out.println("🔄 Running Tests for Environment: " + environment);
        
        // Set system property for environment
        System.setProperty("environment", environment);
        
        // Run the complete suite
        runCompleteSuite();
        
        System.out.println("✅ Tests for Environment " + environment + " completed!");
    }

    /**
     * Run tests with parallel execution
     */
    public static void runParallelTests() {
        System.out.println("🔄 Running Tests in Parallel...");
        
        XmlSuite suite = new XmlSuite();
        suite.setName("Parallel CRUD Tests");
        suite.setParallel(XmlSuite.ParallelMode.CLASSES);
        suite.setThreadCount(2);
        
        XmlTest test = new XmlTest(suite);
        test.setName("Parallel CRUD Operations");
        
        List<XmlClass> classes = Arrays.asList(
            new XmlClass(PhotosApiTest.class),
            new XmlClass(ProductsApiTest.class)
        );
        test.setXmlClasses(classes);
        
        TestNG testng = new TestNG();
        testng.setXmlSuites(Arrays.asList(suite));
        testng.run();
        
        System.out.println("✅ Parallel Tests completed!");
    }
}
