@echo off
echo ========================================
echo Simple CRUD Operations Test Runner
echo ========================================
echo.

REM Check if argument is provided
set TEST_TYPE=%1
if "%TEST_TYPE%"=="" set TEST_TYPE=all

echo Test Type: %TEST_TYPE%
echo.

REM Set Java options
set JAVA_OPTS=-Xmx2g -Dfile.encoding=UTF-8

echo Compiling test classes...
mvn clean compile test-compile

if %ERRORLEVEL% neq 0 (
    echo ERROR: Compilation failed!
    pause
    exit /b 1
)

echo.
echo Running Simple CRUD Tests...

if "%TEST_TYPE%"=="photos" (
    echo Running Photos API Tests only...
    mvn test -Dtest=testCases.PhotosApiTestNew %JAVA_OPTS%
) else if "%TEST_TYPE%"=="products" (
    echo Running Products API Tests only...
    mvn test -Dtest=testCases.ProductsApiTestNew %JAVA_OPTS%
) else if "%TEST_TYPE%"=="runner" (
    echo Running using SimpleCrudTestRunner...
    mvn exec:java -Dexec.mainClass="runner.SimpleCrudTestRunner" -Dexec.args="%TEST_TYPE%" %JAVA_OPTS%
) else (
    echo Running All CRUD Tests using TestNG XML...
    mvn test -DsuiteXmlFile=testng-simple-crud.xml %JAVA_OPTS%
)

if %ERRORLEVEL% neq 0 (
    echo ERROR: Tests failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Simple CRUD Tests Completed Successfully!
echo ========================================
echo.
echo Test Results:
echo - Check Excel files for detailed results
echo - Check target/surefire-reports/ for TestNG reports
echo - Check logs/ for execution logs
echo.
echo Validations Performed:
echo ✅ Status code validation for successful creation
echo ✅ Constraint violation testing (null/unique)
echo ✅ Error message validation
echo ✅ Request body and response matching
echo ✅ Database validation for photos and products tables
echo.

pause
