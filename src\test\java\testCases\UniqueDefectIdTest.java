package testCases;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import utils.DefectDetails;
import utils.DefectTracker;

import java.util.HashSet;
import java.util.Set;

/**
 * Test to verify unique defect ID generation with no duplicates
 */
public class UniqueDefectIdTest {
    private static final Logger logger = LoggerFactory.getLogger(UniqueDefectIdTest.class);
    
    @BeforeMethod
    public void setUp() {
        // Reset all counters before each test
        DefectTracker.resetAllDefectCounters();
        logger.info("🔄 Reset all defect counters for clean test");
    }
    
    @Test
    public void testUniqueDefectIdGeneration() {
        logger.info("=== Testing Unique Defect ID Generation ===");
        
        DefectTracker defectTracker = new DefectTracker();
        Set<String> generatedIds = new HashSet<>();
        
        // Test 1: Generate multiple defects for same table
        logger.info("📋 Testing multiple defects for BundleProduct table");
        for (int i = 1; i <= 5; i++) {
            DefectDetails defect = createDefectForTable("BundleProduct", i);
            String defectId = defectTracker.createDefect(defect);
            
            logger.info("Generated defect {}: {}", i, defectId);
            
            // Check for duplicates
            if (generatedIds.contains(defectId)) {
                logger.error("❌ DUPLICATE DEFECT ID FOUND: {}", defectId);
                throw new RuntimeException("Duplicate defect ID: " + defectId);
            } else {
                generatedIds.add(defectId);
                logger.info("✅ Unique defect ID: {}", defectId);
            }
        }
        
        // Test 2: Generate defects for different tables
        logger.info("📋 Testing defects for different tables");
        String[] tables = {"CountryMaster", "StateMaster", "CompanyDetails", "UserMaster"};
        
        for (String table : tables) {
            for (int i = 1; i <= 3; i++) {
                DefectDetails defect = createDefectForTable(table, i);
                String defectId = defectTracker.createDefect(defect);
                
                logger.info("Generated defect for {}: {}", table, defectId);
                
                // Check for duplicates
                if (generatedIds.contains(defectId)) {
                    logger.error("❌ DUPLICATE DEFECT ID FOUND: {}", defectId);
                    throw new RuntimeException("Duplicate defect ID: " + defectId);
                } else {
                    generatedIds.add(defectId);
                    logger.info("✅ Unique defect ID: {}", defectId);
                }
            }
        }
        
        // Test 3: Verify sequential numbering
        logger.info("📋 Testing sequential numbering for same table");
        for (int i = 6; i <= 10; i++) {
            DefectDetails defect = createDefectForTable("BundleProduct", i);
            String defectId = defectTracker.createDefect(defect);
            
            // Should be D_BundleProduct_006, D_BundleProduct_007, etc.
            String expectedPattern = String.format("D_BundleProduct_%03d", i);
            if (defectId.equals(expectedPattern)) {
                logger.info("✅ Sequential numbering correct: {}", defectId);
            } else {
                logger.warn("⚠️ Sequential numbering unexpected: {} (expected pattern: {})", defectId, expectedPattern);
            }
            
            generatedIds.add(defectId);
        }
        
        logger.info("=== Unique Defect ID Test Results ===");
        logger.info("Total defects generated: {}", generatedIds.size());
        logger.info("All defect IDs are unique: ✅");
        
        // Display all generated IDs
        logger.info("Generated Defect IDs:");
        generatedIds.stream().sorted().forEach(id -> logger.info("  - {}", id));
        
        // Display counter status
        logger.info("=== Counter Status ===");
        logger.info("BundleProduct count: {}", DefectTracker.getCurrentDefectCount("BundleProduct"));
        logger.info("CountryMaster count: {}", DefectTracker.getCurrentDefectCount("CountryMaster"));
        logger.info("StateMaster count: {}", DefectTracker.getCurrentDefectCount("StateMaster"));
        logger.info("CompanyDetails count: {}", DefectTracker.getCurrentDefectCount("CompanyDetails"));
        logger.info("UserMaster count: {}", DefectTracker.getCurrentDefectCount("UserMaster"));
    }
    
    @Test
    public void testDefectCounterReset() {
        logger.info("=== Testing Defect Counter Reset ===");
        
        DefectTracker defectTracker = new DefectTracker();
        
        // Generate some defects
        DefectDetails defect1 = createDefectForTable("TestTable", 1);
        String defectId1 = defectTracker.createDefect(defect1);
        logger.info("First defect: {}", defectId1);
        
        DefectDetails defect2 = createDefectForTable("TestTable", 2);
        String defectId2 = defectTracker.createDefect(defect2);
        logger.info("Second defect: {}", defectId2);
        
        logger.info("Counter before reset: {}", DefectTracker.getCurrentDefectCount("TestTable"));
        
        // Reset counter
        DefectTracker.resetDefectCounter("TestTable");
        logger.info("Counter after reset: {}", DefectTracker.getCurrentDefectCount("TestTable"));
        
        // Generate new defect - should start from 001 again
        DefectDetails defect3 = createDefectForTable("TestTable", 3);
        String defectId3 = defectTracker.createDefect(defect3);
        logger.info("Defect after reset: {}", defectId3);
        
        if (defectId3.equals("D_TestTable_001")) {
            logger.info("✅ Counter reset working correctly");
        } else {
            logger.error("❌ Counter reset not working: {}", defectId3);
        }
    }
    
    private DefectDetails createDefectForTable(String tableName, int testNumber) {
        DefectDetails defect = new DefectDetails();
        defect.setTitle(String.format("API Test Failure: API_POST_Row_%d", testNumber));
        defect.setDescription(String.format("""
            **API Test Case Failed**
            
            **Table/Entity:** %s
            **Endpoint:** /api/%s/save
            **Expected Result:** 201
            **Actual Result:** 400
            **Error Details:** Test failure for %s table
            
            **Steps to Reproduce:**
            1. Execute API test for %s
            2. Verify response matches expected result
            3. Observe the failure
            
            **Environment:** Test
            **Table/Entity:** %s
            **Generated by:** Automated API Testing Framework
            """, tableName, tableName, tableName, tableName, tableName));
        defect.setSeverity("Medium");
        defect.setPriority("High");
        defect.setComponent(tableName);
        defect.setEnvironment("Test");
        defect.setTableName(tableName); // Table name from Excel column 2
        
        return defect;
    }
}
