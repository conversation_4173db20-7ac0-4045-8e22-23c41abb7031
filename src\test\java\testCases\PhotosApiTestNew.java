package testCases;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

/**
 * Photos API Test Class
 * Tests all CRUD operations for Photos entity with comprehensive validations
 * Extends BaseTestOperations for common functionality
 * 
 * Test Focus:
 * - Status code validation for successful creation
 * - Constraint violation testing (null/unique)
 * - Error message validation
 * - Request body and response matching
 * - Database validation for photos table
 */
public class PhotosApiTestNew extends BaseTestOperations {
    private static final Logger logger = LoggerFactory.getLogger(PhotosApiTestNew.class);
    
    private String sheetName = "Photos";
    
    // Test data row numbers in Excel
    private static final int AUTH_ROW = 13;
    private static final int CREATE_ROW = 2;
    private static final int UPDATE_ROW = 3;
    private static final int GET_ALL_ROW = 4;
    private static final int GET_BY_ID_ROW = 5;
    private static final int DELETE_ROW = 6;

    @BeforeClass
    public void setup() {
        logger.info("=== Photos API Test Setup ===");
        
        try {
            // Initialize common components
            initializeCommonComponents();
            
            // Get authentication token from Excel row 13
            authToken = getAuthToken(sheetName);
            
            logger.info("Photos API Test setup completed successfully");
            
        } catch (Exception e) {
            logger.error("Failed to setup Photos API Test: " + e.getMessage());
            throw new RuntimeException("Setup failed", e);
        }
    }

    /**
     * Single test method that executes all CRUD operations for Photos
     * Reads test data from Excel sheet and validates:
     * 1. Status codes for successful creation
     * 2. Constraint violations with error messages
     * 3. Request/response body matching
     * 4. Database validation for photos table
     */
    @Test(priority = 1)
    public void testPhotosCrudOperations() {
        logger.info("=== Starting Photos CRUD Operations Test ===");
        
        try {
            // 1. Test POST operation (CREATE)
            logger.info("🔄 CRUD Operation: CREATE (POST)");
            executePostOperation(sheetName, CREATE_ROW, "photos");
            
            // 2. Test PUT operation (UPDATE) - uses created entity ID
            logger.info("🔄 CRUD Operation: UPDATE (PUT)");
            executePutOperation(sheetName, UPDATE_ROW, "photos");
            
            // 3. Test GET ALL operation (READ ALL)
            logger.info("🔄 CRUD Operation: READ ALL (GET)");
            executeGetAllOperation(sheetName, GET_ALL_ROW, "photos");
            
            // 4. Test GET BY ID operation (READ BY ID)
            logger.info("🔄 CRUD Operation: READ BY ID (GET)");
            executeGetByIdOperation(sheetName, GET_BY_ID_ROW, "photos");
            
            // 5. Test DELETE operation (DELETE)
            logger.info("🔄 CRUD Operation: DELETE");
            executeDeleteOperation(sheetName, DELETE_ROW, "photos");
            
            logger.info("✅ Photos CRUD Operations Test completed successfully");
            
        } catch (Exception e) {
            logger.error("❌ Photos CRUD Operations Test failed: " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * Additional test for constraint violations specific to Photos
     */
    @Test(priority = 2)
    public void testPhotosConstraintValidations() {
        logger.info("=== Testing Photos Constraint Validations ===");
        
        try {
            String excelPath = config.getExcelFilePath();
            
            // Read test data for constraint testing
            String url = excelUtils.getCellData(excelPath, sheetName, CREATE_ROW, COL_URL);
            String requestBody = excelUtils.getCellData(excelPath, sheetName, CREATE_ROW, COL_REQUEST_BODY);
            
            // Test Photos-specific constraints
            testPhotosNullConstraints(url, requestBody);
            testPhotosUniqueConstraints(url, requestBody);
            testPhotosDataTypeConstraints(url, requestBody);
            
            logger.info("✅ Photos Constraint Validations completed");
            
        } catch (Exception e) {
            logger.error("❌ Photos Constraint Validations failed: " + e.getMessage());
        }
    }
    
    /**
     * Test Photos-specific null constraints
     */
    private void testPhotosNullConstraints(String url, String requestBody) {
        logger.info("Testing Photos null constraints");
        
        // Test null title
        testNullField(url, requestBody, "title", "photos");
        
        // Test null url
        testNullField(url, requestBody, "url", "photos");
        
        // Test null thumbnailUrl
        testNullField(url, requestBody, "thumbnailUrl", "photos");
    }
    
    /**
     * Test Photos-specific unique constraints
     */
    private void testPhotosUniqueConstraints(String url, String requestBody) {
        logger.info("Testing Photos unique constraints");
        
        // Test duplicate photo URL (if unique constraint exists)
        testUniqueConstraint(url, requestBody, "photos");
    }
    
    /**
     * Test Photos-specific data type constraints
     */
    private void testPhotosDataTypeConstraints(String url, String requestBody) {
        logger.info("Testing Photos data type constraints");
        
        // Test invalid albumId (non-numeric)
        testInvalidDataType(url, requestBody, "albumId", "invalid_album_id", "photos");
        
        // Test invalid userId (non-numeric)
        testInvalidDataType(url, requestBody, "userId", "invalid_user_id", "photos");
    }
    
    /**
     * Helper method to test null field constraints
     */
    private void testNullField(String url, String requestBody, String fieldName, String entityType) {
        try {
            logger.info("Testing null constraint for field: {}", fieldName);
            
            // Create request body with null field
            String nullBody = requestBody.replaceAll("\"" + fieldName + "\"\\s*:\\s*\"[^\"]*\"", 
                                                   "\"" + fieldName + "\":null");
            
            var response = io.restassured.RestAssured.given()
                .contentType("application/json")
                .header("Authorization", authToken)
                .body(nullBody)
                .post(url);
            
            int statusCode = response.getStatusCode();
            String responseBody = response.getBody().asString();
            
            // Validate constraint violation
            if (statusCode == 400 || statusCode == 422) {
                logger.info("✅ Null constraint validation passed for {} - Status: {}", fieldName, statusCode);
                
                // Validate error message contains field name
                if (responseBody.toLowerCase().contains(fieldName.toLowerCase()) ||
                    responseBody.toLowerCase().contains("null") ||
                    responseBody.toLowerCase().contains("required")) {
                    logger.info("✅ Error message validation passed: {}", responseBody);
                } else {
                    logger.warn("⚠️ Error message validation failed for {}: {}", fieldName, responseBody);
                }
            } else {
                logger.warn("⚠️ Null constraint test failed for {} - Expected: 400/422, Actual: {}", 
                           fieldName, statusCode);
            }
            
        } catch (Exception e) {
            logger.error("Error testing null constraint for {}: {}", fieldName, e.getMessage());
        }
    }
    
    /**
     * Helper method to test invalid data type constraints
     */
    private void testInvalidDataType(String url, String requestBody, String fieldName, 
                                   String invalidValue, String entityType) {
        try {
            logger.info("Testing invalid data type for field: {}", fieldName);
            
            // Create request body with invalid data type
            String invalidBody = requestBody.replaceAll("\"" + fieldName + "\"\\s*:\\s*\\d+", 
                                                       "\"" + fieldName + "\":\"" + invalidValue + "\"");
            
            var response = io.restassured.RestAssured.given()
                .contentType("application/json")
                .header("Authorization", authToken)
                .body(invalidBody)
                .post(url);
            
            int statusCode = response.getStatusCode();
            String responseBody = response.getBody().asString();
            
            // Validate data type constraint violation
            if (statusCode == 400 || statusCode == 422) {
                logger.info("✅ Data type constraint validation passed for {} - Status: {}", fieldName, statusCode);
                
                // Validate error message
                if (responseBody.toLowerCase().contains("invalid") ||
                    responseBody.toLowerCase().contains("type") ||
                    responseBody.toLowerCase().contains("format")) {
                    logger.info("✅ Error message validation passed: {}", responseBody);
                } else {
                    logger.warn("⚠️ Error message validation failed for {}: {}", fieldName, responseBody);
                }
            } else {
                logger.warn("⚠️ Data type constraint test failed for {} - Expected: 400/422, Actual: {}", 
                           fieldName, statusCode);
            }
            
        } catch (Exception e) {
            logger.error("Error testing data type constraint for {}: {}", fieldName, e.getMessage());
        }
    }
}
