# Excel Structure Guide for Comprehensive API Testing

## 📊 Excel Sheet Structure

Your Excel sheet should have the following columns:

| Column | Name | Description | Example |
|--------|------|-------------|---------|
| A | Test Case | Test case identifier | TC_CountryMaster_POST_01 |
| B | Description | Test description | Create new country master record |
| C | Entity | Entity being tested | CountryMaster |
| D | Operation | HTTP operation | POST |
| E | Priority | Test priority | High |
| F | URL | Base URL (optional) | http://localhost:9762 |
| G | Request Body | Complete request structure | See examples below |
| H | Expected Result | Expected outcome | Success |
| I | Actual Result | Framework fills this | API response or DB data |
| J | Status | Framework fills this | Passed/Failed/Error |
| K | Defect ID | Framework fills this | PLANE-12345 |

## 🔧 Request Body Examples

### 1. POST Operation (Create)

```json
{
    "endpoint": "/core/api/CountryMaster/save",
    "payload": {
        "countryShortName": "{{faker}}",
        "countryFullDesc": "{{faker}}",
        "active": true,
        "priority": 1,
        "createdBy": "admin"
    },
    "type": "post",
    "tenantId": "{{tenantId}}",
    "auth": "{{auth}}"
}
```

### 2. POST with Foreign Keys

```json
{
    "endpoint": "/core/api/StateMaster/save",
    "payload": {
        "stateShortName": "{{faker}}",
        "stateFullDesc": "{{faker}}",
        "countryId": "{{foreign_key}}",
        "active": true,
        "priority": 1
    },
    "type": "post",
    "tenantId": "{{tenantId}}",
    "auth": "{{auth}}"
}
```

### 3. PUT Operation (Update)

```json
{
    "endpoint": "/core/api/CountryMaster/update",
    "payload": {
        "countryShortName": "{{faker}}",
        "countryFullDesc": "{{faker}}",
        "active": false
    },
    "type": "put",
    "tenantId": "{{tenantId}}",
    "auth": "{{auth}}"
}
```

### 4. GET All Operation

```json
{
    "endpoint": "/core/api/CountryMaster/getAll",
    "payload": null,
    "type": "get",
    "tenantId": "{{tenantId}}",
    "auth": "{{auth}}"
}
```

### 5. GET By ID Operation

```json
{
    "endpoint": "/core/api/CountryMaster/{id}",
    "payload": null,
    "type": "get",
    "tenantId": "{{tenantId}}",
    "auth": "{{auth}}"
}
```

### 6. DELETE Operation

```json
{
    "endpoint": "/core/api/CountryMaster/delete/{id}",
    "payload": null,
    "type": "delete",
    "tenantId": "{{tenantId}}",
    "auth": "{{auth}}"
}
```

### 7. PATCH Operation

```json
{
    "endpoint": "/core/api/CountryMaster/patch/{id}",
    "payload": {
        "active": false,
        "priority": 2
    },
    "type": "patch",
    "tenantId": "{{tenantId}}",
    "auth": "{{auth}}"
}
```

### 8. Complex Nested Structure with Multiple Foreign Keys

```json
{
    "endpoint": "/contact/api/CompanyKeyPersonnelDetails/save",
    "payload": {
        "companyId": "{{foreign_key}}",
        "keyPersonnelName": "{{faker}}",
        "keyPersonnelTitle": "{{faker}}",
        "contactNo": "{{faker}}",
        "emailId": "{{faker}}",
        "addressMasterId": {
            "addressLine1": "{{faker}}",
            "addressLine2": "{{faker}}",
            "city": "{{faker}}",
            "stateId": "{{foreign_key}}",
            "countryId": "{{foreign_key}}",
            "postalCode": "{{faker}}"
        },
        "firstName": "{{faker}}",
        "lastName": "{{faker}}",
        "documentsJson": null
    },
    "type": "post",
    "tenantId": "{{tenantId}}",
    "auth": "{{auth}}"
}
```

## 🎯 Dynamic Placeholders

### {{faker}}
- Replaced with dynamic data based on field name
- Examples:
  - `countryShortName` → "USA", "IND", "CAN" (3 characters)
  - `emailId` → "<EMAIL>"
  - `phoneNo` → "9876543210"
  - `firstName` → "John"
  - `companyName` → "Tech Solutions Inc"

### {{foreign_key}}
- Automatically resolved by hitting getAll API
- Framework maps field names to entities:
  - `countryId` → CountryMaster
  - `stateId` → StateMaster
  - `cityId` → CityMaster
  - `companyId` → CompanyKeyPersonnelDetails

### {{tenantId}} and {{auth}}
- Framework handles these automatically
- No need to provide actual values

## 🔄 Framework Workflow

### For POST Operations:
1. **Resolve Foreign Keys**: Hit getAll APIs for foreign key entities
2. **Generate Dynamic Data**: Replace {{faker}} with realistic data
3. **Execute POST**: Send request to API
4. **Extract ID**: Get created record ID from response
5. **Database Validation**: Compare API response with database
6. **Update Excel**: Set status, actual result, defect ID if failed

### For GET By ID Operations:
1. **Get Test Data**: Hit getAll API to get existing ID
2. **Update Endpoint**: Replace {id} with actual ID
3. **Execute GetById**: Send request to API
4. **Get Database Data**: Query database with foreign keys resolved
5. **Compare Results**: API response vs Database data
6. **Update Excel**: Set expected result (DB), actual result (API), status

### For DELETE Operations:
1. **Get Target ID**: Hit getAll API to get ID for deletion
2. **Execute DELETE**: Send delete request
3. **Verify Deletion**: Try to get deleted record (should fail)
4. **Update Excel**: Set status based on deletion success

### For PUT/PATCH Operations:
1. **Get Existing Data**: Hit getAll API to get current record
2. **Merge Data**: Combine existing data with Excel payload
3. **Resolve Foreign Keys**: Process any foreign key fields
4. **Execute Update**: Send PUT/PATCH request
5. **Database Validation**: Compare updated data
6. **Update Excel**: Set results and status

## 📋 Excel Sheet Examples

### Core Service Sheet:
- CountryMaster CRUD operations
- StateMaster with country foreign key
- CityMaster with state and country foreign keys

### Contact Service Sheet:
- CompanyKeyPersonnelDetails with multiple foreign keys
- AddressMaster operations
- ContactDetails CRUD

### Finance Service Sheet:
- Financial entity operations
- Transaction processing
- Account management

## 🐛 Defect Tracking

When tests fail, framework automatically:
1. **Creates defect** in Plane bug tracking tool
2. **Generates defect ID** (e.g., PLANE-12345)
3. **Updates Excel** with defect ID in column K
4. **Includes details**:
   - Test case name
   - API endpoint
   - Expected vs Actual results
   - Error details
   - Environment information

## ✅ Benefits

1. **Complete Automation**: No manual test case writing
2. **Foreign Key Resolution**: Automatic dependency handling
3. **Database Validation**: Ensures API-DB consistency
4. **Defect Integration**: Automatic bug tracking
5. **Dynamic Data**: Realistic test data generation
6. **Excel Integration**: Easy test management
7. **Comprehensive Coverage**: All CRUD operations
8. **Scalable**: Works with any API structure

**Your comprehensive API testing framework is ready! 🚀**
