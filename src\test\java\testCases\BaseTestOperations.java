package testCases;

import io.restassured.RestAssured;
import io.restassured.response.Response;
import org.testng.Assert;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import utils.TestConfiguration;
import utils.ExcelUtils;
import utils.DatabaseValidationUtils;
import com.github.javafaker.Faker;

/**
 * Base Test Operations class containing all common CRUD methods
 * Focuses on: Status code validation, Constraint testing, Error message validation, Database validation
 */
public class BaseTestOperations {
    protected static final Logger logger = LoggerFactory.getLogger(BaseTestOperations.class);
    protected TestConfiguration config;
    protected ExcelUtils excelUtils;
    protected DatabaseValidationUtils dbUtils;
    protected Faker faker;
    protected String authToken;
    protected String createdEntityId; // Store created entity ID for subsequent operations

    // Excel column constants
    protected static final int COL_URL = 1;           // Column A
    protected static final int COL_REQUEST_BODY = 2;  // Column B
    protected static final int COL_EXPECTED = 3;      // Column C
    protected static final int COL_ACTUAL = 4;        // Column D
    protected static final int COL_STATUS = 5;        // Column E
    protected static final int COL_DEFECT_ID = 6;     // Column F

    /**
     * Initialize common components
     */
    protected void initializeCommonComponents() {
        try {
            config = TestConfiguration.getInstance();
            excelUtils = new ExcelUtils();
            dbUtils = new DatabaseValidationUtils();
            faker = new Faker();

            // Set RestAssured base URI
            RestAssured.baseURI = config.getBaseUrl();

            logger.info("Common components initialized successfully");
        } catch (Exception e) {
            logger.error("Failed to initialize common components: " + e.getMessage());
            throw new RuntimeException("Initialization failed", e);
        }
    }

    /**
     * Get authentication token from Excel row 13
     */
    protected String getAuthToken(String sheetName) {
        try {
            logger.info("Getting authentication token from Excel sheet: {}", sheetName);

            String excelPath = config.getExcelFilePath();

            // Get auth data from row 13 (as per requirement)
            String authUrl = excelUtils.getCellData(excelPath, sheetName, 13, COL_URL);
            String authBody = excelUtils.getCellData(excelPath, sheetName, 13, COL_REQUEST_BODY);

            if (authUrl == null || authUrl.isEmpty()) {
                authUrl = "/api/auth/login";
            }

            if (authBody == null || authBody.isEmpty()) {
                authBody = "{\"username\":\"admin\",\"password\":\"admin\"}";
            }

            // Make authentication request
            Response response = RestAssured.given()
                .contentType("application/json")
                .body(authBody)
                .post(authUrl);

            if (response.getStatusCode() == 200) {
                String token = response.jsonPath().getString("token");
                if (token == null) {
                    token = "Bearer " + System.currentTimeMillis();
                }
                logger.info("Authentication successful");
                return token;
            } else {
                logger.warn("Authentication failed, using fallback token");
                return "Bearer fallback_token_" + System.currentTimeMillis();
            }

        } catch (Exception e) {
            logger.error("Error getting auth token: " + e.getMessage());
            return "Bearer error_token_" + System.currentTimeMillis();
        }
    }

    /**
     * Execute POST operation with validations
     * Tests: Status code validation, Constraint violations, Database validation
     */
    protected void executePostOperation(String sheetName, int rowNumber, String entityType) {
        logger.info("=== POST Operation for {} (Row: {}) ===", entityType, rowNumber);

        try {
            String excelPath = config.getExcelFilePath();

            // Read test data from Excel
            String url = excelUtils.getCellData(excelPath, sheetName, rowNumber, COL_URL);
            String requestBody = excelUtils.getCellData(excelPath, sheetName, rowNumber, COL_REQUEST_BODY);
            String expectedStatus = excelUtils.getCellData(excelPath, sheetName, rowNumber, COL_EXPECTED);

            logger.info("POST URL: {}", url);
            logger.info("POST Body: {}", requestBody);

            // Make POST request
            Response response = RestAssured.given()
                .contentType("application/json")
                .header("Authorization", authToken)
                .body(requestBody)
                .post(url);

            // Validate status code for successful creation
            int actualStatusCode = response.getStatusCode();
            int expectedStatusCode = Integer.parseInt(expectedStatus.trim());

            logger.info("Expected Status: {}, Actual Status: {}", expectedStatusCode, actualStatusCode);

            if (expectedStatusCode == 201) {
                Assert.assertEquals(actualStatusCode, 201, "Status code validation failed for successful creation");

                // Extract created entity ID for subsequent operations
                try {
                    createdEntityId = response.jsonPath().getString("id");
                    if (createdEntityId == null) {
                        createdEntityId = response.jsonPath().getString("data.id");
                    }
                    logger.info("✅ Created entity ID: {}", createdEntityId);
                } catch (Exception e) {
                    logger.warn("Could not extract entity ID: " + e.getMessage());
                }

                // Validate database record
                validateDatabaseRecord(response, entityType);

                // Validate request body and response matching
                validateRequestResponseMatching(requestBody, response);
            }

            // Test constraint violations (null/unique constraints)
            testConstraintViolations(url, requestBody, entityType);

            // Update Excel with results
            updateExcelWithResults(sheetName, rowNumber, response, expectedStatusCode, "POST");

        } catch (Exception e) {
            logger.error("❌ Error in POST operation: " + e.getMessage());
            updateExcelWithError(sheetName, rowNumber, e.getMessage(), "POST");
        }
    }

    /**
     * Execute PUT operation with validations
     */
    protected void executePutOperation(String sheetName, int rowNumber, String entityType) {
        logger.info("=== PUT Operation for {} (Row: {}) ===", entityType, rowNumber);

        try {
            String excelPath = config.getExcelFilePath();

            // Read test data from Excel
            String url = excelUtils.getCellData(excelPath, sheetName, rowNumber, COL_URL);
            String requestBody = excelUtils.getCellData(excelPath, sheetName, rowNumber, COL_REQUEST_BODY);
            String expectedStatus = excelUtils.getCellData(excelPath, sheetName, rowNumber, COL_EXPECTED);

            // Replace {id} with created entity ID
            url = url.replace("{id}", createdEntityId);

            logger.info("PUT URL: {}", url);
            logger.info("PUT Body: {}", requestBody);

            // Make PUT request
            Response response = RestAssured.given()
                .contentType("application/json")
                .header("Authorization", authToken)
                .body(requestBody)
                .put(url);

            // Validate status code
            int actualStatusCode = response.getStatusCode();
            int expectedStatusCode = Integer.parseInt(expectedStatus.trim());

            logger.info("Expected Status: {}, Actual Status: {}", expectedStatusCode, actualStatusCode);
            Assert.assertEquals(actualStatusCode, expectedStatusCode, "Status code validation failed for update");

            // Validate database record if successful
            if (actualStatusCode >= 200 && actualStatusCode < 300) {
                validateDatabaseRecord(response, entityType);
                validateRequestResponseMatching(requestBody, response);
            }

            // Update Excel with results
            updateExcelWithResults(sheetName, rowNumber, response, expectedStatusCode, "PUT");

        } catch (Exception e) {
            logger.error("❌ Error in PUT operation: " + e.getMessage());
            updateExcelWithError(sheetName, rowNumber, e.getMessage(), "PUT");
        }
    }

    /**
     * Execute GET ALL operation with validations
     */
    protected void executeGetAllOperation(String sheetName, int rowNumber, String entityType) {
        logger.info("=== GET ALL Operation for {} (Row: {}) ===", entityType, rowNumber);

        try {
            String excelPath = config.getExcelFilePath();

            // Read test data from Excel
            String url = excelUtils.getCellData(excelPath, sheetName, rowNumber, COL_URL);
            String expectedStatus = excelUtils.getCellData(excelPath, sheetName, rowNumber, COL_EXPECTED);

            logger.info("GET ALL URL: {}", url);

            // Make GET request
            Response response = RestAssured.given()
                .contentType("application/json")
                .header("Authorization", authToken)
                .get(url);

            // Validate status code
            int actualStatusCode = response.getStatusCode();
            int expectedStatusCode = Integer.parseInt(expectedStatus.trim());

            logger.info("Expected Status: {}, Actual Status: {}", expectedStatusCode, actualStatusCode);
            Assert.assertEquals(actualStatusCode, expectedStatusCode, "Status code validation failed for get all");

            // Validate database records if successful
            if (actualStatusCode >= 200 && actualStatusCode < 300) {
                validateGetAllWithDatabase(response, entityType);
            }

            // Update Excel with results
            updateExcelWithResults(sheetName, rowNumber, response, expectedStatusCode, "GET_ALL");

        } catch (Exception e) {
            logger.error("❌ Error in GET ALL operation: " + e.getMessage());
            updateExcelWithError(sheetName, rowNumber, e.getMessage(), "GET_ALL");
        }
    }

    /**
     * Execute GET BY ID operation with validations
     */
    protected void executeGetByIdOperation(String sheetName, int rowNumber, String entityType) {
        logger.info("=== GET BY ID Operation for {} (Row: {}) ===", entityType, rowNumber);

        try {
            String excelPath = config.getExcelFilePath();

            // Read test data from Excel
            String url = excelUtils.getCellData(excelPath, sheetName, rowNumber, COL_URL);
            String expectedStatus = excelUtils.getCellData(excelPath, sheetName, rowNumber, COL_EXPECTED);

            // Replace {id} with created entity ID
            url = url.replace("{id}", createdEntityId);

            logger.info("GET BY ID URL: {}", url);

            // Make GET request
            Response response = RestAssured.given()
                .contentType("application/json")
                .header("Authorization", authToken)
                .get(url);

            // Validate status code
            int actualStatusCode = response.getStatusCode();
            int expectedStatusCode = Integer.parseInt(expectedStatus.trim());

            logger.info("Expected Status: {}, Actual Status: {}", expectedStatusCode, actualStatusCode);
            Assert.assertEquals(actualStatusCode, expectedStatusCode, "Status code validation failed for get by id");

            // Validate database record with foreign keys if successful
            if (actualStatusCode >= 200 && actualStatusCode < 300) {
                validateGetByIdWithDatabase(response, entityType);
            }

            // Update Excel with results
            updateExcelWithResults(sheetName, rowNumber, response, expectedStatusCode, "GET_BY_ID");

        } catch (Exception e) {
            logger.error("❌ Error in GET BY ID operation: " + e.getMessage());
            updateExcelWithError(sheetName, rowNumber, e.getMessage(), "GET_BY_ID");
        }
    }

    /**
     * Execute DELETE operation with validations
     */
    protected void executeDeleteOperation(String sheetName, int rowNumber, String entityType) {
        logger.info("=== DELETE Operation for {} (Row: {}) ===", entityType, rowNumber);

        try {
            String excelPath = config.getExcelFilePath();

            // Read test data from Excel
            String url = excelUtils.getCellData(excelPath, sheetName, rowNumber, COL_URL);
            String expectedStatus = excelUtils.getCellData(excelPath, sheetName, rowNumber, COL_EXPECTED);

            // Replace {id} with created entity ID
            url = url.replace("{id}", createdEntityId);

            logger.info("DELETE URL: {}", url);

            // Make DELETE request
            Response response = RestAssured.given()
                .contentType("application/json")
                .header("Authorization", authToken)
                .delete(url);

            // Validate status code
            int actualStatusCode = response.getStatusCode();
            int expectedStatusCode = Integer.parseInt(expectedStatus.trim());

            logger.info("Expected Status: {}, Actual Status: {}", expectedStatusCode, actualStatusCode);
            Assert.assertEquals(actualStatusCode, expectedStatusCode, "Status code validation failed for delete");

            // Validate deletion in database if successful
            if (actualStatusCode >= 200 && actualStatusCode < 300) {
                validateDeletion(entityType, createdEntityId);
            }

            // Update Excel with results
            updateExcelWithResults(sheetName, rowNumber, response, expectedStatusCode, "DELETE");

        } catch (Exception e) {
            logger.error("❌ Error in DELETE operation: " + e.getMessage());
            updateExcelWithError(sheetName, rowNumber, e.getMessage(), "DELETE");
        }
    }

    /**
     * Test constraint violations (null/unique constraints)
     */
    protected void testConstraintViolations(String url, String requestBody, String entityType) {
        logger.info("=== Testing Constraint Violations for {} ===", entityType);

        try {
            // Test null constraint violation
            testNullConstraint(url, requestBody, entityType);

            // Test unique constraint violation
            testUniqueConstraint(url, requestBody, entityType);

        } catch (Exception e) {
            logger.error("Error testing constraints: " + e.getMessage());
        }
    }

    /**
     * Test null constraint violation
     */
    protected void testNullConstraint(String url, String requestBody, String entityType) {
        logger.info("Testing null constraint for {}", entityType);

        try {
            // Create request body with null required field
            String nullBody = requestBody.replace("\"title\":", "\"title\":null,\"original_title\":");

            Response response = RestAssured.given()
                .contentType("application/json")
                .header("Authorization", authToken)
                .body(nullBody)
                .post(url);

            int statusCode = response.getStatusCode();
            String responseBody = response.getBody().asString();

            // Expect 400 Bad Request for null constraint violation
            if (statusCode == 400) {
                logger.info("✅ Null constraint validation passed - Status: {}", statusCode);

                // Validate error message
                if (responseBody.toLowerCase().contains("null") ||
                    responseBody.toLowerCase().contains("required") ||
                    responseBody.toLowerCase().contains("cannot be empty")) {
                    logger.info("✅ Error message validation passed: {}", responseBody);
                } else {
                    logger.warn("⚠️ Error message validation failed: {}", responseBody);
                }
            } else {
                logger.warn("⚠️ Null constraint test failed - Expected: 400, Actual: {}", statusCode);
            }

        } catch (Exception e) {
            logger.error("Error testing null constraint: " + e.getMessage());
        }
    }

    /**
     * Test unique constraint violation
     */
    protected void testUniqueConstraint(String url, String requestBody, String entityType) {
        logger.info("Testing unique constraint for {}", entityType);

        try {
            // Make the same request twice to test unique constraint
            Response response = RestAssured.given()
                .contentType("application/json")
                .header("Authorization", authToken)
                .body(requestBody)
                .post(url);

            int statusCode = response.getStatusCode();
            String responseBody = response.getBody().asString();

            // Expect 409 Conflict or 400 Bad Request for unique constraint violation
            if (statusCode == 409 || statusCode == 400) {
                logger.info("✅ Unique constraint validation passed - Status: {}", statusCode);

                // Validate error message
                if (responseBody.toLowerCase().contains("unique") ||
                    responseBody.toLowerCase().contains("duplicate") ||
                    responseBody.toLowerCase().contains("already exists")) {
                    logger.info("✅ Error message validation passed: {}", responseBody);
                } else {
                    logger.warn("⚠️ Error message validation failed: {}", responseBody);
                }
            } else {
                logger.warn("⚠️ Unique constraint test failed - Expected: 409/400, Actual: {}", statusCode);
            }

        } catch (Exception e) {
            logger.error("Error testing unique constraint: " + e.getMessage());
        }
    }

    /**
     * Validate database record
     */
    protected void validateDatabaseRecord(Response response, String entityType) {
        try {
            logger.info("Validating database record for {}", entityType);

            if (config.isDatabaseValidationEnabled()) {
                String tableName = entityType.toLowerCase();
                String recordId = response.jsonPath().getString("id");

                if (recordId != null) {
                    // Query database to verify record exists
                    boolean recordExists = dbUtils.validateRecordExists(tableName, "id", recordId);

                    if (recordExists) {
                        logger.info("✅ Database validation passed - Record exists in {} table", tableName);
                    } else {
                        logger.warn("⚠️ Database validation failed - Record not found in {} table", tableName);
                    }
                } else {
                    logger.warn("⚠️ Cannot validate database - No ID found in response");
                }
            } else {
                logger.info("Database validation disabled");
            }

        } catch (Exception e) {
            logger.error("Error validating database record: " + e.getMessage());
        }
    }

    /**
     * Validate request body and response matching
     */
    protected void validateRequestResponseMatching(String requestBody, Response response) {
        try {
            logger.info("Validating request body and response matching");

            String responseBody = response.getBody().asString();

            // Basic validation - check if key fields match
            if (requestBody.contains("\"title\"") && responseBody.contains("\"title\"")) {
                logger.info("✅ Request-Response matching validation passed");
            } else {
                logger.warn("⚠️ Request-Response matching validation failed");
            }

        } catch (Exception e) {
            logger.error("Error validating request-response matching: " + e.getMessage());
        }
    }

    /**
     * Validate GET ALL with database
     */
    protected void validateGetAllWithDatabase(Response response, String entityType) {
        try {
            logger.info("Validating GET ALL with database for {}", entityType);

            if (config.isDatabaseValidationEnabled()) {
                String tableName = entityType.toLowerCase();

                // Get count from API response
                int apiCount = response.jsonPath().getList("$").size();

                // Get count from database
                int dbCount = dbUtils.getRecordCount(tableName);

                if (apiCount == dbCount) {
                    logger.info("✅ GET ALL database validation passed - API: {}, DB: {}", apiCount, dbCount);
                } else {
                    logger.warn("⚠️ GET ALL database validation failed - API: {}, DB: {}", apiCount, dbCount);
                }
            } else {
                logger.info("Database validation disabled");
            }

        } catch (Exception e) {
            logger.error("Error validating GET ALL with database: " + e.getMessage());
        }
    }

    /**
     * Validate GET BY ID with database (including foreign keys)
     */
    protected void validateGetByIdWithDatabase(Response response, String entityType) {
        try {
            logger.info("Validating GET BY ID with database for {}", entityType);

            if (config.isDatabaseValidationEnabled()) {
                String tableName = entityType.toLowerCase();
                String recordId = response.jsonPath().getString("id");

                if (recordId != null) {
                    // Validate record exists
                    boolean recordExists = dbUtils.validateRecordExists(tableName, "id", recordId);

                    if (recordExists) {
                        logger.info("✅ GET BY ID database validation passed");

                        // Validate foreign key relationships
                        validateForeignKeys(response, entityType);
                    } else {
                        logger.warn("⚠️ GET BY ID database validation failed - Record not found");
                    }
                } else {
                    logger.warn("⚠️ Cannot validate database - No ID found in response");
                }
            } else {
                logger.info("Database validation disabled");
            }

        } catch (Exception e) {
            logger.error("Error validating GET BY ID with database: " + e.getMessage());
        }
    }

    /**
     * Validate foreign key relationships
     */
    protected void validateForeignKeys(Response response, String entityType) {
        try {
            logger.info("Validating foreign key relationships for {}", entityType);

            if (entityType.equalsIgnoreCase("photos")) {
                // Validate albumId foreign key
                String albumId = response.jsonPath().getString("albumId");
                if (albumId != null) {
                    boolean albumExists = dbUtils.validateRecordExists("albums", "id", albumId);
                    if (albumExists) {
                        logger.info("✅ Foreign key validation passed - Album exists");
                    } else {
                        logger.warn("⚠️ Foreign key validation failed - Album not found");
                    }
                }

                // Validate userId foreign key
                String userId = response.jsonPath().getString("userId");
                if (userId != null) {
                    boolean userExists = dbUtils.validateRecordExists("users", "id", userId);
                    if (userExists) {
                        logger.info("✅ Foreign key validation passed - User exists");
                    } else {
                        logger.warn("⚠️ Foreign key validation failed - User not found");
                    }
                }
            } else if (entityType.equalsIgnoreCase("products")) {
                // Validate categoryId foreign key
                String categoryId = response.jsonPath().getString("categoryId");
                if (categoryId != null) {
                    boolean categoryExists = dbUtils.validateRecordExists("categories", "id", categoryId);
                    if (categoryExists) {
                        logger.info("✅ Foreign key validation passed - Category exists");
                    } else {
                        logger.warn("⚠️ Foreign key validation failed - Category not found");
                    }
                }
            }

        } catch (Exception e) {
            logger.error("Error validating foreign keys: " + e.getMessage());
        }
    }

    /**
     * Validate deletion
     */
    protected void validateDeletion(String entityType, String entityId) {
        try {
            logger.info("Validating deletion for {} with ID: {}", entityType, entityId);

            if (config.isDatabaseValidationEnabled()) {
                String tableName = entityType.toLowerCase();

                // Check if record still exists in database
                boolean recordExists = dbUtils.validateRecordExists(tableName, "id", entityId);

                if (!recordExists) {
                    logger.info("✅ Deletion validation passed - Record removed from database");
                } else {
                    logger.warn("⚠️ Deletion validation failed - Record still exists in database");
                }
            } else {
                logger.info("Database validation disabled");
            }

        } catch (Exception e) {
            logger.error("Error validating deletion: " + e.getMessage());
        }
    }

    /**
     * Update Excel with test results
     */
    protected void updateExcelWithResults(String sheetName, int rowNumber, Response response,
                                        int expectedStatusCode, String operation) {
        try {
            String excelPath = config.getExcelFilePath();
            String actualResponse = response.getBody().asString();
            int actualStatusCode = response.getStatusCode();

            // Update actual result column
            excelUtils.setCellData(excelPath, sheetName, rowNumber, COL_ACTUAL, actualResponse);

            // Determine test status
            String status = (actualStatusCode == expectedStatusCode) ? "PASS" : "FAIL";
            String defectId = "";

            if (status.equals("FAIL")) {
                defectId = generateDefectId(operation, "STATUS_CODE_MISMATCH");
            }

            // Update status column
            excelUtils.setCellData(excelPath, sheetName, rowNumber, COL_STATUS, status);

            // Update defect ID if failed
            if (!defectId.isEmpty()) {
                excelUtils.setCellData(excelPath, sheetName, rowNumber, COL_DEFECT_ID, defectId);
            }

            logger.info("Excel updated - Row: {}, Status: {}, DefectID: {}", rowNumber, status, defectId);

        } catch (Exception e) {
            logger.error("Error updating Excel: " + e.getMessage());
        }
    }

    /**
     * Update Excel with error information
     */
    protected void updateExcelWithError(String sheetName, int rowNumber, String errorMessage, String operation) {
        try {
            String excelPath = config.getExcelFilePath();

            // Update actual result with error
            excelUtils.setCellData(excelPath, sheetName, rowNumber, COL_ACTUAL, "ERROR: " + errorMessage);

            // Update status as FAIL
            excelUtils.setCellData(excelPath, sheetName, rowNumber, COL_STATUS, "FAIL");

            // Generate defect ID
            String defectId = generateDefectId(operation, "EXECUTION_ERROR");
            excelUtils.setCellData(excelPath, sheetName, rowNumber, COL_DEFECT_ID, defectId);

            logger.error("Excel updated with error - Row: {}, DefectID: {}", rowNumber, defectId);

        } catch (Exception e) {
            logger.error("Error updating Excel with error: " + e.getMessage());
        }
    }

    /**
     * Generate defect ID
     */
    protected String generateDefectId(String operation, String issueType) {
        return String.format("D_%s_%s_%d",
                           operation.toUpperCase(),
                           issueType.toUpperCase(),
                           System.currentTimeMillis() % 1000);
    }
}
