# How to Run CRUD Operations Test Cases

## 🚀 **Quick Start Guide**

### **Prerequisites**
1. ✅ Java 8 or higher installed
2. ✅ <PERSON><PERSON> installed and configured
3. ✅ Excel file with test data (`data/SnackHack.xlsx`)
4. ✅ API server running (if testing against live API)
5. ✅ Database accessible (if database validation is enabled)

## 📋 **Step-by-Step Setup**

### **1. Verify Project Structure**
```
RestAssuredApiTesting/
├── src/test/java/
│   ├── testCases/
│   │   ├── PhotosApiTest.java
│   │   ├── ProductsApiTest.java
│   │   └── CrudOperationsTestSuite.java
│   └── utils/
├── data/
│   └── SnackHack.xlsx
├── crud-config.properties
├── testng-crud-operations.xml
└── pom.xml
```

### **2. Configure Test Settings**
Edit `crud-config.properties`:
```properties
# Excel Configuration
excel.file.path=data/SnackHack.xlsx

# API Configuration
base.url=http://localhost:8080

# Database Configuration (optional)
database.url=*************************************
database.username=root
database.password=password
database.validation.enabled=true

# Feature Flags
defect.tracking.enabled=true
faker.data.generation.enabled=true
```

### **3. Prepare Excel File**
Ensure your Excel file (`data/SnackHack.xlsx`) has the following sheets:
- **Photos** sheet with columns: URL, Body, Expected, Actual, Status, DefectID
- **Products** sheet with same column structure
- **Auth** sheet with authentication data at row 13

## 🎯 **Running Test Cases**

### **Method 1: Using Maven Commands**

#### **Run Individual Test Classes:**
```bash
# Run Photos API tests only
mvn test -Dtest=PhotosApiTest

# Run Products API tests only
mvn test -Dtest=ProductsApiTest

# Run Authentication tests
mvn test -Dtest=AuthenticationTest

# Run specific test method
mvn test -Dtest=PhotosApiTest#testCreatePhoto
```

#### **Run Complete Test Suite:**
```bash
# Run all CRUD operations tests
mvn test -DsuiteXmlFile=testng-crud-operations.xml

# Run with specific configuration
mvn test -DsuiteXmlFile=testng-crud-operations.xml -Dconfig.file=crud-config.properties

# Run with environment parameter
mvn test -DsuiteXmlFile=testng-crud-operations.xml -Denvironment=test
```

### **Method 2: Using Batch/Shell Scripts**

#### **Windows:**
```batch
# Run the provided batch script
run-crud-tests.bat
```

#### **Linux/Mac:**
```bash
# Make script executable and run
chmod +x run-crud-tests.sh
./run-crud-tests.sh
```

### **Method 3: Using IDE (IntelliJ/Eclipse)**

#### **Run Individual Test:**
1. Right-click on test method (e.g., `testCreatePhoto()`)
2. Select "Run 'testCreatePhoto()'"

#### **Run Test Class:**
1. Right-click on test class (e.g., `PhotosApiTest.java`)
2. Select "Run 'PhotosApiTest'"

#### **Run Test Suite:**
1. Right-click on `testng-crud-operations.xml`
2. Select "Run 'testng-crud-operations.xml'"

### **Method 4: Using TestNG Programmatically**

```java
import org.testng.TestNG;

public class TestRunner {
    public static void main(String[] args) {
        TestNG testng = new TestNG();
        testng.setTestSuites(Arrays.asList("testng-crud-operations.xml"));
        testng.run();
    }
}
```

## 📊 **Test Execution Flow**

### **CRUD Operations Test Sequence:**

#### **1. Photos API Tests:**
```
PhotosApiTest.setup() → Authentication
├── testCreatePhoto() → POST operations
├── testUpdatePhoto() → PUT operations  
├── testGetAllPhotos() → GET all operations
├── testGetPhotoById() → GET by ID operations
└── testDeletePhoto() → DELETE operations
```

#### **2. Products API Tests:**
```
ProductsApiTest.setup() → Authentication
├── testCreateProduct() → POST operations
├── testUpdateProduct() → PUT operations
├── testGetAllProducts() → GET all operations  
├── testGetProductById() → GET by ID operations
└── testDeleteProduct() → DELETE operations
```

#### **3. Each CRUD Operation Tests:**
```
POST Test:
├── Successful creation + status code validation
├── Extract ID from response → validate with GetById
├── Null constraint validation
└── Unique constraint validation

PUT Test:
├── Use POST response ID for update
├── Status code validation
├── Response validation with GetById
└── Constraint validation

GET All Test:
├── Status code validation
├── Database validation (API vs DB)
└── Index-based testing

GET By ID Test:
├── Status code validation
└── Database validation with foreign keys

DELETE Test:
├── Status code validation
└── Verify deletion with GetById
```

## 📈 **Monitoring Test Execution**

### **Console Output:**
```
=== Photos API Test Setup ===
Authentication token obtained for Photos API testing
=== Testing Photo Creation (POST) ===
✅ Test Passed: Successful photo creation
🔄 CRUD Operation: CREATE (POST)
=== Testing Photo Update (PUT) ===
✅ Test Passed: Successful photo update
🔄 CRUD Operation: UPDATE (PUT)
```

### **Excel Updates:**
- ✅ **PASS**: Green background in status column
- ❌ **FAIL**: Red background with defect ID (e.g., `D_PHOTO_001`)

### **Reports Generated:**
- **HTML Report**: `test-reports/crud-operations-report.html`
- **TestNG Reports**: `target/surefire-reports/`
- **Logs**: `logs/crud-operations.log`

## 🔧 **Troubleshooting**

### **Common Issues & Solutions:**

#### **1. Authentication Failure:**
```
Error: Failed to get auth token
Solution: 
- Check Excel row 13 has correct auth data
- Verify API endpoint is accessible
- Check username/password in Excel
```

#### **2. Excel File Not Found:**
```
Error: Excel file not accessible
Solution:
- Verify file path in crud-config.properties
- Ensure file exists: data/SnackHack.xlsx
- Check file permissions
```

#### **3. Database Connection Error:**
```
Error: Database connection failed
Solution:
- Check database URL in configuration
- Verify database credentials
- Ensure database is running
- Test connection manually
```

#### **4. Compilation Errors:**
```
Error: Class not found
Solution:
mvn clean compile test
```

### **Debug Mode:**
```bash
# Run with debug logging
mvn test -DsuiteXmlFile=testng-crud-operations.xml -Dlog.level=DEBUG

# Run with detailed output
mvn test -DsuiteXmlFile=testng-crud-operations.xml -X
```

## 📋 **Test Data Requirements**

### **Excel Sheet Structure:**
| Column | Purpose | Example |
|--------|---------|---------|
| A | Test Case | Create Photo |
| B | Table Name | photos |
| C | URL | /api/photos |
| D | Request Body | {"title":"Test"} |
| E | Expected Result | 201 |
| F | Actual Result | (populated by test) |
| G | Status | (populated by test) |
| H | Defect ID | (populated if failed) |

### **Authentication Data (Row 13):**
| URL | Body | Expected | Actual | Status |
|-----|------|----------|--------|--------|
| /api/auth/login | {"username":"admin","password":"admin"} | 200 | | |

## 🎯 **Expected Results**

### **Successful Test Run:**
```
Tests run: 10, Failures: 0, Errors: 0, Skipped: 0
🎉 All tests passed successfully!
Total Defects Generated: 0
```

### **Test Reports:**
- **Excel**: Updated with PASS/FAIL status and defect IDs
- **HTML**: Interactive report with CRUD operation details
- **Console**: Detailed execution logs

## 🚀 **Next Steps After Running Tests**

1. **Review Results**: Check Excel file for test outcomes
2. **Analyze Reports**: Open HTML report for detailed analysis
3. **Fix Defects**: Address any failed test cases
4. **Database Validation**: Verify data consistency
5. **Continuous Integration**: Integrate with CI/CD pipeline

The CRUD operations framework is now ready to execute comprehensive API testing with database validation, Excel integration, and detailed reporting! 🎉
