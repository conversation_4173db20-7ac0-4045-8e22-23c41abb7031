package utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.github.javafaker.Faker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;

/**
 * Enhanced Dynamic Data Generator that preserves JSON structure and only replaces {{faker}} placeholders
 * while keeping foreign keys and other values unchanged
 */
public class DynamicDataGenerator {
    private static final Logger logger = LoggerFactory.getLogger(DynamicDataGenerator.class);
    private final Faker faker;
    private final ObjectMapper mapper;

    // Patterns for detecting placeholders
    private static final Pattern FAKER_PATTERN = Pattern.compile("\\{\\{faker\\}\\}");
    private static final Pattern FOREIGN_KEY_PATTERN = Pattern.compile("\\{\\{foreign_key\\}\\}");

    // Field type mappings for intelligent data generation
    private final Map<String, DataGenerationStrategy> fieldStrategies;

    public DynamicDataGenerator() {
        this.faker = new Faker();
        this.mapper = new ObjectMapper();
        this.fieldStrategies = initializeFieldStrategies();

        logger.info("DynamicDataGenerator initialized with universal constraints");
    }

    /**
     * Process Excel JSON structure and replace only {{faker}} placeholders with dynamic data
     * Preserves the EXACT JSON structure from Excel - no fields added or removed
     *
     * @param excelJsonStructure The JSON structure from Excel
     * @return JSON with ONLY {{faker}} placeholders replaced with dynamic data
     */
    public String processExcelJsonStructure(String excelJsonStructure) {
        try {
            logger.info("Processing Excel JSON structure (preserving exact structure): {}", excelJsonStructure);

            JsonNode rootNode = mapper.readTree(excelJsonStructure);

            // Detect table context from endpoint
            String tableContext = detectTableContext(rootNode);
            logger.info("Detected table context: {}", tableContext);

            JsonNode processedNode = processJsonNodeWithTableContext(rootNode, tableContext);

            String result = mapper.writeValueAsString(processedNode);
            logger.info("Processed result (exact structure preserved): {}", result);

            return result;

        } catch (Exception e) {
            logger.error("Error processing Excel JSON structure: {}", e.getMessage());
            return excelJsonStructure; // Return original if processing fails
        }
    }

    /**
     * Detect table context from JSON structure (endpoint, etc.)
     */
    private String detectTableContext(JsonNode rootNode) {
        // Check endpoint field for table context
        if (rootNode.has("endpoint")) {
            String endpoint = rootNode.get("endpoint").asText().toLowerCase();

            if (endpoint.contains("countrymaster") || endpoint.contains("country")) {
                return "country_master";
            } else if (endpoint.contains("statemaster") || endpoint.contains("state")) {
                return "state_master";
            } else if (endpoint.contains("citymaster") || endpoint.contains("city")) {
                return "city_master";
            }
        }

        // Check payload structure for table hints
        if (rootNode.has("payload")) {
            JsonNode payload = rootNode.get("payload");
            if (payload.has("countryShortName") && payload.has("countryFullDesc")) {
                return "country_master";
            } else if (payload.has("stateShortName") && payload.has("stateFullDesc")) {
                return "state_master";
            }
        }

        return "unknown";
    }

    /**
     * Process JSON nodes with table context awareness
     */
    private JsonNode processJsonNodeWithTableContext(JsonNode node, String tableContext) {
        return processJsonNodeWithFieldAndTableContext(node, null, tableContext);
    }

    /**
     * Recursively process JSON nodes to replace {{faker}} placeholders
     */
    private JsonNode processJsonNode(JsonNode node) {
        return processJsonNodeWithFieldContext(node, null);
    }



    /**
     * Process JSON node with field and table context for better data generation
     */
    private JsonNode processJsonNodeWithFieldAndTableContext(JsonNode node, String fieldName, String tableContext) {
        if (node.isObject()) {
            ObjectNode objectNode = mapper.createObjectNode();
            node.fields().forEachRemaining(entry -> {
                String childFieldName = entry.getKey();
                JsonNode fieldValue = entry.getValue();
                objectNode.set(childFieldName, processJsonNodeWithFieldAndTableContext(fieldValue, childFieldName, tableContext));
            });
            return objectNode;

        } else if (node.isArray()) {
            ArrayNode arrayNode = mapper.createArrayNode();
            for (JsonNode arrayElement : node) {
                arrayNode.add(processJsonNodeWithFieldAndTableContext(arrayElement, fieldName, tableContext));
            }
            return arrayNode;

        } else if (node.isTextual()) {
            String textValue = node.asText();

            if (FAKER_PATTERN.matcher(textValue).matches()) {
                return mapper.valueToTree(generateFieldContextDataWithTable(fieldName, tableContext));
            } else if (FOREIGN_KEY_PATTERN.matcher(textValue).matches()) {
                logger.debug("Found foreign key placeholder: {}", textValue);
                return node;
            } else {
                return node;
            }

        } else {
            return node;
        }
    }

    /**
     * Process JSON node with field context for better data generation
     */
    private JsonNode processJsonNodeWithFieldContext(JsonNode node, String fieldName) {
        if (node.isObject()) {
            ObjectNode objectNode = mapper.createObjectNode();
            node.fields().forEachRemaining(entry -> {
                String childFieldName = entry.getKey();
                JsonNode fieldValue = entry.getValue();
                objectNode.set(childFieldName, processJsonNodeWithFieldContext(fieldValue, childFieldName));
            });
            return objectNode;

        } else if (node.isArray()) {
            ArrayNode arrayNode = mapper.createArrayNode();
            for (JsonNode arrayElement : node) {
                arrayNode.add(processJsonNodeWithFieldContext(arrayElement, fieldName));
            }
            return arrayNode;

        } else if (node.isTextual()) {
            String textValue = node.asText();

            if (FAKER_PATTERN.matcher(textValue).matches()) {
                return mapper.valueToTree(generateFieldContextData(fieldName));
            } else if (FOREIGN_KEY_PATTERN.matcher(textValue).matches()) {
                logger.debug("Found foreign key placeholder: {}", textValue);
                return node;
            } else {
                return node;
            }

        } else {
            return node;
        }
    }

    /**
     * Generate data based on field context, table context, and constraints
     */
    private Object generateFieldContextDataWithTable(String fieldName, String tableContext) {
        if (fieldName == null) {
            return generateRandomString();
        }

        logger.debug("Generating data for field '{}' in table context '{}'", fieldName, tableContext);

        // Apply table-specific constraints for critical fields
        if ("country_master".equals(tableContext) && "countryShortName".equals(fieldName)) {
            logger.info("Applying country_master constraint: countryShortName length=3");
            return faker.regexify("[A-Z]{3}");
        } else if ("state_master".equals(tableContext) && "stateShortName".equals(fieldName)) {
            logger.info("Applying state_master constraint: stateShortName length=3");
            return faker.regexify("[A-Z]{3}");
        } else if ("city_master".equals(tableContext) && "cityShortName".equals(fieldName)) {
            logger.info("Applying city_master constraint: cityShortName length=3");
            return faker.regexify("[A-Z]{3}");
        }

        // Apply table-specific constraints directly
        if ("country_master".equals(tableContext) && "countryShortName".equals(fieldName)) {
            logger.info("Applying country_master constraint: countryShortName length=3");
            return faker.regexify("[A-Z]{3}");
        }

        // Fallback to strategy-based generation
        DataGenerationStrategy strategy = fieldStrategies.get(fieldName.toLowerCase());
        if (strategy != null) {
            return strategy.generate();
        }

        return generateByType(fieldName, "string");
    }

    /**
     * Generate data based on field context
     */
    private Object generateFieldContextData(String fieldName) {
        if (fieldName == null) {
            return generateRandomString();
        }

        DataGenerationStrategy strategy = fieldStrategies.get(fieldName.toLowerCase());
        if (strategy != null) {
            return strategy.generate();
        }

        return generateByType(fieldName, "string");
    }

    /**
     * Generate field-specific data based on field name and type
     */
    public Object generateFieldData(String fieldName, String fieldType, String placeholder) {
        if (!FAKER_PATTERN.matcher(placeholder).matches()) {
            // Not a faker placeholder, return as-is
            return placeholder;
        }

        DataGenerationStrategy strategy = fieldStrategies.get(fieldName.toLowerCase());
        if (strategy != null) {
            return strategy.generate();
        }

        // Fallback to type-based generation
        return generateByType(fieldName, fieldType);
    }

    /**
     * Generate data based on field type
     */
    private Object generateByType(String fieldName, String fieldType) {
        String lowerFieldName = fieldName.toLowerCase();

        // Name fields
        if (lowerFieldName.contains("name")) {
            if (lowerFieldName.contains("short") || lowerFieldName.contains("code")) {
                return generateShortCode();
            }
            return generateName();
        }

        // Description fields
        if (lowerFieldName.contains("desc") || lowerFieldName.contains("description")) {
            return generateDescription();
        }

        // Email fields
        if (lowerFieldName.contains("email")) {
            return faker.internet().emailAddress();
        }

        // Phone fields
        if (lowerFieldName.contains("phone") || lowerFieldName.contains("mobile")) {
            return faker.phoneNumber().phoneNumber();
        }

        // Date fields
        if (lowerFieldName.contains("date")) {
            return LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        }

        // Address fields
        if (lowerFieldName.contains("address")) {
            return faker.address().fullAddress();
        }

        // Default string generation
        return generateRandomString();
    }

    /**
     * Initialize field-specific generation strategies
     */
    private Map<String, DataGenerationStrategy> initializeFieldStrategies() {
        Map<String, DataGenerationStrategy> strategies = new HashMap<>();

        // Country fields
        strategies.put("countryshortname", () -> generateShortCode());
        strategies.put("countryfulldesc", () -> generateCountryDescription());
        strategies.put("countryname", () -> faker.country().name());

        // State fields
        strategies.put("stateshortname", () -> generateShortCode());
        strategies.put("statefulldesc", () -> generateStateDescription());
        strategies.put("statename", () -> faker.address().state());

        // Common fields
        strategies.put("email", () -> faker.internet().emailAddress());
        strategies.put("phone", () -> faker.phoneNumber().phoneNumber());
        strategies.put("mobile", () -> faker.phoneNumber().cellPhone());
        strategies.put("address", () -> faker.address().fullAddress());
        strategies.put("city", () -> faker.address().city());
        strategies.put("zipcode", () -> faker.address().zipCode());
        strategies.put("firstname", () -> faker.name().firstName());
        strategies.put("lastname", () -> faker.name().lastName());
        strategies.put("username", () -> faker.name().username());

        return strategies;
    }

    /**
     * Generate short code (2-4 characters)
     */
    private String generateShortCode() {
        return faker.regexify("[A-Z]{2}[0-9]{2}");
    }

    /**
     * Generate country description
     */
    private String generateCountryDescription() {
        return faker.country().name() + " " + faker.number().numberBetween(100000, 999999);
    }

    /**
     * Generate state description
     */
    private String generateStateDescription() {
        return faker.address().state() + " " + faker.number().numberBetween(100000, 999999);
    }

    /**
     * Generate generic name
     */
    private String generateName() {
        return faker.company().name();
    }

    /**
     * Generate description
     */
    private String generateDescription() {
        return faker.lorem().sentence(3, 5);
    }

    /**
     * Generate random string
     */
    private String generateRandomString() {
        return faker.regexify("[a-z]{2}[0-9]{2}");
    }

    /**
     * Strategy interface for field-specific data generation
     */
    @FunctionalInterface
    private interface DataGenerationStrategy {
        Object generate();
    }

    /**
     * Generate dynamic payload from Excel template - preserves exact structure
     * ONLY replaces {{faker}} placeholders, keeps everything else unchanged
     */
    public String generateDynamicPayload(String excelTemplate) {
        return processExcelJsonStructure(excelTemplate);
    }
}
