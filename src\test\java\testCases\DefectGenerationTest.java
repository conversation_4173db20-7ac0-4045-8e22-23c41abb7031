package testCases;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;
import utils.ComprehensiveApiTestEngine;
import utils.TestConfiguration;

/**
 * Test to demonstrate defect ID generation when tests fail
 */
public class DefectGenerationTest {
    private static final Logger logger = LoggerFactory.getLogger(DefectGenerationTest.class);

    private final TestConfiguration config = TestConfiguration.getInstance();
    private ComprehensiveApiTestEngine testEngine;
    private String filePath;
    private String baseUrl;
    private String authToken;

    // Column mappings from configuration
    private int urlCol;
    private int bodyCol;
    private int expectedResultCol;
    private int actualResultCol;
    private int statusCol;
    private int defectIdCol;

    @BeforeClass
    public void setup() {
        logger.info("=== Defect Generation Test Setup ===");

        // Load configuration
        loadConfiguration();

        // Initialize test engine
        testEngine = new ComprehensiveApiTestEngine(
            urlCol, bodyCol, expectedResultCol, actualResultCol, statusCol, defectIdCol
        );

        // Get authentication token
        authToken = getAuthToken();
        logger.info("Authentication token obtained for defect generation testing");
    }

    private void loadConfiguration() {
        logger.info("Loading configuration for defect generation test...");

        // Excel configuration
        filePath = config.getExcelFilePath();

        // API configuration
        baseUrl = config.getBaseUrl();

        // Column mappings
        urlCol = config.getUrlColumn();
        bodyCol = config.getBodyColumn();
        expectedResultCol = config.getExpectedResultColumn();
        actualResultCol = config.getActualResultColumn();
        statusCol = config.getStatusColumn();
        defectIdCol = config.getDefectIdColumn();

        logger.info("Defect generation test configuration loaded successfully");
    }

    private String getAuthToken() {
        try {
            basic.BasicTestCase1 bt = new basic.BasicTestCase1(
                logger, filePath, "Order Service", urlCol, bodyCol, statusCol,
                actualResultCol, expectedResultCol, 2
            );
            return bt.signIn(13); // Use row 13 for authentication
        } catch (Exception e) {
            logger.error("Error getting auth token: {}", e.getMessage());
            return "default-token";
        }
    }

    @Test
    public void testDefectGenerationWithStatusCodeMismatch() {
        logger.info("=== Testing Defect Generation with Status Code Mismatch ===");
        
        // This test will intentionally fail because:
        // - API will return 201 (Created)
        // - But Excel expected result should be set to 400 (Bad Request)
        // - This mismatch will trigger defect generation
        
        logger.info("Expected behavior:");
        logger.info("1. API call will succeed with status 201");
        logger.info("2. Excel expected result should be 400 (intentional mismatch)");
        logger.info("3. Framework will detect mismatch: Expected 400 vs Actual 201");
        logger.info("4. Defect ID will be generated in format D_TableName_001");
        logger.info("5. Excel status will be colored RED with 'Failed'");
        
        // Execute test that should fail and generate defect
        testEngine.executeComprehensiveApiTest(
            filePath, "Order Service", 14, baseUrl, authToken
        );

        logger.info("✅ Defect generation test completed");
        logger.info("Check Excel file for:");
        logger.info("  - Status column: RED background with 'Failed'");
        logger.info("  - Defect ID column: D_TableName_XXX format");
        logger.info("  - Actual Result: 201 (API response)");
        logger.info("  - Expected Result: Should remain unchanged from Excel");
    }

    @Test
    public void testMultipleDefectGeneration() {
        logger.info("=== Testing Multiple Defect Generation for Unique IDs ===");
        
        // Test multiple failures to verify unique defect IDs
        logger.info("Testing multiple failures to ensure unique defect IDs...");
        
        // Test 1: Row 15
        logger.info("Executing test for row 15...");
        testEngine.executeComprehensiveApiTest(
            filePath, "Order Service", 15, baseUrl, authToken
        );
        
        // Test 2: Row 16  
        logger.info("Executing test for row 16...");
        testEngine.executeComprehensiveApiTest(
            filePath, "Order Service", 16, baseUrl, authToken
        );
        
        // Test 3: Row 17
        logger.info("Executing test for row 17...");
        testEngine.executeComprehensiveApiTest(
            filePath, "Order Service", 17, baseUrl, authToken
        );

        logger.info("✅ Multiple defect generation test completed");
        logger.info("Check Excel file for unique defect IDs:");
        logger.info("  - Each failed test should have a unique defect ID");
        logger.info("  - Format: D_TableName_001, D_TableName_002, D_TableName_003");
        logger.info("  - No duplicate defect IDs should be generated");
    }

    @Test
    public void testDefectGenerationWithDifferentTables() {
        logger.info("=== Testing Defect Generation with Different Table Names ===");
        
        // This test demonstrates defect generation for different tables
        // Each table should have its own defect counter
        
        logger.info("Testing defect generation for different tables...");
        logger.info("Each table should maintain its own defect counter");
        
        // Execute tests for different rows (which should have different table names in column 2)
        for (int row = 18; row <= 20; row++) {
            logger.info("Executing test for row {} (different table)...", row);
            testEngine.executeComprehensiveApiTest(
                filePath, "Order Service", row, baseUrl, authToken
            );
        }

        logger.info("✅ Different tables defect generation test completed");
        logger.info("Expected results:");
        logger.info("  - Each table should have its own defect sequence");
        logger.info("  - D_Table1_001, D_Table1_002 for first table");
        logger.info("  - D_Table2_001, D_Table2_002 for second table");
        logger.info("  - D_Table3_001, D_Table3_002 for third table");
    }
}
