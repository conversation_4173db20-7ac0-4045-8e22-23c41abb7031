package utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * Configuration for defect tracking systems
 */
public class DefectConfig {
    private static final Logger logger = LoggerFactory.getLogger(DefectConfig.class);
    private static final String CONFIG_FILE = "defect-config.properties";
    
    private static DefectConfig instance;
    private final Properties properties;
    
    private DefectConfig() {
        this.properties = new Properties();
        loadConfig();
    }
    
    public static DefectConfig getInstance() {
        if (instance == null) {
            synchronized (DefectConfig.class) {
                if (instance == null) {
                    instance = new DefectConfig();
                }
            }
        }
        return instance;
    }
    
    private void loadConfig() {
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(CONFIG_FILE)) {
            if (inputStream == null) {
                logger.warn("Defect config file '{}' not found, using default values", CONFIG_FILE);
                loadDefaultConfig();
                return;
            }
            
            properties.load(inputStream);
            logger.info("Loaded defect configuration from {}", CONFIG_FILE);
            
        } catch (IOException e) {
            logger.error("Error loading defect config: {}", e.getMessage());
            loadDefaultConfig();
        }
    }
    
    private void loadDefaultConfig() {
        // Default configuration for mock/testing
        properties.setProperty("bug.tracking.system", "mock");
        properties.setProperty("jira.url", "https://your-jira-instance.atlassian.net");
        properties.setProperty("jira.project.key", "TEST");
        properties.setProperty("jira.auth.token", "your-jira-token");
        properties.setProperty("azure.url", "https://dev.azure.com/your-organization");
        properties.setProperty("azure.project", "your-project");
        properties.setProperty("azure.auth.token", "your-azure-token");
        properties.setProperty("plane.url", "https://your-plane-instance.com");
        properties.setProperty("plane.workspace.id", "your-workspace-id");
        properties.setProperty("plane.project.id", "your-project-id");
        properties.setProperty("plane.api.key", "your-plane-api-key");
        
        logger.info("Loaded default defect configuration");
    }
    
    // Bug tracking system
    public String getBugTrackingSystem() {
        return properties.getProperty("bug.tracking.system", "mock");
    }
    
    // Jira configuration
    public String getJiraUrl() {
        return properties.getProperty("jira.url");
    }
    
    public String getJiraProjectKey() {
        return properties.getProperty("jira.project.key");
    }
    
    public String getJiraAuthToken() {
        return properties.getProperty("jira.auth.token");
    }
    
    // Azure DevOps configuration
    public String getAzureUrl() {
        return properties.getProperty("azure.url");
    }
    
    public String getAzureProject() {
        return properties.getProperty("azure.project");
    }
    
    public String getAzureAuthToken() {
        return properties.getProperty("azure.auth.token");
    }
    
    // Bugzilla configuration
    public String getBugzillaUrl() {
        return properties.getProperty("bugzilla.url");
    }
    
    public String getBugzillaProduct() {
        return properties.getProperty("bugzilla.product");
    }
    
    public String getBugzillaApiKey() {
        return properties.getProperty("bugzilla.api.key");
    }
    
    // Plane configuration
    public String getPlaneUrl() {
        return properties.getProperty("plane.url");
    }
    
    public String getPlaneWorkspaceId() {
        return properties.getProperty("plane.workspace.id");
    }
    
    public String getPlaneProjectId() {
        return properties.getProperty("plane.project.id");
    }
    
    public String getPlaneApiKey() {
        return properties.getProperty("plane.api.key");
    }
    
    // Generic configuration
    public String getProperty(String key) {
        return properties.getProperty(key);
    }
    
    public String getProperty(String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }
}
