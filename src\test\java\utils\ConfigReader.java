package utils;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.Properties;

public class ConfigReader {


    public static Properties getPropertyObject() throws IOException {
        try {
            // Try the absolute path first
            FileInputStream fp = new FileInputStream("F:/BasicFramework (2)/BasicFramework/config.properties");
            Properties prop = new Properties();
            prop.load(fp);
            return prop;
        } catch (FileNotFoundException e) {
            // If absolute path fails, try relative path
            try {
                FileInputStream fp = new FileInputStream("config.properties");
                Properties prop = new Properties();
                prop.load(fp);
                return prop;
            } catch (FileNotFoundException e2) {
                // If both fail, log error and throw exception
                System.err.println("Could not find config.properties file. Tried both absolute and relative paths.");
                throw e2;
            }
        }
    }

    public static String JdbcUrl() throws IOException {

        return getPropertyObject().getProperty("JDBC_URL");

    }

    public static String JdbcUsername() throws IOException {

        return getPropertyObject().getProperty("JDBC_USER");

    }

    public static String JdbcPassword() throws IOException {
        return getPropertyObject().getProperty("JDBC_PASSWORD");
    }

    public static String JdbcConnectionTimeout() throws IOException {
        return getPropertyObject().getProperty("JDBC_CONNECTION_TIMEOUT", "5");
    }


}
