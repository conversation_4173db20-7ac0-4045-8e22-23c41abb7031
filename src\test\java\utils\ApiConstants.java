package utils;

import java.util.HashMap;
import java.util.Map;

/**
 * Constants for API endpoints and request bodies
 * Centralized configuration for all API operations
 */
public class ApiConstants {

    // Base URLs
    public static final String BASE_URL = "http://localhost:9762";
    public static final String CORE_API_BASE = "/core/api";
    public static final String CONTACT_API_BASE = "/contact/api";
    public static final String FINANCE_API_BASE = "/finance/api";

    // API Endpoints Map - Entity Name to Endpoints
    private static final Map<String, EntityEndpoints> ENTITY_ENDPOINTS = new HashMap<>();

    static {
        // Country Master
        ENTITY_ENDPOINTS.put("CountryMaster", new EntityEndpoints(
            "/core/api/CountryMaster/save",
            "/core/api/CountryMaster/update",
            "/core/api/CountryMaster/getAll",
            "/core/api/CountryMaster/{id}",
            "/core/api/CountryMaster/delete/{id}",
            "/core/api/CountryMaster/patch/{id}"
        ));

        // State Master
        ENTITY_ENDPOINTS.put("StateMaster", new EntityEndpoints(
            "/core/api/StateMaster/save",
            "/core/api/StateMaster/update",
            "/core/api/StateMaster/getAll",
            "/core/api/StateMaster/{id}",
            "/core/api/StateMaster/delete/{id}",
            "/core/api/StateMaster/patch/{id}"
        ));

        // City Master
        ENTITY_ENDPOINTS.put("CityMaster", new EntityEndpoints(
            "/core/api/CityMaster/save",
            "/core/api/CityMaster/update",
            "/core/api/CityMaster/getAll",
            "/core/api/CityMaster/{id}",
            "/core/api/CityMaster/delete/{id}",
            "/core/api/CityMaster/patch/{id}"
        ));

        // Company Key Personnel Details
        ENTITY_ENDPOINTS.put("CompanyKeyPersonnelDetails", new EntityEndpoints(
            "/contact/api/CompanyKeyPersonnelDetails/save",
            "/contact/api/CompanyKeyPersonnelDetails/update",
            "/contact/api/CompanyKeyPersonnelDetails/getAll",
            "/contact/api/CompanyKeyPersonnelDetails/{id}",
            "/contact/api/CompanyKeyPersonnelDetails/delete/{id}",
            "/contact/api/CompanyKeyPersonnelDetails/patch/{id}"
        ));

        // Add more entities as needed
    }

    // Foreign Key Mappings - Field Name to Entity Name
    private static final Map<String, String> FOREIGN_KEY_MAPPINGS = new HashMap<>();

    static {
        FOREIGN_KEY_MAPPINGS.put("countryId", "CountryMaster");
        FOREIGN_KEY_MAPPINGS.put("stateId", "StateMaster");
        FOREIGN_KEY_MAPPINGS.put("cityId", "CityMaster");
        FOREIGN_KEY_MAPPINGS.put("stateMasterId", "StateMaster");
        FOREIGN_KEY_MAPPINGS.put("countryMasterId", "CountryMaster");
        FOREIGN_KEY_MAPPINGS.put("cityMasterId", "CityMaster");
        FOREIGN_KEY_MAPPINGS.put("companyId", "CompanyKeyPersonnelDetails");
        FOREIGN_KEY_MAPPINGS.put("accountManagerId", "AccountManager");
        FOREIGN_KEY_MAPPINGS.put("adminContactId", "ContactDetails");
        FOREIGN_KEY_MAPPINGS.put("primaryContactId", "ContactDetails");
        FOREIGN_KEY_MAPPINGS.put("addressMasterId", "AddressMaster");
        // Add more foreign key mappings as needed
    }

    // Request Body Templates for All Operations
    public static final String GET_ALL_TEMPLATE = """
        {
            "endpoint": "%s",
            "payload": null,
            "type": "get",
            "tenantId": "{{tenantId}}",
            "auth": "{{auth}}"
        }
        """;

    public static final String GET_BY_ID_TEMPLATE = """
        {
            "endpoint": "%s",
            "payload": null,
            "type": "get",
            "tenantId": "{{tenantId}}",
            "auth": "{{auth}}"
        }
        """;

    public static final String DELETE_TEMPLATE = """
        {
            "endpoint": "%s",
            "payload": null,
            "type": "delete",
            "tenantId": "{{tenantId}}",
            "auth": "{{auth}}"
        }
        """;

    public static final String POST_TEMPLATE = """
        {
            "endpoint": "%s",
            "payload": %s,
            "type": "post",
            "tenantId": "{{tenantId}}",
            "auth": "{{auth}}"
        }
        """;

    public static final String PUT_TEMPLATE = """
        {
            "endpoint": "%s",
            "payload": %s,
            "type": "put",
            "tenantId": "{{tenantId}}",
            "auth": "{{auth}}"
        }
        """;

    public static final String PATCH_TEMPLATE = """
        {
            "endpoint": "%s",
            "payload": %s,
            "type": "patch",
            "tenantId": "{{tenantId}}",
            "auth": "{{auth}}"
        }
        """;

    // Database Table Mappings
    private static final Map<String, String> ENTITY_TO_TABLE = new HashMap<>();

    static {
        ENTITY_TO_TABLE.put("CountryMaster", "country_master");
        ENTITY_TO_TABLE.put("StateMaster", "state_master");
        ENTITY_TO_TABLE.put("CityMaster", "city_master");
        ENTITY_TO_TABLE.put("CompanyKeyPersonnelDetails", "company_key_personnel_details");
        // Add more entity to table mappings
    }

    // Getter methods
    public static EntityEndpoints getEntityEndpoints(String entityName) {
        return ENTITY_ENDPOINTS.get(entityName);
    }

    public static String getForeignKeyEntity(String fieldName) {
        return FOREIGN_KEY_MAPPINGS.get(fieldName);
    }

    public static String getTableName(String entityName) {
        return ENTITY_TO_TABLE.get(entityName);
    }

    public static Map<String, String> getAllForeignKeyMappings() {
        return new HashMap<>(FOREIGN_KEY_MAPPINGS);
    }

    public static String getEntityPayloadTemplate(String entityName) {
        return ENTITY_PAYLOAD_TEMPLATES.get(entityName);
    }

    public static Map<String, String> getAllEntityPayloadTemplates() {
        return new HashMap<>(ENTITY_PAYLOAD_TEMPLATES);
    }

    // Inner class for entity endpoints
    public static class EntityEndpoints {
        private final String saveEndpoint;
        private final String updateEndpoint;
        private final String getAllEndpoint;
        private final String getByIdEndpoint;
        private final String deleteEndpoint;
        private final String patchEndpoint;

        public EntityEndpoints(String saveEndpoint, String updateEndpoint, String getAllEndpoint,
                             String getByIdEndpoint, String deleteEndpoint, String patchEndpoint) {
            this.saveEndpoint = saveEndpoint;
            this.updateEndpoint = updateEndpoint;
            this.getAllEndpoint = getAllEndpoint;
            this.getByIdEndpoint = getByIdEndpoint;
            this.deleteEndpoint = deleteEndpoint;
            this.patchEndpoint = patchEndpoint;
        }

        public String getSaveEndpoint() { return saveEndpoint; }
        public String getUpdateEndpoint() { return updateEndpoint; }
        public String getAllEndpoint() { return getAllEndpoint; }
        public String getGetByIdEndpoint() { return getByIdEndpoint; }
        public String getDeleteEndpoint() { return deleteEndpoint; }
        public String getPatchEndpoint() { return patchEndpoint; }

        public String getGetByIdEndpoint(String id) {
            return getByIdEndpoint.replace("{id}", id);
        }

        public String getDeleteEndpoint(String id) {
            return deleteEndpoint.replace("{id}", id);
        }

        public String getPatchEndpoint(String id) {
            return patchEndpoint.replace("{id}", id);
        }
    }

    // Payload Templates for Each Entity
    private static final Map<String, String> ENTITY_PAYLOAD_TEMPLATES = new HashMap<>();

    static {
        // Country Master Payload Template
        ENTITY_PAYLOAD_TEMPLATES.put("CountryMaster", """
            {
                "countryShortName": "{{faker}}",
                "countryFullDesc": "{{faker}}",
                "active": true,
                "priority": 1,
                "createdBy": "admin"
            }
            """);

        // State Master Payload Template
        ENTITY_PAYLOAD_TEMPLATES.put("StateMaster", """
            {
                "stateShortName": "{{faker}}",
                "stateFullDesc": "{{faker}}",
                "countryId": "{{foreign_key}}",
                "active": true,
                "priority": 1,
                "createdBy": "admin"
            }
            """);

        // City Master Payload Template
        ENTITY_PAYLOAD_TEMPLATES.put("CityMaster", """
            {
                "cityShortName": "{{faker}}",
                "cityFullDesc": "{{faker}}",
                "stateId": "{{foreign_key}}",
                "countryId": "{{foreign_key}}",
                "active": true,
                "priority": 1,
                "createdBy": "admin"
            }
            """);

        // Company Key Personnel Details Payload Template
        ENTITY_PAYLOAD_TEMPLATES.put("CompanyKeyPersonnelDetails", """
            {
                "companyId": {
                    "companyName": "{{faker}}",
                    "phoneNo": "{{faker}}",
                    "companyEmail": "{{faker}}",
                    "websiteUrl": "http://www.example.com",
                    "fax": "{{faker}}",
                    "accountManagerId": {
                        "id": "{{foreign_key}}",
                        "firstName": "{{faker}}",
                        "lastName": "{{faker}}",
                        "email": "{{faker}}",
                        "phone": "{{faker}}"
                    },
                    "adminContactId": {
                        "id": "{{foreign_key}}",
                        "salutation": "Mr",
                        "firstName": "{{faker}}",
                        "lastName": "{{faker}}",
                        "displayName": "{{faker}}",
                        "jobTitle": "{{faker}}",
                        "phone": "{{faker}}",
                        "fax": "{{faker}}",
                        "emailId": "{{faker}}",
                        "mobileNo": "{{faker}}"
                    },
                    "primaryContactId": {
                        "id": "{{foreign_key}}",
                        "salutation": "Ms",
                        "firstName": "{{faker}}",
                        "lastName": "{{faker}}",
                        "displayName": "{{faker}}",
                        "jobTitle": "{{faker}}",
                        "phone": "{{faker}}",
                        "emailId": "{{faker}}",
                        "mobileNo": "{{faker}}"
                    },
                    "industryId": 1567890,
                    "organizerContactId": "{{foreign_key}}",
                    "entityType": 1,
                    "naicsCode": "{{faker}}",
                    "einNo": "{{faker}}",
                    "entityNo": "{{faker}}",
                    "taxYearEnd": "2024",
                    "registeredAgentName": "{{faker}}",
                    "registeredAgentAddress": "{{faker}}",
                    "registeredAgentEmail": "{{faker}}",
                    "registeredAgentContactNo": "{{faker}}",
                    "dbaName": "{{faker}}",
                    "fkaName": "{{faker}}",
                    "serviceStateId": "{{foreign_key}}",
                    "domesticStateId": "{{foreign_key}}",
                    "statusId": 0
                },
                "keyPersonnelTypeId": 1,
                "keyPersonnelName": "{{faker}}",
                "keyPersonnelTitle": "{{faker}}",
                "amount": 0,
                "contactNo": "{{faker}}",
                "emailId": "{{faker}}",
                "addressMasterId": {
                    "city": "{{faker}}",
                    "addressType": {
                        "id": 1000,
                        "type": "Principal"
                    },
                    "stateId": "{{foreign_key}}",
                    "postalCode": "{{faker}}",
                    "addressLine1": "{{faker}}",
                    "addressLine2": "{{faker}}",
                    "countryId": "{{foreign_key}}"
                },
                "firstName": "{{faker}}",
                "middleName": "{{faker}}",
                "lastName": "{{faker}}",
                "suffix": "sr",
                "documentsJson": null,
                "documentIds": null
            }
            """);

        // Add more entity payload templates as needed
    }

    // Authentication endpoints
    public static final String SIGN_IN_ENDPOINT = "/auth/api/signin";
    public static final String SIGN_IN_REQUEST_BODY = """
        {
            "endpoint": "/auth/api/signin",
            "payload": {
                "username": "<EMAIL>",
                "password": "Snehal@123"
            },
            "type": "post",
            "tenantId": "{{tenantId}}",
            "auth": "{{auth}}"
        }
        """;

    // Complete Request Body Builders - ALL HARDCODED DATA HERE

    /**
     * Build complete POST request body for any entity
     */
    public static String buildPostRequestBody(String entityName) {
        EntityEndpoints endpoints = getEntityEndpoints(entityName);
        String payloadTemplate = getEntityPayloadTemplate(entityName);

        if (endpoints == null || payloadTemplate == null) {
            return null;
        }

        return String.format(POST_TEMPLATE, endpoints.getSaveEndpoint(), payloadTemplate);
    }

    /**
     * Build complete PUT request body for any entity
     */
    public static String buildPutRequestBody(String entityName) {
        EntityEndpoints endpoints = getEntityEndpoints(entityName);
        String payloadTemplate = getEntityPayloadTemplate(entityName);

        if (endpoints == null || payloadTemplate == null) {
            return null;
        }

        return String.format(PUT_TEMPLATE, endpoints.getUpdateEndpoint(), payloadTemplate);
    }

    /**
     * Build complete GET All request body for any entity
     */
    public static String buildGetAllRequestBody(String entityName) {
        EntityEndpoints endpoints = getEntityEndpoints(entityName);

        if (endpoints == null) {
            return null;
        }

        return String.format(GET_ALL_TEMPLATE, endpoints.getAllEndpoint());
    }

    /**
     * Build complete GET By ID request body for any entity
     */
    public static String buildGetByIdRequestBody(String entityName, String id) {
        EntityEndpoints endpoints = getEntityEndpoints(entityName);

        if (endpoints == null) {
            return null;
        }

        return String.format(GET_BY_ID_TEMPLATE, endpoints.getGetByIdEndpoint(id));
    }

    /**
     * Build complete DELETE request body for any entity
     */
    public static String buildDeleteRequestBody(String entityName, String id) {
        EntityEndpoints endpoints = getEntityEndpoints(entityName);

        if (endpoints == null) {
            return null;
        }

        return String.format(DELETE_TEMPLATE, endpoints.getDeleteEndpoint(id));
    }

    /**
     * Build complete PATCH request body for any entity
     */
    public static String buildPatchRequestBody(String entityName, String id, String partialPayload) {
        EntityEndpoints endpoints = getEntityEndpoints(entityName);

        if (endpoints == null) {
            return null;
        }

        return String.format(PATCH_TEMPLATE, endpoints.getPatchEndpoint(id), partialPayload);
    }

    // Demo Request Bodies - Examples for Documentation

    public static final String DEMO_COUNTRY_MASTER_POST = buildPostRequestBody("CountryMaster");
    public static final String DEMO_STATE_MASTER_POST = buildPostRequestBody("StateMaster");
    public static final String DEMO_CITY_MASTER_POST = buildPostRequestBody("CityMaster");
    public static final String DEMO_COMPANY_PERSONNEL_POST = buildPostRequestBody("CompanyKeyPersonnelDetails");

    // URL Configurations
    public static final String DEFAULT_TENANT_ID = "{{tenantId}}";
    public static final String DEFAULT_AUTH_TOKEN = "{{auth}}";

    // Test Data Configurations
    public static final String FAKER_PLACEHOLDER = "{{faker}}";
    public static final String FOREIGN_KEY_PLACEHOLDER = "{{foreign_key}}";

    // API Response Field Names
    public static final String[] ID_FIELD_NAMES = {"id", "Id", "ID", "entityId", "recordId"};

    // Database Connection Configurations
    public static final String DEFAULT_DB_SCHEMA = "core";
    public static final String DEFAULT_DB_PORT = "5432";
    public static final String DEFAULT_DB_HOST = "localhost";
    public static final String DEFAULT_DB_NAME = "rfilings";
}
