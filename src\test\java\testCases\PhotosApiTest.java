package testCases;

// BasicTestCase1 removed - using direct implementation
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;
import utils.*;
import io.restassured.response.Response;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.javafaker.Faker;
import java.util.HashMap;
import java.util.Map;

/**
 * CRUD Operations Test Class for Photos Table
 * Handles POST, PUT, GET, GET ALL, and DELETE operations with database validation
 */
public class PhotosApiTest {
    private static final Logger logger = LoggerFactory.getLogger(PhotosApiTest.class);

    // Configuration
    private final TestConfiguration config = TestConfiguration.getInstance();
    private String filePath;
    private String baseUrl;
    private String authToken;
    private String sheetName = "Photos";

    // Column mappings
    private int urlCol;
    private int bodyCol;
    private int expectedResultCol;
    private int actualResultCol;
    private int statusCol;
    private int defectIdCol;
    private int tableNameCol;

    // Utilities
    private ExcelUtils excelUtils;
    private DatabaseValidationUtils dbUtils;
    private DynamicDataGenerator dataGenerator;
    private DefectTracker defectTracker;
    private Faker faker;
    private ObjectMapper objectMapper;

    // Test data storage
    private String lastCreatedId;

    @BeforeClass
    public void setup() {
        logger.info("=== Photos API Test Setup ===");

        // Load configuration
        loadConfiguration();

        // Initialize utilities
        initializeUtilities();

        // Get authentication token
        authToken = getAuthToken();
        logger.info("Authentication token obtained for Photos API testing");
    }

    /**
     * Load configuration from properties
     */
    private void loadConfiguration() {
        logger.info("Loading configuration for Photos API...");

        filePath = config.getExcelFilePath();
        baseUrl = config.getBaseUrl();

        // Column mappings
        urlCol = config.getUrlColumn();
        bodyCol = config.getBodyColumn();
        expectedResultCol = config.getExpectedResultColumn();
        actualResultCol = config.getActualResultColumn();
        statusCol = config.getStatusColumn();
        defectIdCol = config.getDefectIdColumn();
        tableNameCol = 2; // Table name column

        logger.info("Photos API configuration loaded successfully");
    }

    /**
     * Initialize utility classes
     */
    private void initializeUtilities() {
        excelUtils = new ExcelUtils();
        dbUtils = new DatabaseValidationUtils();
        dataGenerator = new DynamicDataGenerator();
        defectTracker = new DefectTracker();
        faker = new Faker();
        objectMapper = new ObjectMapper();
    }

    /**
     * Get authentication token
     */
    private String getAuthToken() {
        try {
            BasicTestCase1 bt = new BasicTestCase1(
                logger, filePath, sheetName, urlCol, bodyCol, statusCol,
                actualResultCol, expectedResultCol, tableNameCol
            );
            return bt.signIn(13); // Auth row number
        } catch (Exception e) {
            logger.error("Failed to get auth token: " + e.getMessage());
            return null;
        }
    }

    /**
     * POST API Test - Create Photo
     * Tests successful creation, status code validation, null constraints, unique constraints
     */
    @Test(priority = 1)
    public void testCreatePhoto() {
        logger.info("=== Testing Photo Creation (POST) ===");

        int testRow = 14; // Starting row for POST tests

        try {
            // Test 1: Successful creation with status code validation
            testSuccessfulPhotoCreation(testRow);

            // Test 2: Null constraint validation
            testPhotoNullConstraints(testRow + 1);

            // Test 3: Unique constraint validation
            testPhotoUniqueConstraints(testRow + 2);

        } catch (Exception e) {
            logger.error("Error in testCreatePhoto: " + e.getMessage());
            handleTestFailure(testRow, "POST Test Failed", e.getMessage());
        }
    }

    /**
     * Test successful photo creation with status code and response validation
     */
    private void testSuccessfulPhotoCreation(int rowNum) throws Exception {
        logger.info("Testing successful photo creation at row {}", rowNum);

        // Generate dynamic test data using Faker
        String requestBody = generatePhotoRequestBody();

        // Update Excel with generated request body
        excelUtils.setCellData(filePath, sheetName, rowNum, bodyCol, requestBody);

        // Make POST request
        Response postResponse = makePostRequest(rowNum, requestBody);

        if (postResponse != null) {
            // Validate status code
            int actualStatusCode = postResponse.getStatusCode();
            String expectedStatusCode = excelUtils.getCellData(filePath, sheetName, rowNum, expectedResultCol);

            if (String.valueOf(actualStatusCode).equals(expectedStatusCode.trim())) {
                // Extract ID from response
                lastCreatedId = extractIdFromResponse(postResponse);

                if (lastCreatedId != null) {
                    // Hit GetById API to validate creation
                    validateCreationWithGetById(rowNum, lastCreatedId);

                    // Update Excel with success
                    updateExcelWithSuccess(rowNum, postResponse.asString(), "Photo created successfully");
                } else {
                    updateExcelWithFailure(rowNum, "Failed to extract ID from response", postResponse.asString());
                }
            } else {
                updateExcelWithFailure(rowNum,
                    "Status code mismatch. Expected: " + expectedStatusCode + ", Actual: " + actualStatusCode,
                    postResponse.asString());
            }
        }
    }

    /**
     * Test null constraint validation for photo creation
     */
    private void testPhotoNullConstraints(int rowNum) throws Exception {
        logger.info("Testing photo null constraints at row {}", rowNum);

        // Generate request body with null required field
        String requestBodyWithNull = generatePhotoRequestBodyWithNullField();

        // Update Excel with request body
        excelUtils.setCellData(filePath, sheetName, rowNum, bodyCol, requestBodyWithNull);

        // Make POST request
        Response response = makePostRequest(rowNum, requestBodyWithNull);

        if (response != null) {
            // Get expected error message from Excel
            String expectedError = excelUtils.getCellData(filePath, sheetName, rowNum, expectedResultCol);
            String actualResponse = response.asString();

            // Check if actual response contains expected error message
            if (actualResponse.toLowerCase().contains(expectedError.toLowerCase()) ||
                response.getStatusCode() >= 400) {
                updateExcelWithSuccess(rowNum, actualResponse, "Null constraint validation passed");
            } else {
                updateExcelWithFailure(rowNum, "Null constraint validation failed", actualResponse);
            }
        }
    }

    /**
     * Test unique constraint validation for photo creation
     */
    private void testPhotoUniqueConstraints(int rowNum) throws Exception {
        logger.info("Testing photo unique constraints at row {}", rowNum);

        // Use the same data as successful creation to trigger unique constraint
        if (lastCreatedId != null) {
            String duplicateRequestBody = generateDuplicatePhotoRequestBody();

            // Update Excel with request body
            excelUtils.setCellData(filePath, sheetName, rowNum, bodyCol, duplicateRequestBody);

            // Make POST request
            Response response = makePostRequest(rowNum, duplicateRequestBody);

            if (response != null) {
                // Get expected error message from Excel
                String expectedError = excelUtils.getCellData(filePath, sheetName, rowNum, expectedResultCol);
                String actualResponse = response.asString();

                // Check if actual response contains expected error message
                if (actualResponse.toLowerCase().contains(expectedError.toLowerCase()) ||
                    response.getStatusCode() >= 400) {
                    updateExcelWithSuccess(rowNum, actualResponse, "Unique constraint validation passed");
                } else {
                    updateExcelWithFailure(rowNum, "Unique constraint validation failed", actualResponse);
                }
            }
        }
    }

    /**
     * PUT API Test - Update Photo
     * Tests successful update, status code validation, null constraints, unique constraints
     */
    @Test(priority = 2, dependsOnMethods = "testCreatePhoto")
    public void testUpdatePhoto() {
        logger.info("=== Testing Photo Update (PUT) ===");

        int testRow = 17; // Starting row for PUT tests

        try {
            if (lastCreatedId != null) {
                // Test 1: Successful update with status code validation
                testSuccessfulPhotoUpdate(testRow);

                // Test 2: Null constraint validation for update
                testPhotoUpdateNullConstraints(testRow + 1);

                // Test 3: Unique constraint validation for update
                testPhotoUpdateUniqueConstraints(testRow + 2);
            } else {
                logger.warn("No created photo ID available for update tests");
            }

        } catch (Exception e) {
            logger.error("Error in testUpdatePhoto: " + e.getMessage());
            handleTestFailure(testRow, "PUT Test Failed", e.getMessage());
        }
    }

    /**
     * Test successful photo update
     */
    private void testSuccessfulPhotoUpdate(int rowNum) throws Exception {
        logger.info("Testing successful photo update at row {}", rowNum);

        // Generate updated test data using Faker
        String updateRequestBody = generatePhotoUpdateRequestBody(lastCreatedId);

        // Update Excel with generated request body
        excelUtils.setCellData(filePath, sheetName, rowNum, bodyCol, updateRequestBody);

        // Make PUT request
        Response putResponse = makePutRequest(rowNum, updateRequestBody);

        if (putResponse != null) {
            // Validate status code
            int actualStatusCode = putResponse.getStatusCode();
            String expectedStatusCode = excelUtils.getCellData(filePath, sheetName, rowNum, expectedResultCol);

            if (String.valueOf(actualStatusCode).equals(expectedStatusCode.trim())) {
                // Extract ID from response and validate with GetById
                String updatedId = extractIdFromResponse(putResponse);
                if (updatedId != null) {
                    validateUpdateWithGetById(rowNum, updatedId);
                    updateExcelWithSuccess(rowNum, putResponse.asString(), "Photo updated successfully");
                } else {
                    updateExcelWithFailure(rowNum, "Failed to extract ID from update response", putResponse.asString());
                }
            } else {
                updateExcelWithFailure(rowNum,
                    "Status code mismatch. Expected: " + expectedStatusCode + ", Actual: " + actualStatusCode,
                    putResponse.asString());
            }
        }
    }

    /**
     * Test null constraint validation for photo update
     */
    private void testPhotoUpdateNullConstraints(int rowNum) throws Exception {
        logger.info("Testing photo update null constraints at row {}", rowNum);

        // Generate update request body with null required field
        String updateRequestBodyWithNull = generatePhotoUpdateRequestBodyWithNullField(lastCreatedId);

        // Update Excel with request body
        excelUtils.setCellData(filePath, sheetName, rowNum, bodyCol, updateRequestBodyWithNull);

        // Make PUT request
        Response response = makePutRequest(rowNum, updateRequestBodyWithNull);

        if (response != null) {
            // Get expected error message from Excel
            String expectedError = excelUtils.getCellData(filePath, sheetName, rowNum, expectedResultCol);
            String actualResponse = response.asString();

            // Check if actual response contains expected error message
            if (actualResponse.toLowerCase().contains(expectedError.toLowerCase()) ||
                response.getStatusCode() >= 400) {
                updateExcelWithSuccess(rowNum, actualResponse, "Update null constraint validation passed");
            } else {
                updateExcelWithFailure(rowNum, "Update null constraint validation failed", actualResponse);
            }
        }
    }

    /**
     * Test unique constraint validation for photo update
     */
    private void testPhotoUpdateUniqueConstraints(int rowNum) throws Exception {
        logger.info("Testing photo update unique constraints at row {}", rowNum);

        // Generate update request body that violates unique constraint
        String duplicateUpdateRequestBody = generateDuplicatePhotoUpdateRequestBody(lastCreatedId);

        // Update Excel with request body
        excelUtils.setCellData(filePath, sheetName, rowNum, bodyCol, duplicateUpdateRequestBody);

        // Make PUT request
        Response response = makePutRequest(rowNum, duplicateUpdateRequestBody);

        if (response != null) {
            // Get expected error message from Excel
            String expectedError = excelUtils.getCellData(filePath, sheetName, rowNum, expectedResultCol);
            String actualResponse = response.asString();

            // Check if actual response contains expected error message
            if (actualResponse.toLowerCase().contains(expectedError.toLowerCase()) ||
                response.getStatusCode() >= 400) {
                updateExcelWithSuccess(rowNum, actualResponse, "Update unique constraint validation passed");
            } else {
                updateExcelWithFailure(rowNum, "Update unique constraint validation failed", actualResponse);
            }
        }
    }

    /**
     * GET ALL API Test - Retrieve all photos with database validation
     */
    @Test(priority = 3)
    public void testGetAllPhotos() {
        logger.info("=== Testing Get All Photos (GET ALL) ===");

        int testRow = 20; // Starting row for GET ALL tests

        try {
            // Test with different indices (0, 1, 2, 3...n)
            for (int index = 0; index < 5; index++) {
                testGetAllPhotosWithIndex(testRow + index, index);
            }

        } catch (Exception e) {
            logger.error("Error in testGetAllPhotos: " + e.getMessage());
            handleTestFailure(testRow, "GET ALL Test Failed", e.getMessage());
        }
    }

    /**
     * Test get all photos with specific index
     */
    private void testGetAllPhotosWithIndex(int rowNum, int index) throws Exception {
        logger.info("Testing get all photos with index {} at row {}", index, rowNum);

        // Get request body from Excel
        String requestBody = excelUtils.getCellData(filePath, sheetName, rowNum, bodyCol);

        // Make GET ALL request
        Response getAllResponse = makeGetAllRequest(rowNum, requestBody);

        if (getAllResponse != null) {
            // Validate status code
            int actualStatusCode = getAllResponse.getStatusCode();
            String expectedStatusCode = excelUtils.getCellData(filePath, sheetName, rowNum, expectedResultCol);

            if (String.valueOf(actualStatusCode).equals(expectedStatusCode.trim())) {
                // Parse response array and get specific index
                JsonNode responseArray = objectMapper.readTree(getAllResponse.asString());

                if (responseArray.isArray() && responseArray.size() > index) {
                    JsonNode photoAtIndex = responseArray.get(index);
                    String photoId = photoAtIndex.get("id").asText();

                    // Store in expected result
                    excelUtils.setCellData(filePath, sheetName, rowNum, expectedResultCol, photoAtIndex.toString());

                    // Query database for this photo
                    String dbResult = dbUtils.getPhotoFromDatabase(photoId);

                    // Store in actual result
                    excelUtils.setCellData(filePath, sheetName, rowNum, actualResultCol, dbResult);

                    // Compare API response with database
                    if (compareApiWithDatabase(photoAtIndex.toString(), dbResult)) {
                        updateExcelWithSuccess(rowNum, dbResult, "Get All Photos validation passed for index " + index);
                    } else {
                        updateExcelWithFailure(rowNum, "API and Database mismatch for index " + index, dbResult);
                    }
                } else {
                    updateExcelWithFailure(rowNum, "Array index " + index + " not found in response", getAllResponse.asString());
                }
            } else {
                updateExcelWithFailure(rowNum,
                    "Status code mismatch. Expected: " + expectedStatusCode + ", Actual: " + actualStatusCode,
                    getAllResponse.asString());
            }
        }
    }

    /**
     * GET BY ID API Test - Retrieve photo by ID with database validation
     */
    @Test(priority = 4, dependsOnMethods = "testCreatePhoto")
    public void testGetPhotoById() {
        logger.info("=== Testing Get Photo By ID (GET BY ID) ===");

        int testRow = 25; // Starting row for GET BY ID tests

        try {
            if (lastCreatedId != null) {
                testGetPhotoByIdWithValidation(testRow);
            } else {
                logger.warn("No created photo ID available for GetById tests");
            }

        } catch (Exception e) {
            logger.error("Error in testGetPhotoById: " + e.getMessage());
            handleTestFailure(testRow, "GET BY ID Test Failed", e.getMessage());
        }
    }

    /**
     * Test get photo by ID with database validation
     */
    private void testGetPhotoByIdWithValidation(int rowNum) throws Exception {
        logger.info("Testing get photo by ID at row {}", rowNum);

        // Generate GetById request body
        String getByIdRequestBody = generateGetByIdRequestBody(lastCreatedId);

        // Update Excel with request body
        excelUtils.setCellData(filePath, sheetName, rowNum, bodyCol, getByIdRequestBody);

        // Make GET BY ID request
        Response getByIdResponse = makeGetByIdRequest(rowNum, getByIdRequestBody);

        if (getByIdResponse != null) {
            // Validate status code
            int actualStatusCode = getByIdResponse.getStatusCode();
            String expectedStatusCode = excelUtils.getCellData(filePath, sheetName, rowNum, expectedResultCol);

            if (String.valueOf(actualStatusCode).equals(expectedStatusCode.trim())) {
                // Store API response in expected result
                String apiResponse = getByIdResponse.asString();
                excelUtils.setCellData(filePath, sheetName, rowNum, expectedResultCol, apiResponse);

                // Extract ID from response
                String responseId = extractIdFromResponse(getByIdResponse);

                if (responseId != null) {
                    // Query database for this photo with foreign key handling
                    String dbResult = dbUtils.getPhotoFromDatabaseWithForeignKeys(responseId);

                    // Store in actual result
                    excelUtils.setCellData(filePath, sheetName, rowNum, actualResultCol, dbResult);

                    // Compare API response with database
                    if (compareApiWithDatabase(apiResponse, dbResult)) {
                        updateExcelWithSuccess(rowNum, dbResult, "Get Photo By ID validation passed");
                    } else {
                        updateExcelWithFailure(rowNum, "API and Database mismatch", dbResult);
                    }
                } else {
                    updateExcelWithFailure(rowNum, "Failed to extract ID from GetById response", apiResponse);
                }
            } else {
                updateExcelWithFailure(rowNum,
                    "Status code mismatch. Expected: " + expectedStatusCode + ", Actual: " + actualStatusCode,
                    getByIdResponse.asString());
            }
        }
    }

    /**
     * DELETE API Test - Delete photo and verify deletion
     */
    @Test(priority = 5, dependsOnMethods = "testCreatePhoto")
    public void testDeletePhoto() {
        logger.info("=== Testing Photo Deletion (DELETE) ===");

        int testRow = 26; // Starting row for DELETE tests

        try {
            if (lastCreatedId != null) {
                testPhotoDeleteWithValidation(testRow);
            } else {
                logger.warn("No created photo ID available for delete tests");
            }

        } catch (Exception e) {
            logger.error("Error in testDeletePhoto: " + e.getMessage());
            handleTestFailure(testRow, "DELETE Test Failed", e.getMessage());
        }
    }

    /**
     * Test photo deletion with validation
     */
    private void testPhotoDeleteWithValidation(int rowNum) throws Exception {
        logger.info("Testing photo deletion at row {}", rowNum);

        // Generate DELETE request body
        String deleteRequestBody = generateDeleteRequestBody(lastCreatedId);

        // Update Excel with request body
        excelUtils.setCellData(filePath, sheetName, rowNum, bodyCol, deleteRequestBody);

        // Make DELETE request
        Response deleteResponse = makeDeleteRequest(rowNum, deleteRequestBody);

        if (deleteResponse != null) {
            // Validate status code
            int actualStatusCode = deleteResponse.getStatusCode();
            String expectedStatusCode = excelUtils.getCellData(filePath, sheetName, rowNum, expectedResultCol);

            if (String.valueOf(actualStatusCode).equals(expectedStatusCode.trim())) {
                // Verify deletion by trying to get the deleted record
                verifyDeletionWithGetById(rowNum + 1, lastCreatedId);
                updateExcelWithSuccess(rowNum, deleteResponse.asString(), "Photo deleted successfully");
            } else {
                updateExcelWithFailure(rowNum,
                    "Status code mismatch. Expected: " + expectedStatusCode + ", Actual: " + actualStatusCode,
                    deleteResponse.asString());
            }
        }
    }

    /**
     * Verify deletion by attempting to get the deleted record
     */
    private void verifyDeletionWithGetById(int rowNum, String deletedId) throws Exception {
        logger.info("Verifying deletion by GetById at row {}", rowNum);

        // Generate GetById request body for deleted record
        String getByIdRequestBody = generateGetByIdRequestBody(deletedId);

        // Make GET BY ID request for deleted record
        Response getByIdResponse = makeGetByIdRequest(rowNum, getByIdRequestBody);

        if (getByIdResponse != null) {
            int statusCode = getByIdResponse.getStatusCode();
            String expectedStatusCode = excelUtils.getCellData(filePath, sheetName, rowNum, expectedResultCol);

            // Should return 404 or similar error status
            if (String.valueOf(statusCode).equals(expectedStatusCode.trim()) || statusCode >= 400) {
                updateExcelWithSuccess(rowNum, getByIdResponse.asString(), "Deletion verification passed - record not found");
            } else {
                updateExcelWithFailure(rowNum, "Deletion verification failed - record still exists", getByIdResponse.asString());
            }
        }
    }

    // ==================== HELPER METHODS ====================

    /**
     * Generate photo request body using Faker
     */
    private String generatePhotoRequestBody() throws Exception {
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> photoData = new HashMap<>();

            photoData.put("title", faker.lorem().sentence(3));
            photoData.put("description", faker.lorem().paragraph());
            photoData.put("url", faker.internet().url());
            photoData.put("thumbnailUrl", faker.internet().url());
            photoData.put("albumId", faker.number().numberBetween(1, 100));
            photoData.put("userId", faker.number().numberBetween(1, 10));
            photoData.put("tags", faker.lorem().words(3));
            photoData.put("createdBy", faker.name().username());
            photoData.put("auth", authToken);
            photoData.put("type", "post");

            return mapper.writeValueAsString(photoData);
        } catch (Exception e) {
            logger.error("Error generating photo request body: " + e.getMessage());
            throw e;
        }
    }

    /**
     * Generate photo request body with null field for constraint testing
     */
    private String generatePhotoRequestBodyWithNullField() throws Exception {
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> photoData = new HashMap<>();

            // Leave title as null to test null constraint
            photoData.put("title", null);
            photoData.put("description", faker.lorem().paragraph());
            photoData.put("url", faker.internet().url());
            photoData.put("thumbnailUrl", faker.internet().url());
            photoData.put("albumId", faker.number().numberBetween(1, 100));
            photoData.put("userId", faker.number().numberBetween(1, 10));
            photoData.put("auth", authToken);
            photoData.put("type", "post");

            return mapper.writeValueAsString(photoData);
        } catch (Exception e) {
            logger.error("Error generating photo request body with null field: " + e.getMessage());
            throw e;
        }
    }

    /**
     * Generate duplicate photo request body for unique constraint testing
     */
    private String generateDuplicatePhotoRequestBody() throws Exception {
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> photoData = new HashMap<>();

            // Use same title/url to trigger unique constraint
            photoData.put("title", "Duplicate Photo Title");
            photoData.put("description", faker.lorem().paragraph());
            photoData.put("url", "http://duplicate-photo-url.com");
            photoData.put("thumbnailUrl", faker.internet().url());
            photoData.put("albumId", faker.number().numberBetween(1, 100));
            photoData.put("userId", faker.number().numberBetween(1, 10));
            photoData.put("auth", authToken);
            photoData.put("type", "post");

            return mapper.writeValueAsString(photoData);
        } catch (Exception e) {
            logger.error("Error generating duplicate photo request body: " + e.getMessage());
            throw e;
        }
    }

    /**
     * Generate photo update request body using Faker
     */
    private String generatePhotoUpdateRequestBody(String photoId) throws Exception {
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> photoData = new HashMap<>();

            photoData.put("id", photoId);
            photoData.put("title", faker.lorem().sentence(3) + " - Updated");
            photoData.put("description", faker.lorem().paragraph() + " - Updated");
            photoData.put("url", faker.internet().url());
            photoData.put("thumbnailUrl", faker.internet().url());
            photoData.put("albumId", faker.number().numberBetween(1, 100));
            photoData.put("userId", faker.number().numberBetween(1, 10));
            photoData.put("tags", faker.lorem().words(3));
            photoData.put("updatedBy", faker.name().username());
            photoData.put("auth", authToken);
            photoData.put("type", "put");

            return mapper.writeValueAsString(photoData);
        } catch (Exception e) {
            logger.error("Error generating photo update request body: " + e.getMessage());
            throw e;
        }
    }

    /**
     * Generate photo update request body with null field
     */
    private String generatePhotoUpdateRequestBodyWithNullField(String photoId) throws Exception {
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> photoData = new HashMap<>();

            photoData.put("id", photoId);
            photoData.put("title", null); // Null field for constraint testing
            photoData.put("description", faker.lorem().paragraph());
            photoData.put("url", faker.internet().url());
            photoData.put("auth", authToken);
            photoData.put("type", "put");

            return mapper.writeValueAsString(photoData);
        } catch (Exception e) {
            logger.error("Error generating photo update request body with null field: " + e.getMessage());
            throw e;
        }
    }

    /**
     * Generate duplicate photo update request body
     */
    private String generateDuplicatePhotoUpdateRequestBody(String photoId) throws Exception {
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> photoData = new HashMap<>();

            photoData.put("id", photoId);
            photoData.put("title", "Duplicate Photo Title"); // Duplicate title
            photoData.put("description", faker.lorem().paragraph());
            photoData.put("url", "http://duplicate-photo-url.com"); // Duplicate URL
            photoData.put("auth", authToken);
            photoData.put("type", "put");

            return mapper.writeValueAsString(photoData);
        } catch (Exception e) {
            logger.error("Error generating duplicate photo update request body: " + e.getMessage());
            throw e;
        }
    }

    /**
     * Generate GetById request body
     */
    private String generateGetByIdRequestBody(String photoId) throws Exception {
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> requestData = new HashMap<>();

            requestData.put("id", photoId);
            requestData.put("auth", authToken);
            requestData.put("type", "getById");

            return mapper.writeValueAsString(requestData);
        } catch (Exception e) {
            logger.error("Error generating GetById request body: " + e.getMessage());
            throw e;
        }
    }

    /**
     * Generate Delete request body
     */
    private String generateDeleteRequestBody(String photoId) throws Exception {
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> requestData = new HashMap<>();

            requestData.put("id", photoId);
            requestData.put("auth", authToken);
            requestData.put("type", "delete");

            return mapper.writeValueAsString(requestData);
        } catch (Exception e) {
            logger.error("Error generating Delete request body: " + e.getMessage());
            throw e;
        }
    }

    /**
     * Make POST request
     */
    private Response makePostRequest(int rowNum, String requestBody) throws Exception {
        try {
            api.helpers.PostWithDynamicRequestBody requestHandler =
                new api.helpers.PostWithDynamicRequestBody(logger, filePath, sheetName, urlCol, bodyCol);
            return requestHandler.post(rowNum, requestBody);
        } catch (Exception e) {
            logger.error("Error making POST request: " + e.getMessage());
            updateExcelWithFailure(rowNum, "POST request failed", e.getMessage());
            return null;
        }
    }

    /**
     * Make PUT request
     */
    private Response makePutRequest(int rowNum, String requestBody) throws Exception {
        try {
            api.helpers.PostWithDynamicRequestBody requestHandler =
                new api.helpers.PostWithDynamicRequestBody(logger, filePath, sheetName, urlCol, bodyCol);
            return requestHandler.post(rowNum, requestBody);
        } catch (Exception e) {
            logger.error("Error making PUT request: " + e.getMessage());
            updateExcelWithFailure(rowNum, "PUT request failed", e.getMessage());
            return null;
        }
    }

    /**
     * Make GET ALL request
     */
    private Response makeGetAllRequest(int rowNum, String requestBody) throws Exception {
        try {
            api.helpers.PostWithDynamicRequestBody requestHandler =
                new api.helpers.PostWithDynamicRequestBody(logger, filePath, sheetName, urlCol, bodyCol);
            return requestHandler.post(rowNum, requestBody);
        } catch (Exception e) {
            logger.error("Error making GET ALL request: " + e.getMessage());
            updateExcelWithFailure(rowNum, "GET ALL request failed", e.getMessage());
            return null;
        }
    }

    /**
     * Make GET BY ID request
     */
    private Response makeGetByIdRequest(int rowNum, String requestBody) throws Exception {
        try {
            api.helpers.PostWithDynamicRequestBody requestHandler =
                new api.helpers.PostWithDynamicRequestBody(logger, filePath, sheetName, urlCol, bodyCol);
            return requestHandler.post(rowNum, requestBody);
        } catch (Exception e) {
            logger.error("Error making GET BY ID request: " + e.getMessage());
            updateExcelWithFailure(rowNum, "GET BY ID request failed", e.getMessage());
            return null;
        }
    }

    /**
     * Make DELETE request
     */
    private Response makeDeleteRequest(int rowNum, String requestBody) throws Exception {
        try {
            api.helpers.PostWithDynamicRequestBody requestHandler =
                new api.helpers.PostWithDynamicRequestBody(logger, filePath, sheetName, urlCol, bodyCol);
            return requestHandler.post(rowNum, requestBody);
        } catch (Exception e) {
            logger.error("Error making DELETE request: " + e.getMessage());
            updateExcelWithFailure(rowNum, "DELETE request failed", e.getMessage());
            return null;
        }
    }

    /**
     * Extract ID from response
     */
    private String extractIdFromResponse(Response response) {
        try {
            JsonNode jsonNode = objectMapper.readTree(response.asString());
            if (jsonNode.has("id")) {
                return jsonNode.get("id").asText();
            } else if (jsonNode.has("data") && jsonNode.get("data").has("id")) {
                return jsonNode.get("data").get("id").asText();
            }
            logger.warn("ID not found in response: " + response.asString());
            return null;
        } catch (Exception e) {
            logger.error("Error extracting ID from response: " + e.getMessage());
            return null;
        }
    }

    /**
     * Validate creation with GetById API
     */
    private void validateCreationWithGetById(int rowNum, String createdId) throws Exception {
        logger.info("Validating creation with GetById for ID: {}", createdId);

        String getByIdRequestBody = generateGetByIdRequestBody(createdId);
        Response getByIdResponse = makeGetByIdRequest(rowNum, getByIdRequestBody);

        if (getByIdResponse != null && getByIdResponse.getStatusCode() == 200) {
            logger.info("Creation validation successful - record found with GetById");
        } else {
            logger.warn("Creation validation failed - record not found with GetById");
        }
    }

    /**
     * Validate update with GetById API
     */
    private void validateUpdateWithGetById(int rowNum, String updatedId) throws Exception {
        logger.info("Validating update with GetById for ID: {}", updatedId);

        String getByIdRequestBody = generateGetByIdRequestBody(updatedId);
        Response getByIdResponse = makeGetByIdRequest(rowNum, getByIdRequestBody);

        if (getByIdResponse != null && getByIdResponse.getStatusCode() == 200) {
            logger.info("Update validation successful - updated record found with GetById");
        } else {
            logger.warn("Update validation failed - updated record not found with GetById");
        }
    }

    /**
     * Compare API response with database result
     */
    private boolean compareApiWithDatabase(String apiResponse, String dbResult) {
        try {
            JsonNode apiNode = objectMapper.readTree(apiResponse);
            JsonNode dbNode = objectMapper.readTree(dbResult);

            // Compare key fields (ignoring timestamps and system fields)
            String[] fieldsToCompare = {"id", "title", "description", "url", "thumbnailUrl", "albumId", "userId"};

            for (String field : fieldsToCompare) {
                if (apiNode.has(field) && dbNode.has(field)) {
                    String apiValue = apiNode.get(field).asText();
                    String dbValue = dbNode.get(field).asText();

                    if (!apiValue.equals(dbValue)) {
                        logger.warn("Field mismatch - {}: API={}, DB={}", field, apiValue, dbValue);
                        return false;
                    }
                }
            }

            return true;
        } catch (Exception e) {
            logger.error("Error comparing API with database: " + e.getMessage());
            return false;
        }
    }

    /**
     * Update Excel with success result
     */
    private void updateExcelWithSuccess(int rowNum, String actualResult, String message) {
        try {
            excelUtils.setCellData(filePath, sheetName, rowNum, actualResultCol, actualResult);
            excelUtils.setCellData(filePath, sheetName, rowNum, statusCol, "PASS", true);
            logger.info("Test PASSED at row {}: {}", rowNum, message);
        } catch (Exception e) {
            logger.error("Error updating Excel with success: " + e.getMessage());
        }
    }

    /**
     * Update Excel with failure result
     */
    private void updateExcelWithFailure(int rowNum, String errorMessage, String actualResult) {
        try {
            excelUtils.setCellData(filePath, sheetName, rowNum, actualResultCol, actualResult);
            excelUtils.setCellData(filePath, sheetName, rowNum, statusCol, "FAIL", false);

            // Generate defect ID for failed test case
            String defectId = defectTracker.generateDefectId("PHOTO", "API_TEST", errorMessage);
            excelUtils.setCellData(filePath, sheetName, rowNum, defectIdCol, defectId);

            logger.error("Test FAILED at row {}: {}", rowNum, errorMessage);
        } catch (Exception e) {
            logger.error("Error updating Excel with failure: " + e.getMessage());
        }
    }

    /**
     * Handle test failure
     */
    private void handleTestFailure(int rowNum, String testType, String errorMessage) {
        try {
            String defectId = defectTracker.generateDefectId("PHOTO", testType, errorMessage);
            excelUtils.setCellData(filePath, sheetName, rowNum, statusCol, "FAIL", false);
            excelUtils.setCellData(filePath, sheetName, rowNum, defectIdCol, defectId);
            excelUtils.setCellData(filePath, sheetName, rowNum, actualResultCol, "Test execution failed: " + errorMessage);

            logger.error("Test execution failed at row {}: {}", rowNum, errorMessage);
        } catch (Exception e) {
            logger.error("Error handling test failure: " + e.getMessage());
        }
    }
}