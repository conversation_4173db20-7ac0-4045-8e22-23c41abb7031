package runner;

import org.testng.TestNG;
import org.testng.xml.XmlClass;
import org.testng.xml.XmlSuite;
import org.testng.xml.XmlTest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import testCases.PhotosApiTestNew;
import testCases.ProductsApiTestNew;

import java.util.Arrays;
import java.util.List;

/**
 * Simple CRUD Test Runner
 * Executes Photos and Products API tests with comprehensive validations
 */
public class SimpleCrudTestRunner {
    private static final Logger logger = LoggerFactory.getLogger(SimpleCrudTestRunner.class);

    public static void main(String[] args) {
        logger.info("=== Starting Simple CRUD Test Runner ===");
        
        try {
            // Determine which tests to run based on arguments
            String testType = (args.length > 0) ? args[0].toLowerCase() : "all";
            
            switch (testType) {
                case "photos":
                    runPhotosTests();
                    break;
                case "products":
                    runProductsTests();
                    break;
                case "all":
                default:
                    runAllTests();
                    break;
            }
            
            logger.info("=== Simple CRUD Test Runner Completed ===");
            
        } catch (Exception e) {
            logger.error("Error running tests: " + e.getMessage());
            System.exit(1);
        }
    }
    
    /**
     * Run only Photos API tests
     */
    private static void runPhotosTests() {
        logger.info("Running Photos API Tests only");
        
        TestNG testng = new TestNG();
        testng.setTestClasses(new Class[]{PhotosApiTestNew.class});
        testng.run();
    }
    
    /**
     * Run only Products API tests
     */
    private static void runProductsTests() {
        logger.info("Running Products API Tests only");
        
        TestNG testng = new TestNG();
        testng.setTestClasses(new Class[]{ProductsApiTestNew.class});
        testng.run();
    }
    
    /**
     * Run all CRUD tests
     */
    private static void runAllTests() {
        logger.info("Running All CRUD Tests");
        
        // Create TestNG suite programmatically
        XmlSuite suite = new XmlSuite();
        suite.setName("CRUD Operations Test Suite");
        suite.setParallel(XmlSuite.ParallelMode.NONE);
        
        // Create test for Photos API
        XmlTest photosTest = new XmlTest(suite);
        photosTest.setName("Photos API CRUD Tests");
        XmlClass photosClass = new XmlClass(PhotosApiTestNew.class);
        photosTest.setXmlClasses(Arrays.asList(photosClass));
        
        // Create test for Products API
        XmlTest productsTest = new XmlTest(suite);
        productsTest.setName("Products API CRUD Tests");
        XmlClass productsClass = new XmlClass(ProductsApiTestNew.class);
        productsTest.setXmlClasses(Arrays.asList(productsClass));
        
        // Run the suite
        TestNG testng = new TestNG();
        testng.setXmlSuites(Arrays.asList(suite));
        testng.run();
        
        // Print summary
        printTestSummary();
    }
    
    /**
     * Print test execution summary
     */
    private static void printTestSummary() {
        logger.info("=== Test Execution Summary ===");
        logger.info("✅ Photos API CRUD Operations: Completed");
        logger.info("✅ Products API CRUD Operations: Completed");
        logger.info("📊 Check Excel files for detailed results");
        logger.info("📋 Check logs for execution details");
        logger.info("🎯 Validations performed:");
        logger.info("   - Status code validation for successful creation");
        logger.info("   - Constraint violation testing (null/unique)");
        logger.info("   - Error message validation");
        logger.info("   - Request body and response matching");
        logger.info("   - Database validation for photos and products tables");
    }
}
