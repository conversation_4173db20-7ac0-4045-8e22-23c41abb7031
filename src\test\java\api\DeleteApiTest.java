package api;

import io.restassured.response.Response;
import org.slf4j.Logger;
import org.testng.Assert;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import api.helpers.PostWithDynamicRequestBody;

/**
 * Class for testing DELETE API operations
 */
public class DeleteApiTest extends ApiTestBase {

    /**
     * Constructor
     * @param logger Logger instance
     * @param filePath Excel file path
     * @param sheetName Excel sheet name
     * @param url URL column index
     * @param body Request body column index
     * @param Status Status column index
     * @param ActualResult Actual result column index
     * @param ExpectedResult Expected result column index
     * @param tableName Table name column index
     */
    public DeleteApiTest(Logger logger, String filePath, String sheetName, int url, int body, int Status, int ActualResult, int ExpectedResult, int tableName) {
        super(logger, filePath, sheetName, url, body, Status, ActualResult, ExpectedResult, tableName);
    }

    /**
     * Delete a record
     * @param rowNum Excel row number
     * @param accessToken Authentication token
     * @param id ID of the record to delete
     * @return Response from the DELETE request
     * @throws Exception If any error occurs during the process
     */
    public Response deleteRecord(int rowNum, String accessToken, String id) throws Exception {
        // Step 1: Get the original request body from Excel
        String originalRequestBody = getRequestBodyFromExcel(rowNum);

        // Step 2: Create a proper request body for the DELETE request
        String deleteRequestBody = createDeleteRequestBody(originalRequestBody, accessToken, id);

        // Step 3: Make the DELETE request
        Response deleteResponse = makeDeleteRequest(rowNum, deleteRequestBody);

        if (deleteResponse == null) {
            logger.error("DELETE response is null");
            updateExcelSheet(rowNum, "Failed", "DELETE response is null");
            return null;
        }

        // Step 4: Store the DELETE response in the Actual Result column
        String deleteResponseBody = deleteResponse.getBody().asPrettyString();
        excelUtils.setCellData(filePath, sheetName, rowNum, ActualResult, deleteResponseBody);
        logger.info("Stored DELETE response in Actual Result column");

        return deleteResponse;
    }

    /**
     * Delete a record and verify the status code
     * @param rowNum Excel row number
     * @param accessToken Authentication token
     * @param id ID of the record to delete
     * @param expectedStatusCode Expected status code
     * @return Response from the DELETE request
     * @throws Exception If any error occurs during the process
     */
    public Response deleteRecordAndVerifyStatusCode(int rowNum, String accessToken, String id, int expectedStatusCode) throws Exception {
        // Step 1: Delete the record
        Response deleteResponse = deleteRecord(rowNum, accessToken, id);

        if (deleteResponse == null) {
            return null;
        }

        // Step 2: Verify the status code
        int actualStatusCode = deleteResponse.getStatusCode();
        boolean isMatch = (actualStatusCode == expectedStatusCode);

        // Step 3: Update Excel with the result
        if (isMatch) {
            updateExcelSheet(rowNum, "Passed", "DELETE response status code " + actualStatusCode + " matches expected " + expectedStatusCode);
        } else {
            updateExcelSheet(rowNum, "Failed", "DELETE response status code " + actualStatusCode + " does not match expected " + expectedStatusCode);
        }

        return deleteResponse;
    }

    /**
     * Create a request body for the DELETE API
     * @param originalRequestBody The original request body
     * @param accessToken Authentication token
     * @param id ID of the record to delete
     * @return The request body for the DELETE API
     * @throws Exception If any error occurs during the process
     */
    private String createDeleteRequestBody(String originalRequestBody, String accessToken, String id) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode requestNode;

        try {
            // Try to parse the original request body as JSON
            JsonNode root = mapper.readTree(originalRequestBody);

            // Check if it already has the required structure
            if (root.has("endpoint") && root.has("type")) {
                // It already has the required structure, use it as is
                requestNode = root.deepCopy();

                // Update the endpoint to use the ID
                String originalEndpoint = requestNode.get("endpoint").asText();
                String basePath = originalEndpoint;

                // Extract the base path without /save or /update
                if (basePath.contains("/save")) {
                    basePath = basePath.substring(0, basePath.indexOf("/save"));
                } else if (basePath.contains("/update")) {
                    basePath = basePath.substring(0, basePath.indexOf("/update"));
                } else if (basePath.contains("/list")) {
                    basePath = basePath.substring(0, basePath.indexOf("/list"));
                }

                // Create the DELETE endpoint
                String deleteEndpoint = basePath + "/" + id;

                // Update the request node
                requestNode.put("endpoint", deleteEndpoint);
                requestNode.put("type", "delete");
                requestNode.putNull("payload");
            } else {
                // It doesn't have the required structure, create a new one
                requestNode = mapper.createObjectNode();
                requestNode.put("endpoint", "/core/api/CountryMaster/" + id);
                requestNode.put("type", "delete");
                requestNode.putNull("payload");
            }
        } catch (Exception e) {
            // If parsing fails, create a simple DELETE request
            requestNode = mapper.createObjectNode();
            requestNode.put("endpoint", "/core/api/CountryMaster/" + id);
            requestNode.put("type", "delete");
            requestNode.putNull("payload");
        }

        // Ensure the auth token is set
        requestNode.put("auth", accessToken);

        // Convert to JSON string
        String deleteRequestBody = mapper.writeValueAsString(requestNode);
        logger.info("DELETE request body: {}", deleteRequestBody);

        return deleteRequestBody;
    }

    /**
     * Make a DELETE request
     * @param rowNum Excel row number
     * @param requestBody Request body
     * @return Response from the DELETE request
     * @throws Exception If any error occurs during the process
     */
    private Response makeDeleteRequest(int rowNum, String requestBody) throws Exception {
        PostWithDynamicRequestBody requestHandler = new PostWithDynamicRequestBody(logger, filePath, sheetName, url, body);
        Response deleteResponse = requestHandler.post(rowNum, requestBody);

        if (deleteResponse == null) {
            logger.error("DELETE response is null");
            updateExcelSheet(rowNum, "Failed", "DELETE response is null");
            return null;
        }

        int deleteStatusCode = deleteResponse.getStatusCode();
        String deleteResponseBody = deleteResponse.getBody().asPrettyString();

        logger.info("DELETE response status code: {}", deleteStatusCode);
        logger.info("DELETE response body: {}", deleteResponseBody);

        return deleteResponse;
    }
}
