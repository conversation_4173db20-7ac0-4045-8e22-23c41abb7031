@echo off
echo ========================================
echo Running Simple CRUD Framework Test
echo ========================================
echo.

REM Set Java options
set JAVA_OPTS=-Xmx2g -Dfile.encoding=UTF-8

echo Compiling only the required classes...

REM Compile only the utils and simple test classes
javac -cp "target/test-classes;%MAVEN_REPO%\*" ^
    -d target/test-classes ^
    src/test/java/utils/TestConfiguration.java ^
    src/test/java/utils/ExcelUtils.java ^
    src/test/java/utils/DatabaseValidationUtils.java ^
    src/test/java/utils/DatabaseUtils.java ^
    src/test/java/utils/Constants.java ^
    src/test/java/utils/ConfigManager.java ^
    src/test/java/utils/Excel.java ^
    src/test/java/simple/SimpleCrudTest.java

if %ERRORLEVEL% neq 0 (
    echo ERROR: Compilation failed!
    pause
    exit /b 1
)

echo.
echo Running Simple CRUD Test...
mvn test -Dtest=simple.SimpleCrudTest %JAVA_OPTS%

if %ERRORLEVEL% neq 0 (
    echo ERROR: Simple CRUD Test failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Simple CRUD Test Completed Successfully!
echo ========================================
echo.
echo The CRUD Operations Framework is ready!
echo You can now run the full test suite.
echo.

pause
