package api;

import io.restassured.response.Response;
import org.slf4j.Logger;
import org.testng.Assert;
import utils.DatabaseUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import api.helpers.PostWithDynamicRequestBody;
import java.util.Iterator;

/**
 * Class for testing GET all API operations
 */
public class GetAllApiTest extends ApiTestBase {
    private DatabaseUtils dbUtils;

    /**
     * Constructor
     * @param logger Logger instance
     * @param filePath Excel file path
     * @param sheetName Excel sheet name
     * @param url URL column index
     * @param body Request body column index
     * @param Status Status column index
     * @param ActualResult Actual result column index
     * @param ExpectedResult Expected result column index
     * @param tableName Table name column index
     */
    public GetAllApiTest(Logger logger, String filePath, String sheetName, int url, int body, int Status, int ActualResult, int ExpectedResult, int tableName) {
        super(logger, filePath, sheetName, url, body, Status, ActualResult, ExpectedResult, tableName);
        this.dbUtils = new DatabaseUtils(logger);
    }

    /**
     * Test the getAll API by comparing a specific record from the API response with the database record
     * @param rowNum Excel row number
     * @param accessToken Authentication token
     * @param recordIndex Index of the record to validate (0 for first, 1 for second, etc.)
     * @return The response from the getAll API
     * @throws Exception If any error occurs during the process
     */
    public Response testGetAllApi(int rowNum, String accessToken, int recordIndex) throws Exception {
        // Step 1: Get the request body from Excel
        String originalRequestBody = getRequestBodyFromExcel(rowNum);

        // Step 2: Create a request body for the getAll API
        String requestBody = createGetAllRequestBody(originalRequestBody, accessToken);

        // Step 3: Make the getAll API request
        Response getAllResponse = makeGetAllApiRequest(rowNum, requestBody);

        // Step 4: Process the response and compare with database
        processGetAllApiResponse(rowNum, getAllResponse, requestBody, recordIndex);

        return getAllResponse;
    }

    /**
     * Process the getAll API response and compare with database
     * @param rowNum Excel row number
     * @param getAllResponse Response from the getAll API
     * @param requestBody Request body used for the API call
     * @param recordIndex Index of the record to validate (0 for first, 1 for second, etc.)
     * @return True if the processing was successful, false otherwise
     * @throws Exception If any error occurs during the process
     */
    private boolean processGetAllApiResponse(int rowNum, Response getAllResponse, String requestBody, int recordIndex) throws Exception {
        if (getAllResponse == null) {
            return false;
        }

        // Log the status code but don't validate it
        int statusCode = getAllResponse.getStatusCode();
        logger.info("API returned status code: {}", statusCode);

        // Extract the response body
        String getAllResponseBody = getAllResponse.getBody().asPrettyString();

        // Check if the response is empty or not an array
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(getAllResponseBody);

            if (rootNode.isArray() && rootNode.size() == 0) {
                logger.error("API returned an empty array");
                updateExcelSheet(rowNum, "Failed", "API returned an empty array");
                return false;
            }

            if (rootNode.has("content") && rootNode.get("content").isArray() && rootNode.get("content").size() == 0) {
                logger.error("API returned an empty content array");
                updateExcelSheet(rowNum, "Failed", "API returned an empty content array");
                return false;
            }
        } catch (Exception e) {
            logger.error("Error checking if response is empty: " + e.getMessage());
        }

        // Log the record index we're using
        logger.info("Using record index {} for validation", recordIndex);

        // We can still allow overriding the index from Excel if needed
        String indexStr = excelUtils.getCellData(filePath, sheetName, rowNum, this.tableName + 1);
        if (indexStr != null && !indexStr.isEmpty()) {
            try {
                int excelIndex = Integer.parseInt(indexStr);
                if (excelIndex != recordIndex) {
                    logger.info("Overriding record index from Excel sheet: {} instead of {}", excelIndex, recordIndex);
                    recordIndex = excelIndex;
                }
            } catch (NumberFormatException e) {
                logger.warn("Invalid record index in Excel sheet: {}, using provided index {}", indexStr, recordIndex);
            }
        }

        // Extract the record at the specified index from the response
        String recordAtIndex = extractRecordFromResponse(getAllResponseBody, recordIndex);
        logger.info("Extracted record at index {}: {}", recordIndex, recordAtIndex);

        if (recordAtIndex.equals("{}")) {
            logger.error("Could not extract record at index {} from response", recordIndex);
            updateExcelSheet(rowNum, "Failed", "Could not extract record at index " + recordIndex + " from response");
            return false;
        }

        // Extract the ID from the record
        String id = extractIdFromResponseBody(recordAtIndex);
        logger.info("Extracted ID '{}' from field 'id'", id);

        // Get the table name from Excel
        String tableName = excelUtils.getCellData(filePath, sheetName, rowNum, this.tableName);
        logger.info("Table name from Excel: {}", tableName);

        // Use the table name from Excel
        logger.info("Using table name: {}", tableName);
        logger.info("Using table name from Excel: {}", tableName);

        // Check if the table exists in the core schema
        try {
            // Check if the table exists in the core schema
            if (!dbUtils.tableExists(tableName)) {
                logger.warn("Table '{}' from Excel does not exist in the core schema", tableName);

                // Check if the ID exists in the table even if the table doesn't exist in the schema
                String fullTableName = dbUtils.findTableWithId(id, tableName);
                if (!fullTableName.isEmpty()) {
                    logger.info("Found ID {} in table {}", id, fullTableName);
                    tableName = fullTableName;
                } else {
                    // If we can't find the table or ID, mark as failed
                    logger.error("Could not find table '{}' in the core schema or ID {} in the table. Database verification failed.", tableName, id);

                    // Store the API response in the Expected Result column
                    excelUtils.setCellData(filePath, sheetName, rowNum, ExpectedResult, recordAtIndex);
                    logger.info("Stored record from API response in Expected Result column");

                    // Update the Excel sheet with a "Failed" status for database verification failure
                    updateExcelSheet(rowNum, "Failed", "Database connection failed: Could not find table '" + tableName + "' or ID " + id);

                    return false;
                }
            }
        } catch (Exception e) {
            logger.error("Error checking if table exists: {}", e.getMessage());
            logger.error("Database connection failed - test will be marked as failed");

            // Store the API response in the Expected Result column
            excelUtils.setCellData(filePath, sheetName, rowNum, ExpectedResult, recordAtIndex);
            logger.info("Stored record from API response in Expected Result column");

            // Update the Excel sheet with a "Failed" status for database connection failure
            updateExcelSheet(rowNum, "Failed", "Database connection failed: " + e.getMessage());

            return false;
        }

        // Query the database with the ID
        String dbRecord;
        try {
            dbRecord = dbUtils.getRecordByIdAsJson(tableName, "id", id);

            if (dbRecord.equals("{}")) {
                logger.error("No record found in database for ID: {}", id);

                // Store the API response in the Expected Result column
                excelUtils.setCellData(filePath, sheetName, rowNum, ExpectedResult, recordAtIndex);
                logger.info("Stored record from API response in Expected Result column");

                // Update the Excel sheet with a "Failed" status for missing database record
                updateExcelSheet(rowNum, "Failed", "Database connection failed: No matching record found in database for ID " + id);

                return false;
            }
        } catch (Exception e) {
            logger.error("Error querying database: {}", e.getMessage());
            logger.error("Database query failed - test will be marked as failed");

            // Store the API response in the Expected Result column
            excelUtils.setCellData(filePath, sheetName, rowNum, ExpectedResult, recordAtIndex);
            logger.info("Stored record from API response in Expected Result column");

            // Update the Excel sheet with a "Failed" status for database query failure
            updateExcelSheet(rowNum, "Failed", "Database query failed: " + e.getMessage());

            return false;
        }

        // Store the record from the API response in the Expected Result column
        try {
            excelUtils.setCellData(filePath, sheetName, rowNum, ExpectedResult, recordAtIndex);
            logger.info("Stored record from API response in Expected Result column");

            // Store the database record in the Actual Result column
            excelUtils.setCellData(filePath, sheetName, rowNum, ActualResult, dbRecord);
            logger.info("Stored database record in Actual Result column");
        } catch (Exception e) {
            logger.warn("Could not write to Excel file: {}. Continuing with comparison.", e.getMessage());
            // Continue with the comparison even if we can't write to the Excel file
        }

        // Compare the API response with the database record
        boolean isMatch = compareResponses(recordAtIndex, dbRecord);
        if (isMatch) {
            logger.info("API response matches database record");
            updateExcelSheet(rowNum, "Passed", "API response matches database record");
        } else {
            logger.error("API response does not match database record");
            updateExcelSheet(rowNum, "Failed", "API response does not match database record");
        }

        return isMatch;
    }

    /**
     * Test the getAll API by comparing the first record from the API response with the database record
     * @param rowNum Excel row number
     * @param accessToken Authentication token
     * @return The response from the getAll API
     * @throws Exception If any error occurs during the process
     */
    public Response testGetAllApi(int rowNum, String accessToken) throws Exception {
        // Default to index 0 (first record)
        return testGetAllApi(rowNum, accessToken, 0);
    }

    /**
     * Test the getAll API with a specific record index and handle all validation and error cases
     * @param rowNum Excel row number
     * @param accessToken Authentication token
     * @param recordIndex Index of the record to validate (0 for first, 1 for second, etc.)
     * @throws Exception If any error occurs during the process
     */
    public void testGetAllWithIndex(int rowNum, String accessToken, int recordIndex) throws Exception {
        logger.info("Running getAll API test with record index: {}", recordIndex);

        // The test might fail due to incorrect endpoint, so we'll handle that gracefully
        try {
            Response response = testGetAllApi(rowNum, accessToken, recordIndex);

            // Verify that the response is not null
            Assert.assertNotNull(response, "Response should not be null");

            // Log the response status code
            logger.info("Response status code: " + response.getStatusCode());

            // If the status code is not 200, log a warning but don't fail the test
            if (response.getStatusCode() != 200) {
                logger.warn("API returned non-200 status code: " + response.getStatusCode());
                logger.warn("Response body: " + response.getBody().asPrettyString());

                // For 404 errors, check if the endpoint is incorrect
                if (response.getStatusCode() == 404) {
                    String responseBody = response.getBody().asPrettyString();
                    try {
                        ObjectMapper mapper = new ObjectMapper();
                        JsonNode errorNode = mapper.readTree(responseBody);
                        if (errorNode.has("path")) {
                            String path = errorNode.get("path").asText();
                            logger.error("Endpoint not found: " + path);

                            // Check if the path has a duplicate "/list"
                            if (path.contains("/list/list")) {
                                logger.error("Endpoint has duplicate '/list' suffix. This is likely the cause of the 404 error.");
                                logger.info("Please check the Excel sheet and ensure the endpoint is correct.");
                            }
                        }
                    } catch (Exception e) {
                        logger.error("Error parsing error response: " + e.getMessage());
                    }
                }

                // Check if the Excel sheet has been updated with the error
                String testStatus = getTestStatus(rowNum);
                logger.info("Test status in Excel sheet: " + testStatus);

                if (testStatus != null && !testStatus.isEmpty()) {
                    logger.info("Excel sheet has been updated with test results, test is complete");
                } else {
                    Assert.fail("Excel sheet has not been updated with test results");
                }

                return;
            }

            // Check if the test status is "Failed" due to database issues
            String testStatus = getTestStatus(rowNum);
            String testStatusMessage = getTestStatusMessage(rowNum);

            if ("Failed".equalsIgnoreCase(testStatus) &&
                    (testStatusMessage.contains("No record found in database") ||
                            testStatusMessage.contains("does not exist"))) {

                logger.warn("Test failed due to database issues: " + testStatusMessage);
                logger.info("API returned valid data but database verification failed");
                logger.info("This could be because:");
                logger.info("1. The database connection is incorrect");
                logger.info("2. The database doesn't contain the ID from the API response");
                logger.info("3. The table name is different from what we're trying");

                // Get the API response and log it
                String responseBody = response.getBody().asPrettyString();
                logger.info("API Response: " + responseBody);

                // Since we can't verify against the database, we'll mark this as failed
                logger.error("Marking test as failed since database verification failed");
                // Use a public method to update the Excel sheet
                setTestStatus(rowNum, "Failed");
                setTestStatusMessage(rowNum, "Database connection failed");

                return;
            }

            // Check if the Excel sheet has been updated with the test results
            // We already have testStatus from above, no need to get it again
            logger.info("Test status in Excel sheet: " + testStatus);

            // If the test status is not empty, it means the Excel sheet has been updated
            if (testStatus != null && !testStatus.isEmpty()) {
                // Check if the test passed
                if ("Passed".equalsIgnoreCase(testStatus)) {
                    logger.info("GetAll API test passed: API response matches database record");
                } else {
                    logger.warn("GetAll API test failed: " + getTestStatusMessage(rowNum));
                    Assert.fail("GetAll API test failed: " + getTestStatusMessage(rowNum));
                }
            } else {
                // If the test status is empty, it means the Excel sheet hasn't been updated
                Assert.fail("Excel sheet has not been updated with test results");
            }
        } catch (Exception e) {
            logger.error("Error in getAll API test: " + e.getMessage(), e);
            Assert.fail("Error in getAll API test: " + e.getMessage());
        }
    }

    /**
     * Create a request body for the getAll API
     * @param originalRequestBody The original request body
     * @param accessToken Authentication token
     * @return The request body for the getAll API
     * @throws Exception If any error occurs during the process
     */
    private String createGetAllRequestBody(String originalRequestBody, String accessToken) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        JsonNode requestNode;
        String requestBody;

        try {
            // Try to parse the original request body as JSON
            requestNode = mapper.readTree(originalRequestBody);

            // Check if it has the required structure
            if (requestNode.has("endpoint") && requestNode.has("type")) {
                // Create a copy of the request node
                ObjectNode modifiedNode = mapper.createObjectNode();
                requestNode.fields().forEachRemaining(entry -> modifiedNode.set(entry.getKey(), entry.getValue()));

                // Extract the base path from the endpoint
                String endpoint = requestNode.get("endpoint").asText();
                String basePath = endpoint;

                // Remove any trailing "/list" from the endpoint
                if (basePath.endsWith("/list")) {
                    basePath = basePath.substring(0, basePath.length() - 5);
                    logger.info("Removed suffix '/list' from endpoint: {}", endpoint);
                }

                // Clean up the base path
                String cleanBasePath = basePath.trim();

                // Now add the /list suffix
                String getAllEndpoint = cleanBasePath + "/list";
                logger.info("Created getAll endpoint: {}", getAllEndpoint);

                // Update the request node
                modifiedNode.put("endpoint", getAllEndpoint);
                modifiedNode.put("type", "get");
                modifiedNode.putNull("payload");

                // Ensure the auth token is set
                modifiedNode.put("auth", accessToken);

                requestBody = mapper.writeValueAsString(modifiedNode);
            } else {
                // It doesn't have the required structure, create a new one
                ObjectNode modifiedNode = mapper.createObjectNode();
                modifiedNode.put("endpoint", "/core/api/CountryMaster/list");
                modifiedNode.put("type", "get");
                modifiedNode.putNull("payload");
                modifiedNode.put("auth", accessToken);

                requestBody = mapper.writeValueAsString(modifiedNode);
            }
        } catch (Exception e) {
            // If parsing fails, create a simple getAll request
            ObjectNode modifiedNode = mapper.createObjectNode();
            modifiedNode.put("endpoint", "/core/api/CountryMaster/list");
            modifiedNode.put("type", "get");
            modifiedNode.putNull("payload");
            modifiedNode.put("auth", accessToken);

            requestBody = mapper.writeValueAsString(modifiedNode);
        }

        logger.info("GetAll request body: " + requestBody);
        return requestBody;
    }

    /**
     * Make a getAll API request
     * @param rowNum Excel row number
     * @param requestBody Request body
     * @return Response from the getAll API
     * @throws Exception If any error occurs during the process
     */
    private Response makeGetAllApiRequest(int rowNum, String requestBody) throws Exception {
        PostWithDynamicRequestBody requestHandler = new PostWithDynamicRequestBody(logger, filePath, sheetName, url, body);
        Response getAllResponse = requestHandler.post(rowNum, requestBody);

        if (getAllResponse == null) {
            logger.error("GetAll response is null");
            updateExcelSheet(rowNum, "Failed", "GetAll response is null");
            return null;
        }

        return getAllResponse;
    }

    /**
     * Extract a record from the response at the specified index
     * @param responseBody Response body
     * @param recordIndex Index of the record to extract
     * @return The extracted record as a JSON string
     */
    private String extractRecordFromResponse(String responseBody, int recordIndex) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(responseBody);

            // Check if the response is an array
            if (rootNode.isArray()) {
                // If it's an array, get the record at the specified index
                if (recordIndex < rootNode.size()) {
                    JsonNode recordNode = rootNode.get(recordIndex);
                    return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(recordNode);
                } else {
                    logger.error("Record index {} is out of bounds. Array size is {}", recordIndex, rootNode.size());
                    return "{}";
                }
            }
            // Check if the response has a content array (Spring Data format)
            else if (rootNode.has("content") && rootNode.get("content").isArray()) {
                JsonNode contentNode = rootNode.get("content");
                if (recordIndex < contentNode.size()) {
                    JsonNode recordNode = contentNode.get(recordIndex);
                    return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(recordNode);
                } else {
                    logger.error("Record index {} is out of bounds. Content array size is {}", recordIndex, contentNode.size());
                    return "{}";
                }
            }
            // Check if the response is a single object
            else if (rootNode.isObject() && recordIndex == 0) {
                // If it's a single object and we're looking for the first record, return the whole object
                return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(rootNode);
            }
            // Check if the response has a data array
            else if (rootNode.has("data") && rootNode.get("data").isArray()) {
                JsonNode dataNode = rootNode.get("data");
                if (recordIndex < dataNode.size()) {
                    JsonNode recordNode = dataNode.get(recordIndex);
                    return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(recordNode);
                } else {
                    logger.error("Record index {} is out of bounds. Data array size is {}", recordIndex, dataNode.size());
                    return "{}";
                }
            }
            // Check if the response has a results array
            else if (rootNode.has("results") && rootNode.get("results").isArray()) {
                JsonNode resultsNode = rootNode.get("results");
                if (recordIndex < resultsNode.size()) {
                    JsonNode recordNode = resultsNode.get(recordIndex);
                    return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(recordNode);
                } else {
                    logger.error("Record index {} is out of bounds. Results array size is {}", recordIndex, resultsNode.size());
                    return "{}";
                }
            }
            // Check if the response has a items array
            else if (rootNode.has("items") && rootNode.get("items").isArray()) {
                JsonNode itemsNode = rootNode.get("items");
                if (recordIndex < itemsNode.size()) {
                    JsonNode recordNode = itemsNode.get(recordIndex);
                    return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(recordNode);
                } else {
                    logger.error("Record index {} is out of bounds. Items array size is {}", recordIndex, itemsNode.size());
                    return "{}";
                }
            }
            // If we can't find a suitable array, log an error and return an empty object
            else {
                logger.error("Response is not an array or doesn't have a content/data/results/items array");
                return "{}";
            }
        } catch (Exception e) {
            logger.error("Error extracting record from response: {}", e.getMessage());
            return "{}";
        }
    }
}
