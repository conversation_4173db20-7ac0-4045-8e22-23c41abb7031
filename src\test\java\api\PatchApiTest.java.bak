package api;

import io.restassured.response.Response;
import org.slf4j.Logger;
import org.testng.Assert;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import utils.DatabaseUtils;
import utils.ExcelUtils;
import utils.Constants;
import utils.ConfigManager;
import java.util.Iterator;

/**
 * Class for testing PATCH API operations
 */
public class PatchApiTest extends ApiTestBase {
    private DatabaseUtils dbUtils;

    /**
     * Constructor
     * @param logger Logger instance
     * @param filePath Excel file path
     * @param sheetName Excel sheet name
     * @param url URL column index
     * @param body Request body column index
     * @param Status Status column index
     * @param ActualResult Actual result column index
     * @param ExpectedResult Expected result column index
     * @param tableName Table name column index
     */
    public PatchApiTest(Logger logger, String filePath, String sheetName, int url, int body, int Status, int ActualResult, int ExpectedResult, int tableName) {
        super(logger, filePath, sheetName, url, body, Status, ActualResult, ExpectedResult, tableName);
        this.dbUtils = new DatabaseUtils(logger);
    }

    /**
     * Patch a record
     * @param rowNum Excel row number
     * @param accessToken Authentication token
     * @param id ID of the record to patch
     * @return Response from the PATCH request
     * @throws Exception If any error occurs during the process
     */
    public Response patchRecord(int rowNum, String accessToken, String id) throws Exception {
        // Step 1: Get the original request body from Excel
        String originalRequestBody = getRequestBodyFromExcel(rowNum);

        // Step 2: Create a proper request body for the PATCH request
        String patchRequestBody = createPatchRequestBody(originalRequestBody, accessToken, id);

        // Step 3: Make the PATCH request
        Response patchResponse = makePatchRequest(rowNum, patchRequestBody);

        if (patchResponse == null) {
            logger.error("PATCH response is null");
            updateExcelSheet(rowNum, "Failed", "PATCH response is null");
            return null;
        }

        // Step 4: Store the PATCH response in the Actual Result column
        String patchResponseBody = patchResponse.getBody().asPrettyString();
        excelUtils.setCellData(filePath, sheetName, rowNum, ActualResult, patchResponseBody);
        logger.info("Stored PATCH response in Actual Result column");

        return patchResponse;
    }

    /**
     * Patch a record and compare with expected result
     * @param rowNum Excel row number
     * @param accessToken Authentication token
     * @param id ID of the record to patch
     * @param expectedResult Expected result to compare with
     * @return Response from the PATCH request
     * @throws Exception If any error occurs during the process
     */
    public Response patchRecordAndCompare(int rowNum, String accessToken, String id, String expectedResult) throws Exception {
        // Step 1: Patch the record
        Response patchResponse = patchRecord(rowNum, accessToken, id);

        if (patchResponse == null) {
            return null;
        }

        // Step 2: Compare the response with the expected result
        String patchResponseBody = patchResponse.getBody().asPrettyString();
        boolean isMatch = compareResponses(patchResponseBody, expectedResult);

        // Step 3: Update Excel with the result
        if (isMatch) {
            updateExcelSheet(rowNum, "Passed", "PATCH response matches expected result");
        } else {
            updateExcelSheet(rowNum, "Failed", "PATCH response does not match expected result");
        }

        return patchResponse;
    }

    /**
     * Create a request body for the PATCH API
     * @param originalRequestBody The original request body
     * @param accessToken Authentication token
     * @param id ID of the record to patch
     * @return The request body for the PATCH API
     * @throws Exception If any error occurs during the process
     */
    private String createPatchRequestBody(String originalRequestBody, String accessToken, String id) throws Exception {
        // Simply replace the auth token in the original request body
        // This preserves the original endpoint structure from the Excel sheet
        String updatedRequestBody = originalRequestBody.replace("{{auth}}", accessToken);
        logger.info("Updated PATCH request body with auth token: {}", updatedRequestBody);

        return updatedRequestBody;
    }

    /**
     * Make a PATCH request
     * @param rowNum Excel row number
     * @param requestBody Request body
     * @return Response from the PATCH request
     * @throws Exception If any error occurs during the process
     */
    private Response makePatchRequest(int rowNum, String requestBody) throws Exception {
        // Use the newer PostWithDynamicRequestBody class from api.helpers package
        api.helpers.PostWithDynamicRequestBody requestHandler = new api.helpers.PostWithDynamicRequestBody(logger, filePath, sheetName, url, body);

        // Use POST with "type": "patch" in the request body
        // This is a common pattern where the HTTP method is always POST, but the operation type is specified in the request body
        Response patchResponse = requestHandler.post(rowNum, requestBody);

        if (patchResponse == null) {
            logger.error("PATCH response is null");
            updateExcelSheet(rowNum, "Failed", "PATCH response is null");
            return null;
        }

        int patchStatusCode = patchResponse.getStatusCode();
        String patchResponseBody = patchResponse.getBody().asPrettyString();

        logger.info("PATCH response status code: {}", patchStatusCode);
        logger.info("PATCH response body: {}", patchResponseBody);

        return patchResponse;
    }

    /**
     * Perform a PATCH request and then get the record by ID
     * @param rowNum Excel row number
     * @param accessToken Authentication token
     * @throws Exception If any error occurs during the process
     */
    public void PatchAndGetById(int rowNum, String accessToken) throws Exception {
        logger.info("Running PatchAndGetById test for row {}", rowNum);

        try {
            // Step 1: Get the original request body from Excel
            String originalRequestBody = getRequestBodyFromExcel(rowNum);

            // Step 2: Create the PATCH request body
            String updatedRequestBody = createPatchRequestBody(originalRequestBody, accessToken, "");

            // Step 3: Make the PATCH request
            Response patchResponse = makePatchRequest(rowNum, updatedRequestBody);

            if (patchResponse == null) {
                logger.error("PATCH response is null");
                updateExcelSheet(rowNum, "Failed", "PATCH response is null");
                return;
            }

            // Step 3: Store the PATCH response in the Expected Result column
            String patchResponseBody = patchResponse.getBody().asPrettyString();
            try {
                excelUtils.setCellData(filePath, sheetName, rowNum, ExpectedResult, patchResponseBody);
                logger.info("Stored PATCH response in Expected Result column");
            } catch (Exception e) {
                logger.warn("Could not store PATCH response in Expected Result column: {}. Will continue with test.", e.getMessage());
            }

            // Check if the PATCH request was successful
            int statusCode = patchResponse.getStatusCode();
            if (statusCode >= 400) {
                logger.error("PATCH request failed with status code: {}", statusCode);
                updateExcelSheet(rowNum, Constants.STATUS_FAILED, Constants.MSG_PATCH_FAILED + statusCode);

                // If the PATCH request fails, use the default ID from configuration
                String id = ConfigManager.getDefaultId();
                logger.info("Using default ID from configuration: {}", id);

                // Step 5: Create a dynamic request body for GetById API
                String getByIdRequestBody = createGetByIdRequestBody(originalRequestBody, accessToken, id);

                // Step 6: Make the GetById request
                Response getByIdResponse = makeGetByIdRequest(rowNum, getByIdRequestBody);

                if (getByIdResponse == null) {
                    logger.error("GetById response is null");
                    updateExcelSheet(rowNum, "Failed", "GetById response is null");
                    return;
                }

                // Step 7: Store the GetById response in the Actual Result column
                String getByIdResponseBody = getByIdResponse.getBody().asPrettyString();
                try {
                    excelUtils.setCellData(filePath, sheetName, rowNum, ActualResult, getByIdResponseBody);
                    logger.info("Stored GetById response in Actual Result column");
                } catch (Exception e) {
                    logger.warn("Could not store GetById response in Actual Result column: {}. Will continue with test.", e.getMessage());
                }

                // Step 8: Update Excel with the result
                updateExcelSheet(rowNum, Constants.STATUS_COMPLETED, Constants.MSG_PATCH_FAILED_GETBYID_COMPLETED);

                return;
            }

            // Step 4: Extract the ID from the response
            String id = extractIdFromResponse(patchResponse);
            logger.info("Extracted ID from PATCH response: {}", id);

            if (id == null || id.isEmpty()) {
                logger.error("Failed to extract ID from PATCH response");

                // If we couldn't extract the ID, use the default ID from configuration
                id = ConfigManager.getDefaultId();
                logger.info("Using default ID from configuration: {}", id);
            }

            // Step 5: Create a dynamic request body for GetById API
            String getByIdRequestBody = createGetByIdRequestBody(originalRequestBody, accessToken, id);

            // Step 6: Make the GetById request
            Response getByIdResponse = makeGetByIdRequest(rowNum, getByIdRequestBody);

            if (getByIdResponse == null) {
                logger.error("GetById response is null");
                updateExcelSheet(rowNum, "Failed", "GetById response is null");
                return;
            }

            // Step 7: Store the GetById response in the Actual Result column
            String getByIdResponseBody = getByIdResponse.getBody().asPrettyString();
            try {
                excelUtils.setCellData(filePath, sheetName, rowNum, ActualResult, getByIdResponseBody);
                logger.info("Stored GetById response in Actual Result column");
            } catch (Exception e) {
                logger.warn("Could not store GetById response in Actual Result column: {}. Will continue with test.", e.getMessage());
            }

            // Step 8: Compare the PATCH response with the GetById response
            // If the PATCH response is an array, extract the first element for comparison
            String patchResponseForComparison = patchResponseBody;
            try {
                ObjectMapper mapper = new ObjectMapper();
                JsonNode patchNode = mapper.readTree(patchResponseBody);
                if (patchNode.isArray() && patchNode.size() > 0) {
                    // Extract the first element of the array
                    patchResponseForComparison = patchNode.get(0).toString();
                    logger.info("Extracted first element from PATCH response array for comparison: {}", patchResponseForComparison);
                }
            } catch (Exception e) {
                logger.warn("Error extracting first element from PATCH response array: {}", e.getMessage());
            }

            boolean isMatch = compareResponses(patchResponseForComparison, getByIdResponseBody);

            // Step 9: Update Excel with the result
            if (isMatch) {
                updateExcelSheet(rowNum, Constants.STATUS_PASSED, Constants.MSG_PATCH_GETBYID_MATCH);
            } else {
                updateExcelSheet(rowNum, Constants.STATUS_FAILED, Constants.MSG_PATCH_GETBYID_MISMATCH);
            }

            // Step 10: Validate with database
            validateWithDatabase(rowNum, id, getByIdResponseBody);

        } catch (Exception e) {
            logger.error("Error in PatchAndGetById: {}", e.getMessage(), e);
            updateExcelSheet(rowNum, "Failed", "Error in PatchAndGetById: " + e.getMessage());
        }
    }

    /**
     * Extract the ID from the response
     * @param response The response
     * @return The extracted ID
     */
    private String extractIdFromResponse(Response response) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            String responseBody = response.getBody().asString();
            JsonNode responseNode = mapper.readTree(responseBody);

            // Check if the response is an array
            if (responseNode.isArray() && responseNode.size() > 0) {
                // Get the first element of the array
                JsonNode firstElement = responseNode.get(0);

                // Try to find the ID in the first element
                if (firstElement.has(Constants.FIELD_ID)) {
                    return firstElement.get(Constants.FIELD_ID).asText();
                } else {
                    // Try to find any field that looks like an ID
                    Iterator<String> fieldNames = firstElement.fieldNames();
                    while (fieldNames.hasNext()) {
                        String fieldName = fieldNames.next();
                        if (fieldName.equalsIgnoreCase(Constants.FIELD_ID) || fieldName.endsWith("Id") || fieldName.endsWith("ID")) {
                            return firstElement.get(fieldName).asText();
                        }
                    }
                }
            } else {
                // Try to find the ID in the response
                if (responseNode.has(Constants.FIELD_ID)) {
                    return responseNode.get(Constants.FIELD_ID).asText();
                } else if (responseNode.has(Constants.FIELD_DATA) && responseNode.get(Constants.FIELD_DATA).has(Constants.FIELD_ID)) {
                    return responseNode.get(Constants.FIELD_DATA).get(Constants.FIELD_ID).asText();
                } else if (responseNode.has(Constants.FIELD_PAYLOAD) && responseNode.get(Constants.FIELD_PAYLOAD).has(Constants.FIELD_ID)) {
                    return responseNode.get(Constants.FIELD_PAYLOAD).get(Constants.FIELD_ID).asText();
                } else {
                    // Try to find any field that looks like an ID
                    Iterator<String> fieldNames = responseNode.fieldNames();
                    while (fieldNames.hasNext()) {
                        String fieldName = fieldNames.next();
                        if (fieldName.equalsIgnoreCase(Constants.FIELD_ID) || fieldName.endsWith("Id") || fieldName.endsWith("ID")) {
                            return responseNode.get(fieldName).asText();
                        }
                    }
                }
            }

            logger.error("Could not find ID in response: {}", responseBody);
            return null;
        } catch (Exception e) {
            logger.error("Error extracting ID from response: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Create a request body for the GetById API
     * @param originalRequestBody The original request body
     * @param accessToken Authentication token
     * @param id ID of the record to get
     * @return The request body for the GetById API
     * @throws Exception If any error occurs during the process
     */
    private String createGetByIdRequestBody(String originalRequestBody, String accessToken, String id) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode requestNode;

        try {
            // Try to parse the original request body as JSON
            JsonNode root = mapper.readTree(originalRequestBody);

            // Extract the entity name and ID from the endpoint
            String originalEndpoint = root.has(Constants.FIELD_ENDPOINT) ? root.get(Constants.FIELD_ENDPOINT).asText() : "";
            String entityName = "";
            String entityId = id; // Use the provided ID by default

            // Log the original endpoint for debugging
            logger.info("Original endpoint: {}", originalEndpoint);

            // Extract the entity name from the endpoint
            if (originalEndpoint.contains(Constants.PATTERN_UPDATE_PARTIAL_ENTITY)) {
                // For endpoints like "/core/api/updatePartialEntity/CountryMaster/1008"
                String[] parts = originalEndpoint.split(Constants.PATTERN_UPDATE_PARTIAL_ENTITY);
                if (parts.length > 1) {
                    String[] entityParts = parts[1].split("/");
                    if (entityParts.length > 0) {
                        entityName = entityParts[0]; // Extract CountryMaster from CountryMaster/1008

                        // If ID is not provided and there's an ID in the endpoint, use it
                        if ((id == null || id.isEmpty()) && entityParts.length > 1) {
                            entityId = entityParts[1]; // Extract 1008 from CountryMaster/1008
                        }
                    }
                }
            } else if (originalEndpoint.contains(Constants.PATTERN_API)) {
                // For endpoints like "/core/api/CountryMaster/1008"
                String[] parts = originalEndpoint.split(Constants.PATTERN_API);
                if (parts.length > 1) {
                    String[] entityParts = parts[1].split("/");
                    if (entityParts.length > 0) {
                        entityName = entityParts[0]; // Extract CountryMaster from CountryMaster/1008

                        // If ID is not provided and there's an ID in the endpoint, use it
                        if ((id == null || id.isEmpty()) && entityParts.length > 1) {
                            entityId = entityParts[1]; // Extract 1008 from CountryMaster/1008
                        }
                    }
                }
            }

            // If we couldn't extract the entity name, use the default from configuration
            if (entityName.isEmpty()) {
                entityName = ConfigManager.getDefaultEntityName();
            }

            // If we couldn't extract the ID, use the default from configuration
            if (entityId == null || entityId.isEmpty()) {
                entityId = ConfigManager.getDefaultId();
            }

            // Create the GET endpoint - remove /updatePartialEntity/ and use the correct pattern
            String getEndpoint = "/core/api/" + entityName + "/" + entityId;

            logger.info("Entity name: {}", entityName);
            logger.info("Entity ID: {}", entityId);
            logger.info("Final GET endpoint: {}", getEndpoint);

            // Create a new request node with the required structure
            requestNode = mapper.createObjectNode();
            requestNode.put(Constants.FIELD_ENDPOINT, getEndpoint);
            requestNode.put(Constants.FIELD_TYPE, Constants.TYPE_GET);
            requestNode.put(Constants.FIELD_TENANT_ID, "{tenantId}");
            requestNode.put(Constants.FIELD_AUTH, accessToken);

            // We don't need a payload for GET requests
            // Do not include the payload field at all, not even as null
        } catch (Exception e) {
            // If parsing fails, create a simple GET request
            requestNode = mapper.createObjectNode();

            // Use the default entity name and ID if not provided
            String entityName = ConfigManager.getDefaultEntityName();
            String entityId = (id != null && !id.isEmpty()) ? id : ConfigManager.getDefaultId();

            requestNode.put(Constants.FIELD_ENDPOINT, "/core/api/" + entityName + "/" + entityId);
            requestNode.put(Constants.FIELD_TYPE, Constants.TYPE_GET);
            requestNode.put(Constants.FIELD_TENANT_ID, "{tenantId}");
            requestNode.put(Constants.FIELD_AUTH, accessToken);
        }

        // Convert to JSON string
        String getRequestBody = mapper.writeValueAsString(requestNode);
        logger.info("GET request body: {}", getRequestBody);

        return getRequestBody;
    }

    /**
     * Make a GetById request
     * @param rowNum Excel row number
     * @param requestBody Request body
     * @return Response from the GetById request
     * @throws Exception If any error occurs during the process
     */
    private Response makeGetByIdRequest(int rowNum, String requestBody) throws Exception {
        // Use the newer PostWithDynamicRequestBody class from api.helpers package
        api.helpers.PostWithDynamicRequestBody requestHandler = new api.helpers.PostWithDynamicRequestBody(logger, filePath, sheetName, url, body);
        Response getResponse = requestHandler.post(rowNum, requestBody);

        if (getResponse == null) {
            logger.error("GET response is null");
            updateExcelSheet(rowNum, "Failed", "GET response is null");
            return null;
        }

        int getStatusCode = getResponse.getStatusCode();
        String getResponseBody = getResponse.getBody().asPrettyString();

        logger.info("GET response status code: {}", getStatusCode);
        logger.info("GET response body: {}", getResponseBody);

        return getResponse;
    }

    /**
     * Validate the response with the database
     * @param rowNum Excel row number
     * @param id ID of the record to validate
     * @param getByIdResponseBody The GetById response body
     * @throws Exception If any error occurs during the process
     */
    private void validateWithDatabase(int rowNum, String id, String getByIdResponseBody) throws Exception {
        try {
            // Get the table name from Excel
            String tableNameValue = excelUtils.getCellData(filePath, sheetName, rowNum, tableName);
            logger.info("Table name from Excel: {}", tableNameValue);

            if (tableNameValue == null || tableNameValue.isEmpty()) {
                logger.warn("Table name is empty, skipping database validation");
                return;
            }

            // Query the database with the ID
            String dbRecord = dbUtils.getRecordByIdAsJson(tableNameValue, "id", id);

            if (dbRecord.equals("{}")) {
                logger.warn("No record found in database for ID: {}", id);
                return;
            }

            // Use the GetById response for comparison since we might not be able to read from Excel
            String actualResult = getByIdResponseBody;

            // Compare the database record with the actual result
            boolean isMatch = compareResponses(dbRecord, actualResult);

            // Update Excel with the result
            if (isMatch) {
                updateExcelSheet(rowNum, Constants.STATUS_PASSED, Constants.MSG_DB_API_MATCH);
            } else {
                updateExcelSheet(rowNum, Constants.STATUS_FAILED, Constants.MSG_DB_API_MISMATCH);
            }
        } catch (Exception e) {
            logger.error("Error validating with database: {}", e.getMessage());
        }
    }
}
