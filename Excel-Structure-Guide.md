# Excel Structure Guide for CRUD Operations Testing

## 📋 **Excel File Structure**

### **File Location:** `data/SnackHack.xlsx`

### **Sheet Names:**
- **Photos** - For Photos API testing
- **Products** - For Products API testing

---

## 📊 **Column Structure (Same for both sheets)**

| Column | Name | Description | Example |
|--------|------|-------------|---------|
| A | URL | API endpoint URL | `/api/photos` |
| B | Request Body | JSON request body | `{"title":"Test Photo","url":"http://example.com"}` |
| C | Expected | Expected status code | `201` |
| D | Actual | Actual response (auto-filled) | `{"id":1,"title":"Test Photo"}` |
| E | Status | Test result (auto-filled) | `PASS` or `FAIL` |
| F | Defect ID | Generated defect ID (auto-filled) | `D_POST_STATUS_CODE_MISMATCH_123` |

---

## 📝 **Row Structure**

### **Photos Sheet Example:**

| Row | Operation | URL | Request Body | Expected |
|-----|-----------|-----|--------------|----------|
| 2 | CREATE | `/api/photos` | `{"title":"Test Photo","url":"http://example.com/photo.jpg","thumbnailUrl":"http://example.com/thumb.jpg","albumId":1,"userId":1}` | `201` |
| 3 | UPDATE | `/api/photos/{id}` | `{"title":"Updated Photo","url":"http://example.com/updated.jpg"}` | `200` |
| 4 | GET ALL | `/api/photos` | *(empty)* | `200` |
| 5 | GET BY ID | `/api/photos/{id}` | *(empty)* | `200` |
| 6 | DELETE | `/api/photos/{id}` | *(empty)* | `200` |
| 13 | AUTH | `/api/auth/login` | `{"username":"admin","password":"admin"}` | `200` |

### **Products Sheet Example:**

| Row | Operation | URL | Request Body | Expected |
|-----|-----------|-----|--------------|----------|
| 2 | CREATE | `/api/products` | `{"name":"Test Product","price":99.99,"category":"Electronics","brand":"TestBrand","sku":"TEST123","stock":10}` | `201` |
| 3 | UPDATE | `/api/products/{id}` | `{"name":"Updated Product","price":149.99}` | `200` |
| 4 | GET ALL | `/api/products` | *(empty)* | `200` |
| 5 | GET BY ID | `/api/products/{id}` | *(empty)* | `200` |
| 6 | DELETE | `/api/products/{id}` | *(empty)* | `200` |
| 13 | AUTH | `/api/auth/login` | `{"username":"admin","password":"admin"}` | `200` |

---

## 🔧 **Configuration File: `crud-config.properties`**

```properties
# API Configuration
base.url=http://localhost:8080

# Excel Configuration
excel.file.path=data/SnackHack.xlsx

# Database Configuration (optional)
database.url=*************************************
database.username=root
database.password=password
database.validation.enabled=true

# Feature Flags
defect.tracking.enabled=true
faker.data.generation.enabled=true
```

---

## 🎯 **Test Validations Performed**

### **1. Status Code Validation**
- ✅ **Successful Creation (201)**: Validates POST requests return 201
- ✅ **Successful Update (200)**: Validates PUT requests return 200
- ✅ **Successful Retrieval (200)**: Validates GET requests return 200
- ✅ **Successful Deletion (200/204)**: Validates DELETE requests return 200 or 204

### **2. Constraint Violation Testing**
- ✅ **Null Constraints**: Tests required fields with null values
- ✅ **Unique Constraints**: Tests duplicate entries
- ✅ **Data Type Constraints**: Tests invalid data types
- ✅ **Error Message Validation**: Validates error messages contain relevant information

### **3. Database Validation**
- ✅ **Record Creation**: Verifies records are created in database
- ✅ **Record Updates**: Verifies records are updated in database
- ✅ **Record Retrieval**: Compares API response with database records
- ✅ **Foreign Key Validation**: Validates foreign key relationships
- ✅ **Record Deletion**: Verifies records are deleted from database

### **4. Request/Response Matching**
- ✅ **Field Matching**: Validates request fields appear in response
- ✅ **Data Consistency**: Ensures data integrity between request and response

---

## 🚀 **How to Run Tests**

### **Method 1: Using TestNG XML**
```bash
mvn test -DsuiteXmlFile=testng-crud-operations.xml
```

### **Method 2: Individual Test Classes**
```bash
# Run Photos API tests
mvn test -Dtest=testCases.PhotosApiTestNew

# Run Products API tests
mvn test -Dtest=testCases.ProductsApiTestNew
```

### **Method 3: Using IDE**
1. Right-click on `PhotosApiTestNew.java` or `ProductsApiTestNew.java`
2. Select "Run as TestNG Test"

---

## 📊 **Expected Test Output**

### **Console Output:**
```
=== Photos API Test Setup ===
Authentication token obtained for Photos API testing
=== Starting Photos CRUD Operations Test ===
🔄 CRUD Operation: CREATE (POST)
=== POST Operation for photos (Row: 2) ===
Expected Status: 201, Actual Status: 201
✅ Created entity ID: 123
✅ Database validation passed - Record exists in photos table
✅ Request-Response matching validation passed
🔄 CRUD Operation: UPDATE (PUT)
=== PUT Operation for photos (Row: 3) ===
Expected Status: 200, Actual Status: 200
✅ Database validation passed - Record exists in photos table
```

### **Excel Results:**
- **Actual Column**: Populated with API responses
- **Status Column**: Shows PASS/FAIL
- **Defect ID Column**: Shows defect IDs for failed tests

### **Generated Reports:**
- **HTML Report**: `test-reports/crud-operations-report.html`
- **TestNG Reports**: `target/surefire-reports/`
- **Logs**: `logs/crud-operations.log`

---

## 🎉 **Success Indicators**

✅ **All tests pass**: Status column shows "PASS"  
✅ **No defect IDs**: Defect ID column is empty for passed tests  
✅ **Database validation**: Records are properly created/updated/deleted  
✅ **Constraint testing**: Proper error codes and messages for violations  
✅ **Foreign key validation**: Related records exist in database  

The framework is now ready to test your APIs with comprehensive validation! 🚀
