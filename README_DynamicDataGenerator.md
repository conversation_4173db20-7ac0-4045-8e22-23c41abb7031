# Enhanced Dynamic Data Generator

## 🎯 Overview
The Enhanced Dynamic Data Generator processes Excel JSON templates and replaces **ONLY** `{{faker}}` placeholders with dynamic data while preserving the exact structure from Excel.

## ✅ Key Features

### 1. **Exact Structure Preservation**
- **ONLY** processes fields present in Excel JSON template
- **NO** extra fields are added by the generator
- Field order and nesting preserved exactly
- Arrays and nested objects handled correctly

### 2. **Configurable Field Constraints**
- Field constraints loaded from `field-constraints.properties`
- Country short name automatically generates 3-character codes (e.g., "AG37", "LS30")
- Phone numbers follow 10-digit pattern
- Email addresses are properly formatted
- Easy to modify constraints without code changes

### 3. **Smart Placeholder Handling**
- `{{faker}}` → Replaced with dynamic data based on field constraints
- `{{foreign_key}}` → Preserved unchanged for dependency resolution
- Static values → Remain exactly as specified in Excel

## 📊 Examples

### Input (Excel Template):
```json
{
  "countryShortName": "{{faker}}",
  "priority": 5,
  "active": true,
  "description": "Static description",
  "countryId": "{{foreign_key}}"
}
```

### Output (Generated):
```json
{
  "countryShortName": "AG37",
  "priority": 5,
  "active": true,
  "description": "Static description",
  "countryId": "{{foreign_key}}"
}
```

## 🔧 Configuration

### Field Constraints (field-constraints.properties):
```properties
# Country Master Constraints
countryShortName.length=3
countryShortName.pattern=[A-Z]{3}
countryShortName.type=ALPHA_UPPER

# Phone Constraints
phone.length=10
phone.pattern=[0-9]{10}
phone.type=PHONE_NUMBER

# Email Constraints
email.maxLength=50
email.type=EMAIL
```

## 🚀 Usage

### 1. In Excel Body Column:
```json
{
  "endpoint": "/core/api/CountryMaster/save",
  "type": "post",
  "payload": {
    "countryShortName": "{{faker}}",
    "countryFullDesc": "{{faker}}",
    "active": true,
    "priority": 1
  }
}
```

### 2. In Java Code:
```java
DynamicDataGenerator generator = new DynamicDataGenerator();
String result = generator.processExcelJsonStructure(excelTemplate);
```

## ✅ Benefits

1. **Complete Control**: You define the exact JSON structure in Excel
2. **Dynamic Values**: Only `{{faker}}` fields get dynamic data
3. **Smart Generation**: Field names determine data type (email, phone, etc.)
4. **Foreign Key Support**: `{{foreign_key}}` preserved for dependency resolution
5. **Static Preservation**: All other values remain exactly as specified
6. **Configurable Constraints**: Field constraints loaded from properties file
7. **Database Validation**: Respects database constraints (e.g., length=3 for country codes)

## 🎯 Test Results

### Structure Preservation:
- ✅ Field count: 5 (expected: 5)
- ✅ countryShortName: 'AG37' (was {{faker}})
- ✅ priority: 5 (unchanged)
- ✅ active: true (unchanged)
- ✅ description: 'Static description' (unchanged)
- ✅ countryId: '{{foreign_key}}' (preserved foreign key)

### Constraint Validation:
- ✅ countryShortName constraint (length=3, pattern=[A-Z]{3}): PASSED
- ✅ phone constraint (length=10, pattern=[0-9]{10}): PASSED
- ✅ email constraint (valid format): PASSED

## 🔄 How It Works

1. **Read Excel JSON template** with `{{faker}}` and `{{foreign_key}}` placeholders
2. **Parse JSON structure** preserving exact hierarchy
3. **Replace only `{{faker}}` placeholders** with constraint-based dynamic data
4. **Keep `{{foreign_key}}` unchanged** for later dependency resolution
5. **Preserve all static values** exactly as specified in Excel
6. **Return JSON with exact structure** from Excel template

**Your enhanced dynamic data generator is ready to use! Just update your Excel templates with `{{faker}}` placeholders where you want dynamic data! 🎉**
