package utils;

/**
 * Defect details for bug tracking integration
 */
public class DefectDetails {
    private String title;
    private String description;
    private String severity;
    private String priority;
    private String component;
    private String environment;
    private String assignee;
    private String reporter;
    private String tableName; // Table name from Excel column 2

    public DefectDetails() {
        // Default values
        this.severity = "Medium";
        this.priority = "High";
        this.environment = "Test";
        this.reporter = "Automated Testing Framework";
    }

    // Getters and Setters
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSeverity() {
        return severity;
    }

    public void setSeverity(String severity) {
        this.severity = severity;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public String getComponent() {
        return component;
    }

    public void setComponent(String component) {
        this.component = component;
    }

    public String getEnvironment() {
        return environment;
    }

    public void setEnvironment(String environment) {
        this.environment = environment;
    }

    public String getAssignee() {
        return assignee;
    }

    public void setAssignee(String assignee) {
        this.assignee = assignee;
    }

    public String getReporter() {
        return reporter;
    }

    public void setReporter(String reporter) {
        this.reporter = reporter;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    @Override
    public String toString() {
        return String.format("DefectDetails{title='%s', severity='%s', priority='%s', component='%s', tableName='%s'}",
                           title, severity, priority, component, tableName);
    }
}
