package utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Database Validation Utilities for API Testing
 * Handles database queries and validation for Photos and Products tables
 */
public class DatabaseValidationUtils {
    private static final Logger logger = LoggerFactory.getLogger(DatabaseValidationUtils.class);

    private DatabaseUtils dbUtils;

    public DatabaseValidationUtils() {
        this.dbUtils = new DatabaseUtils(logger);
    }

    /**
     * Get photo data from database by ID
     */
    public String getPhotoFromDatabase(String photoId) {
        try {
            logger.info("Querying database for photo ID: {}", photoId);

            // Use the DatabaseUtils methods
            String result = dbUtils.getRecordById("photos", photoId);
            if (!result.equals("{}")) {
                logger.info("Photo found in database for ID: {}", photoId);
                return result;
            } else {
                logger.warn("Photo not found in database for ID: {}", photoId);
                return "{}";
            }
        } catch (Exception e) {
            logger.error("Error querying photo from database: " + e.getMessage());
            return "{}";
        }
    }

    /**
     * Get photo data from database with foreign key handling
     */
    public String getPhotoFromDatabaseWithForeignKeys(String photoId) {
        try {
            logger.info("Querying database for photo with foreign keys, ID: {}", photoId);

            // Use custom query through DatabaseUtils
            String sql = "SELECT p.*, a.title as album_title, u.username " +
                        "FROM photos p " +
                        "LEFT JOIN albums a ON p.album_id = a.id " +
                        "LEFT JOIN users u ON p.user_id = u.id " +
                        "WHERE p.id = '" + photoId + "'";

            String result = dbUtils.executeQuery(sql);
            if (!result.equals("[]")) {
                logger.info("Photo with foreign keys found in database for ID: {}", photoId);
                return result;
            } else {
                logger.warn("Photo not found in database for ID: {}", photoId);
                return "{}";
            }
        } catch (Exception e) {
            logger.error("Error querying photo with foreign keys from database: " + e.getMessage());
            return "{}";
        }
    }

    /**
     * Get product data from database by ID
     */
    public String getProductFromDatabase(String productId) {
        try {
            logger.info("Querying database for product ID: {}", productId);

            // Use the DatabaseUtils methods
            String result = dbUtils.getRecordById("products", productId);
            if (!result.equals("{}")) {
                logger.info("Product found in database for ID: {}", productId);
                return result;
            } else {
                logger.warn("Product not found in database for ID: {}", productId);
                return "{}";
            }
        } catch (Exception e) {
            logger.error("Error querying product from database: " + e.getMessage());
            return "{}";
        }
    }

    /**
     * Get product data from database with foreign key handling
     */
    public String getProductFromDatabaseWithForeignKeys(String productId) {
        try {
            logger.info("Querying database for product with foreign keys, ID: {}", productId);

            // Use custom query through DatabaseUtils
            String sql = "SELECT p.*, c.name as category_name, b.name as brand_name " +
                        "FROM products p " +
                        "LEFT JOIN categories c ON p.category_id = c.id " +
                        "LEFT JOIN brands b ON p.brand_id = b.id " +
                        "WHERE p.id = '" + productId + "'";

            String result = dbUtils.executeQuery(sql);
            if (!result.equals("[]")) {
                logger.info("Product with foreign keys found in database for ID: {}", productId);
                return result;
            } else {
                logger.warn("Product not found in database for ID: {}", productId);
                return "{}";
            }
        } catch (Exception e) {
            logger.error("Error querying product with foreign keys from database: " + e.getMessage());
            return "{}";
        }
    }

    /**
     * Verify record deletion in database
     */
    public boolean isRecordDeleted(String tableName, String recordId) {
        try {
            logger.info("Checking if record is deleted from table: {}, ID: {}", tableName, recordId);

            // Use DatabaseUtils to check if record exists
            String result = dbUtils.getRecordById(tableName, recordId);
            boolean isDeleted = result.equals("{}");

            if (isDeleted) {
                logger.info("Record is deleted from table: {}, ID: {}", tableName, recordId);
            } else {
                logger.info("Record still exists in table: {}, ID: {}", tableName, recordId);
            }

            return isDeleted;
        } catch (Exception e) {
            logger.error("Error checking record deletion: " + e.getMessage());
            return false;
        }
    }

    /**
     * Get all records from table with pagination
     */
    public String getAllRecordsFromTable(String tableName, int limit, int offset) {
        try {
            logger.info("Getting all records from table: {}, limit: {}, offset: {}", tableName, limit, offset);

            // Use DatabaseUtils method
            String result = dbUtils.getAllRecords(tableName, limit, offset);
            if (!result.equals("[]")) {
                logger.info("Retrieved {} records from table: {}", limit, tableName);
                return result;
            } else {
                logger.warn("No records found in table: {}", tableName);
                return "[]";
            }
        } catch (Exception e) {
            logger.error("Error getting all records from table: " + e.getMessage());
            return "[]";
        }
    }

    /**
     * Get record by filter from database
     */
    public String getRecordByFilter(String tableName, String filterKey, String filterValue) {
        try {
            logger.info("Getting record from table: {} with filter {}={}", tableName, filterKey, filterValue);

            // Use DatabaseUtils method
            String result = dbUtils.getRecordByFilter(tableName, filterKey, filterValue);
            if (!result.equals("{}")) {
                logger.info("Record found in table: {} with filter {}={}", tableName, filterKey, filterValue);
                return result;
            } else {
                logger.warn("No record found in table: {} with filter {}={}", tableName, filterKey, filterValue);
                return "{}";
            }
        } catch (Exception e) {
            logger.error("Error getting record by filter: " + e.getMessage());
            return "{}";
        }
    }
}
