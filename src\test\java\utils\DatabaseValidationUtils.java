package utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.sql.*;
import java.util.HashMap;
import java.util.Map;

/**
 * Database Validation Utilities for API Testing
 * Handles database queries and validation for Photos and Products tables
 */
public class DatabaseValidationUtils {
    private static final Logger logger = LoggerFactory.getLogger(DatabaseValidationUtils.class);
    
    private DatabaseUtils dbUtils;
    private ObjectMapper objectMapper;
    
    public DatabaseValidationUtils() {
        this.dbUtils = new DatabaseUtils();
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * Get photo data from database by ID
     */
    public String getPhotoFromDatabase(String photoId) {
        try {
            logger.info("Querying database for photo ID: {}", photoId);
            
            String sql = "SELECT * FROM photos WHERE id = ?";
            
            try (Connection conn = dbUtils.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {
                
                stmt.setString(1, photoId);
                
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        Map<String, Object> photoData = new HashMap<>();
                        
                        // Map database columns to JSON
                        photoData.put("id", rs.getString("id"));
                        photoData.put("title", rs.getString("title"));
                        photoData.put("description", rs.getString("description"));
                        photoData.put("url", rs.getString("url"));
                        photoData.put("thumbnailUrl", rs.getString("thumbnail_url"));
                        photoData.put("albumId", rs.getInt("album_id"));
                        photoData.put("userId", rs.getInt("user_id"));
                        photoData.put("tags", rs.getString("tags"));
                        photoData.put("createdAt", rs.getTimestamp("created_at"));
                        photoData.put("updatedAt", rs.getTimestamp("updated_at"));
                        photoData.put("createdBy", rs.getString("created_by"));
                        photoData.put("updatedBy", rs.getString("updated_by"));
                        
                        return objectMapper.writeValueAsString(photoData);
                    } else {
                        logger.warn("Photo not found in database for ID: {}", photoId);
                        return "{}";
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Error querying photo from database: " + e.getMessage());
            return "{}";
        }
    }
    
    /**
     * Get photo data from database with foreign key handling
     */
    public String getPhotoFromDatabaseWithForeignKeys(String photoId) {
        try {
            logger.info("Querying database for photo with foreign keys, ID: {}", photoId);
            
            String sql = """
                SELECT p.*, 
                       a.title as album_title, a.description as album_description,
                       u.username, u.email, u.first_name, u.last_name
                FROM photos p
                LEFT JOIN albums a ON p.album_id = a.id
                LEFT JOIN users u ON p.user_id = u.id
                WHERE p.id = ?
                """;
            
            try (Connection conn = dbUtils.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {
                
                stmt.setString(1, photoId);
                
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        Map<String, Object> photoData = new HashMap<>();
                        
                        // Photo data
                        photoData.put("id", rs.getString("id"));
                        photoData.put("title", rs.getString("title"));
                        photoData.put("description", rs.getString("description"));
                        photoData.put("url", rs.getString("url"));
                        photoData.put("thumbnailUrl", rs.getString("thumbnail_url"));
                        photoData.put("albumId", rs.getInt("album_id"));
                        photoData.put("userId", rs.getInt("user_id"));
                        photoData.put("tags", rs.getString("tags"));
                        photoData.put("createdAt", rs.getTimestamp("created_at"));
                        photoData.put("updatedAt", rs.getTimestamp("updated_at"));
                        photoData.put("createdBy", rs.getString("created_by"));
                        photoData.put("updatedBy", rs.getString("updated_by"));
                        
                        // Album data (foreign key)
                        Map<String, Object> albumData = new HashMap<>();
                        albumData.put("title", rs.getString("album_title"));
                        albumData.put("description", rs.getString("album_description"));
                        photoData.put("album", albumData);
                        
                        // User data (foreign key)
                        Map<String, Object> userData = new HashMap<>();
                        userData.put("username", rs.getString("username"));
                        userData.put("email", rs.getString("email"));
                        userData.put("firstName", rs.getString("first_name"));
                        userData.put("lastName", rs.getString("last_name"));
                        photoData.put("user", userData);
                        
                        return objectMapper.writeValueAsString(photoData);
                    } else {
                        logger.warn("Photo not found in database for ID: {}", photoId);
                        return "{}";
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Error querying photo with foreign keys from database: " + e.getMessage());
            return "{}";
        }
    }
    
    /**
     * Get product data from database by ID
     */
    public String getProductFromDatabase(String productId) {
        try {
            logger.info("Querying database for product ID: {}", productId);
            
            String sql = "SELECT * FROM products WHERE id = ?";
            
            try (Connection conn = dbUtils.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {
                
                stmt.setString(1, productId);
                
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        Map<String, Object> productData = new HashMap<>();
                        
                        // Map database columns to JSON
                        productData.put("id", rs.getString("id"));
                        productData.put("name", rs.getString("name"));
                        productData.put("description", rs.getString("description"));
                        productData.put("price", rs.getBigDecimal("price"));
                        productData.put("category", rs.getString("category"));
                        productData.put("brand", rs.getString("brand"));
                        productData.put("sku", rs.getString("sku"));
                        productData.put("stock", rs.getInt("stock"));
                        productData.put("isActive", rs.getBoolean("is_active"));
                        productData.put("imageUrl", rs.getString("image_url"));
                        productData.put("weight", rs.getBigDecimal("weight"));
                        productData.put("dimensions", rs.getString("dimensions"));
                        productData.put("createdAt", rs.getTimestamp("created_at"));
                        productData.put("updatedAt", rs.getTimestamp("updated_at"));
                        productData.put("createdBy", rs.getString("created_by"));
                        productData.put("updatedBy", rs.getString("updated_by"));
                        
                        return objectMapper.writeValueAsString(productData);
                    } else {
                        logger.warn("Product not found in database for ID: {}", productId);
                        return "{}";
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Error querying product from database: " + e.getMessage());
            return "{}";
        }
    }
    
    /**
     * Get product data from database with foreign key handling
     */
    public String getProductFromDatabaseWithForeignKeys(String productId) {
        try {
            logger.info("Querying database for product with foreign keys, ID: {}", productId);
            
            String sql = """
                SELECT p.*, 
                       c.name as category_name, c.description as category_description,
                       b.name as brand_name, b.description as brand_description,
                       s.name as supplier_name, s.contact_email as supplier_email
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                LEFT JOIN brands b ON p.brand_id = b.id
                LEFT JOIN suppliers s ON p.supplier_id = s.id
                WHERE p.id = ?
                """;
            
            try (Connection conn = dbUtils.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {
                
                stmt.setString(1, productId);
                
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        Map<String, Object> productData = new HashMap<>();
                        
                        // Product data
                        productData.put("id", rs.getString("id"));
                        productData.put("name", rs.getString("name"));
                        productData.put("description", rs.getString("description"));
                        productData.put("price", rs.getBigDecimal("price"));
                        productData.put("category", rs.getString("category"));
                        productData.put("brand", rs.getString("brand"));
                        productData.put("sku", rs.getString("sku"));
                        productData.put("stock", rs.getInt("stock"));
                        productData.put("isActive", rs.getBoolean("is_active"));
                        productData.put("imageUrl", rs.getString("image_url"));
                        productData.put("weight", rs.getBigDecimal("weight"));
                        productData.put("dimensions", rs.getString("dimensions"));
                        productData.put("createdAt", rs.getTimestamp("created_at"));
                        productData.put("updatedAt", rs.getTimestamp("updated_at"));
                        productData.put("createdBy", rs.getString("created_by"));
                        productData.put("updatedBy", rs.getString("updated_by"));
                        
                        // Category data (foreign key)
                        Map<String, Object> categoryData = new HashMap<>();
                        categoryData.put("name", rs.getString("category_name"));
                        categoryData.put("description", rs.getString("category_description"));
                        productData.put("categoryDetails", categoryData);
                        
                        // Brand data (foreign key)
                        Map<String, Object> brandData = new HashMap<>();
                        brandData.put("name", rs.getString("brand_name"));
                        brandData.put("description", rs.getString("brand_description"));
                        productData.put("brandDetails", brandData);
                        
                        // Supplier data (foreign key)
                        Map<String, Object> supplierData = new HashMap<>();
                        supplierData.put("name", rs.getString("supplier_name"));
                        supplierData.put("email", rs.getString("supplier_email"));
                        productData.put("supplier", supplierData);
                        
                        return objectMapper.writeValueAsString(productData);
                    } else {
                        logger.warn("Product not found in database for ID: {}", productId);
                        return "{}";
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Error querying product with foreign keys from database: " + e.getMessage());
            return "{}";
        }
    }
    
    /**
     * Verify record deletion in database
     */
    public boolean isRecordDeleted(String tableName, String recordId) {
        try {
            logger.info("Checking if record is deleted from table: {}, ID: {}", tableName, recordId);
            
            String sql = "SELECT COUNT(*) FROM " + tableName + " WHERE id = ?";
            
            try (Connection conn = dbUtils.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {
                
                stmt.setString(1, recordId);
                
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        int count = rs.getInt(1);
                        return count == 0; // Record is deleted if count is 0
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Error checking record deletion: " + e.getMessage());
        }
        
        return false;
    }
    
    /**
     * Get all records from table with pagination
     */
    public String getAllRecordsFromTable(String tableName, int limit, int offset) {
        try {
            logger.info("Getting all records from table: {}, limit: {}, offset: {}", tableName, limit, offset);
            
            String sql = "SELECT * FROM " + tableName + " ORDER BY created_at DESC LIMIT ? OFFSET ?";
            
            try (Connection conn = dbUtils.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {
                
                stmt.setInt(1, limit);
                stmt.setInt(2, offset);
                
                try (ResultSet rs = stmt.executeQuery()) {
                    return dbUtils.resultSetToJsonArray(rs);
                }
            }
        } catch (Exception e) {
            logger.error("Error getting all records from table: " + e.getMessage());
            return "[]";
        }
    }
}
