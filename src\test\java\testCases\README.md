# TestCases Package

## Overview
The TestCases package contains the actual test implementations for different API services. Each class in this package represents a set of tests for a specific API service or functionality.

## Components

### CoreService.java
This class implements tests for the core API services. It extends BasicTestCase1 and provides:

- **CRUD Operation Tests**: Tests for Create, Read, Update, and Delete operations
- **Data-Driven Testing**: Uses Excel data to drive test execution
- **Database Validation**: Validates API responses against database records

#### Key Methods

##### Test Setup
- `setup()`: Initializes the test environment and authenticates with the API

##### CRUD Tests
- `testCreateResource()`: Tests resource creation (POST)
- `testGetResource()`: Tests resource retrieval (GET)
- `testUpdateResource()`: Tests resource update (PUT)
- `testDeleteResource()`: Tests resource deletion (DELETE)

##### Validation Tests
- `testApiDatabaseConsistency()`: Validates API data against database records
- `testErrorHandling()`: Tests API error responses

## Adding New Test Cases

To add a new test case:

1. Create a new row in the Excel test data file with:
   - API endpoint
   - Request method
   - Request body
   - Expected response
   - Database table for validation

2. Add a new test method in the appropriate test class:

```java
@Test
public void testNewFeature() {
    // Get the row number for this test in the Excel file
    int rowNum = getRowNumForTest("NewFeatureTest");
    
    // Get authentication token
    String token = getAuthToken();
    
    // Execute the test and validate results
    compareAndUpdateStatusCaseResult(rowNum, token);
    
    // Validate against database if needed
    compareApiWithDatabase(rowNum, token);
}
```

## Best Practices

- **Naming**: Use descriptive test method names that indicate what is being tested
- **Independence**: Each test should be independent and not rely on other tests
- **Data**: Keep test data in Excel files, not hardcoded in test methods
- **Validation**: Always validate both API responses and database state
- **Documentation**: Add comments explaining the purpose of each test
- **Maintenance**: Update tests when API contracts change
