<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="testCases.OrderService" time="16.943" tests="1" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="21"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="Cp1252"/>
    <property name="java.class.path" value="D:\RestAssuredApiTesting\BasicFramework_RFilings - Copy\target\test-classes;D:\RestAssuredApiTesting\BasicFramework_RFilings - Copy\target\classes;C:\Users\<USER>\.m2\repository\io\rest-assured\rest-assured\4.4.0\rest-assured-4.4.0.jar;C:\Users\<USER>\.m2\repository\org\codehaus\groovy\groovy\3.0.8\groovy-3.0.8.jar;C:\Users\<USER>\.m2\repository\org\codehaus\groovy\groovy-xml\3.0.8\groovy-xml-3.0.8.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.13\httpclient-4.5.13.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.13\httpcore-4.4.13.jar;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpmime\4.5.13\httpmime-4.5.13.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest\2.1\hamcrest-2.1.jar;C:\Users\<USER>\.m2\repository\org\ccil\cowan\tagsoup\tagsoup\1.2.1\tagsoup-1.2.1.jar;C:\Users\<USER>\.m2\repository\io\rest-assured\json-path\4.4.0\json-path-4.4.0.jar;C:\Users\<USER>\.m2\repository\org\codehaus\groovy\groovy-json\3.0.8\groovy-json-3.0.8.jar;C:\Users\<USER>\.m2\repository\io\rest-assured\rest-assured-common\4.4.0\rest-assured-common-4.4.0.jar;C:\Users\<USER>\.m2\repository\io\rest-assured\xml-path\4.4.0\xml-path-4.4.0.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;C:\Users\<USER>\.m2\repository\com\sun\xml\bind\jaxb-impl\2.3.3\jaxb-impl-2.3.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\6.0.11\spring-jdbc-6.0.11.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\6.0.11\spring-core-6.0.11.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\6.0.11\spring-jcl-6.0.11.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\6.0.11\spring-tx-6.0.11.jar;C:\Users\<USER>\.m2\repository\com\beust\jcommander\1.82\jcommander-1.82.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest-all\1.3\hamcrest-all-1.3.jar;C:\Users\<USER>\.m2\repository\org\postgresql\postgresql\42.5.1\postgresql-42.5.1.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.5.0\checker-qual-3.5.0.jar;C:\Users\<USER>\.m2\repository\junit\junit\4.12\junit-4.12.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest-core\1.3\hamcrest-core-1.3.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.30\slf4j-api-1.7.30.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\6.0.11\spring-beans-6.0.11.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.30\lombok-1.18.30.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.3\logback-classic-1.2.3.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.3\logback-core-1.2.3.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.8.1\junit-jupiter-api-5.8.1.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.8.1\junit-platform-commons-1.8.1.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.8.1\junit-jupiter-engine-5.8.1.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.8.1\junit-platform-engine-1.8.1.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.11.0\commons-io-2.11.0.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.19.0\log4j-api-2.19.0.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-core\2.19.0\log4j-core-2.19.0.jar;C:\Users\<USER>\.m2\repository\com\aventstack\extentreports\4.1.1\extentreports-4.1.1.jar;C:\Users\<USER>\.m2\repository\org\freemarker\freemarker\2.3.29\freemarker-2.3.29.jar;C:\Users\<USER>\.m2\repository\org\mongodb\mongodb-driver\3.12.0\mongodb-driver-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\mongodb\mongodb-driver-core\3.12.0\mongodb-driver-core-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\mongodb\bson\3.12.0\bson-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-runner\1.7.0\junit-platform-runner-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-launcher\1.7.0\junit-platform-launcher-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-suite-api\1.7.0\junit-platform-suite-api-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\plugins\maven-compiler-plugin\3.8.1\maven-compiler-plugin-3.8.1.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-plugin-api\3.0\maven-plugin-api-3.0.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-model\3.0\maven-model-3.0.jar;C:\Users\<USER>\.m2\repository\org\sonatype\sisu\sisu-inject-plexus\1.4.2\sisu-inject-plexus-1.4.2.jar;C:\Users\<USER>\.m2\repository\org\sonatype\sisu\sisu-inject-bean\1.4.2\sisu-inject-bean-1.4.2.jar;C:\Users\<USER>\.m2\repository\org\sonatype\sisu\sisu-guice\2.1.7\sisu-guice-2.1.7-noaop.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-artifact\3.0\maven-artifact-3.0.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-utils\2.0.4\plexus-utils-2.0.4.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-core\3.0\maven-core-3.0.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-settings\3.0\maven-settings-3.0.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-settings-builder\3.0\maven-settings-builder-3.0.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-repository-metadata\3.0\maven-repository-metadata-3.0.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-model-builder\3.0\maven-model-builder-3.0.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-aether-provider\3.0\maven-aether-provider-3.0.jar;C:\Users\<USER>\.m2\repository\org\sonatype\aether\aether-impl\1.7\aether-impl-1.7.jar;C:\Users\<USER>\.m2\repository\org\sonatype\aether\aether-spi\1.7\aether-spi-1.7.jar;C:\Users\<USER>\.m2\repository\org\sonatype\aether\aether-api\1.7\aether-api-1.7.jar;C:\Users\<USER>\.m2\repository\org\sonatype\aether\aether-util\1.7\aether-util-1.7.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-interpolation\1.14\plexus-interpolation-1.14.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-classworlds\2.2.3\plexus-classworlds-2.2.3.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-component-annotations\1.5.5\plexus-component-annotations-1.5.5.jar;C:\Users\<USER>\.m2\repository\org\sonatype\plexus\plexus-sec-dispatcher\1.3\plexus-sec-dispatcher-1.3.jar;C:\Users\<USER>\.m2\repository\org\sonatype\plexus\plexus-cipher\1.4\plexus-cipher-1.4.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\shared\maven-shared-utils\3.2.1\maven-shared-utils-3.2.1.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\shared\maven-shared-incremental\1.1\maven-shared-incremental-1.1.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-java\0.9.10\plexus-java-0.9.10.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\6.2\asm-6.2.jar;C:\Users\<USER>\.m2\repository\com\thoughtworks\qdox\qdox\2.0-M8\qdox-2.0-M8.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-compiler-api\2.8.4\plexus-compiler-api-2.8.4.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-compiler-manager\2.8.4\plexus-compiler-manager-2.8.4.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-compiler-javac\2.8.4\plexus-compiler-javac-2.8.4.jar;C:\Users\<USER>\.m2\repository\org\testng\testng\7.7.0\testng-7.7.0.jar;C:\Users\<USER>\.m2\repository\org\webjars\jquery\3.6.1\jquery-3.6.1.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml\5.2.3\poi-ooxml-5.2.3.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml-lite\5.2.3\poi-ooxml-lite-5.2.3.jar;C:\Users\<USER>\.m2\repository\org\apache\xmlbeans\xmlbeans\5.1.1\xmlbeans-5.1.1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.21\commons-compress-1.21.jar;C:\Users\<USER>\.m2\repository\com\github\virtuald\curvesapi\1.07\curvesapi-1.07.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi\5.2.3\poi-5.2.3.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-math3\3.6.1\commons-math3-3.6.1.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\SparseBitSet\1.2\SparseBitSet-1.2.jar;C:\Users\<USER>\.m2\repository\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.10.1\gson-2.10.1.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.15.2\jackson-databind-2.15.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.15.2\jackson-annotations-2.15.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.15.2\jackson-core-2.15.2.jar;C:\Users\<USER>\.m2\repository\com\github\javafaker\javafaker\1.0.2\javafaker-1.0.2.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.5\commons-lang3-3.5.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.23\snakeyaml-1.23-android.jar;C:\Users\<USER>\.m2\repository\com\github\mifmif\generex\1.0.2\generex-1.0.2.jar;C:\Users\<USER>\.m2\repository\dk\brics\automaton\automaton\1.11-8\automaton-1.11-8.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Calcutta"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="21"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="IN"/>
    <property name="sun.boot.library.path" value="C:\Program Files\Java\jdk-21\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire5707240254829135120\surefirebooter5277171263297839486.jar C:\Users\<USER>\AppData\Local\Temp\surefire5707240254829135120 2025-05-26T16-40-47_737-jvmRun1 surefire17233307132564430414tmp surefire_08052122689783507005tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="test" value="OrderService#TC_01"/>
    <property name="surefire.test.class.path" value="D:\RestAssuredApiTesting\BasicFramework_RFilings - Copy\target\test-classes;D:\RestAssuredApiTesting\BasicFramework_RFilings - Copy\target\classes;C:\Users\<USER>\.m2\repository\io\rest-assured\rest-assured\4.4.0\rest-assured-4.4.0.jar;C:\Users\<USER>\.m2\repository\org\codehaus\groovy\groovy\3.0.8\groovy-3.0.8.jar;C:\Users\<USER>\.m2\repository\org\codehaus\groovy\groovy-xml\3.0.8\groovy-xml-3.0.8.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.13\httpclient-4.5.13.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.13\httpcore-4.4.13.jar;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpmime\4.5.13\httpmime-4.5.13.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest\2.1\hamcrest-2.1.jar;C:\Users\<USER>\.m2\repository\org\ccil\cowan\tagsoup\tagsoup\1.2.1\tagsoup-1.2.1.jar;C:\Users\<USER>\.m2\repository\io\rest-assured\json-path\4.4.0\json-path-4.4.0.jar;C:\Users\<USER>\.m2\repository\org\codehaus\groovy\groovy-json\3.0.8\groovy-json-3.0.8.jar;C:\Users\<USER>\.m2\repository\io\rest-assured\rest-assured-common\4.4.0\rest-assured-common-4.4.0.jar;C:\Users\<USER>\.m2\repository\io\rest-assured\xml-path\4.4.0\xml-path-4.4.0.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;C:\Users\<USER>\.m2\repository\com\sun\xml\bind\jaxb-impl\2.3.3\jaxb-impl-2.3.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\6.0.11\spring-jdbc-6.0.11.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\6.0.11\spring-core-6.0.11.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\6.0.11\spring-jcl-6.0.11.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\6.0.11\spring-tx-6.0.11.jar;C:\Users\<USER>\.m2\repository\com\beust\jcommander\1.82\jcommander-1.82.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest-all\1.3\hamcrest-all-1.3.jar;C:\Users\<USER>\.m2\repository\org\postgresql\postgresql\42.5.1\postgresql-42.5.1.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.5.0\checker-qual-3.5.0.jar;C:\Users\<USER>\.m2\repository\junit\junit\4.12\junit-4.12.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest-core\1.3\hamcrest-core-1.3.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.30\slf4j-api-1.7.30.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\6.0.11\spring-beans-6.0.11.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.30\lombok-1.18.30.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.3\logback-classic-1.2.3.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.3\logback-core-1.2.3.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.8.1\junit-jupiter-api-5.8.1.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.8.1\junit-platform-commons-1.8.1.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.8.1\junit-jupiter-engine-5.8.1.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.8.1\junit-platform-engine-1.8.1.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.11.0\commons-io-2.11.0.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.19.0\log4j-api-2.19.0.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-core\2.19.0\log4j-core-2.19.0.jar;C:\Users\<USER>\.m2\repository\com\aventstack\extentreports\4.1.1\extentreports-4.1.1.jar;C:\Users\<USER>\.m2\repository\org\freemarker\freemarker\2.3.29\freemarker-2.3.29.jar;C:\Users\<USER>\.m2\repository\org\mongodb\mongodb-driver\3.12.0\mongodb-driver-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\mongodb\mongodb-driver-core\3.12.0\mongodb-driver-core-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\mongodb\bson\3.12.0\bson-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-runner\1.7.0\junit-platform-runner-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-launcher\1.7.0\junit-platform-launcher-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-suite-api\1.7.0\junit-platform-suite-api-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\plugins\maven-compiler-plugin\3.8.1\maven-compiler-plugin-3.8.1.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-plugin-api\3.0\maven-plugin-api-3.0.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-model\3.0\maven-model-3.0.jar;C:\Users\<USER>\.m2\repository\org\sonatype\sisu\sisu-inject-plexus\1.4.2\sisu-inject-plexus-1.4.2.jar;C:\Users\<USER>\.m2\repository\org\sonatype\sisu\sisu-inject-bean\1.4.2\sisu-inject-bean-1.4.2.jar;C:\Users\<USER>\.m2\repository\org\sonatype\sisu\sisu-guice\2.1.7\sisu-guice-2.1.7-noaop.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-artifact\3.0\maven-artifact-3.0.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-utils\2.0.4\plexus-utils-2.0.4.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-core\3.0\maven-core-3.0.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-settings\3.0\maven-settings-3.0.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-settings-builder\3.0\maven-settings-builder-3.0.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-repository-metadata\3.0\maven-repository-metadata-3.0.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-model-builder\3.0\maven-model-builder-3.0.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-aether-provider\3.0\maven-aether-provider-3.0.jar;C:\Users\<USER>\.m2\repository\org\sonatype\aether\aether-impl\1.7\aether-impl-1.7.jar;C:\Users\<USER>\.m2\repository\org\sonatype\aether\aether-spi\1.7\aether-spi-1.7.jar;C:\Users\<USER>\.m2\repository\org\sonatype\aether\aether-api\1.7\aether-api-1.7.jar;C:\Users\<USER>\.m2\repository\org\sonatype\aether\aether-util\1.7\aether-util-1.7.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-interpolation\1.14\plexus-interpolation-1.14.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-classworlds\2.2.3\plexus-classworlds-2.2.3.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-component-annotations\1.5.5\plexus-component-annotations-1.5.5.jar;C:\Users\<USER>\.m2\repository\org\sonatype\plexus\plexus-sec-dispatcher\1.3\plexus-sec-dispatcher-1.3.jar;C:\Users\<USER>\.m2\repository\org\sonatype\plexus\plexus-cipher\1.4\plexus-cipher-1.4.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\shared\maven-shared-utils\3.2.1\maven-shared-utils-3.2.1.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\shared\maven-shared-incremental\1.1\maven-shared-incremental-1.1.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-java\0.9.10\plexus-java-0.9.10.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\6.2\asm-6.2.jar;C:\Users\<USER>\.m2\repository\com\thoughtworks\qdox\qdox\2.0-M8\qdox-2.0-M8.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-compiler-api\2.8.4\plexus-compiler-api-2.8.4.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-compiler-manager\2.8.4\plexus-compiler-manager-2.8.4.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-compiler-javac\2.8.4\plexus-compiler-javac-2.8.4.jar;C:\Users\<USER>\.m2\repository\org\testng\testng\7.7.0\testng-7.7.0.jar;C:\Users\<USER>\.m2\repository\org\webjars\jquery\3.6.1\jquery-3.6.1.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml\5.2.3\poi-ooxml-5.2.3.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml-lite\5.2.3\poi-ooxml-lite-5.2.3.jar;C:\Users\<USER>\.m2\repository\org\apache\xmlbeans\xmlbeans\5.1.1\xmlbeans-5.1.1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.21\commons-compress-1.21.jar;C:\Users\<USER>\.m2\repository\com\github\virtuald\curvesapi\1.07\curvesapi-1.07.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi\5.2.3\poi-5.2.3.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-math3\3.6.1\commons-math3-3.6.1.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\SparseBitSet\1.2\SparseBitSet-1.2.jar;C:\Users\<USER>\.m2\repository\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.10.1\gson-2.10.1.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.15.2\jackson-databind-2.15.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.15.2\jackson-annotations-2.15.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.15.2\jackson-core-2.15.2.jar;C:\Users\<USER>\.m2\repository\com\github\javafaker\javafaker\1.0.2\javafaker-1.0.2.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.5\commons-lang3-3.5.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.23\snakeyaml-1.23-android.jar;C:\Users\<USER>\.m2\repository\com\github\mifmif\generex\1.0.2\generex-1.0.2.jar;C:\Users\<USER>\.m2\repository\dk\brics\automaton\automaton\1.11-8\automaton-1.11-8.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Program Files\Java\jdk-21"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="D:\RestAssuredApiTesting\BasicFramework_RFilings - Copy"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire5707240254829135120\surefirebooter5277171263297839486.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="21.0.7+8-LTS-245"/>
    <property name="user.name" value="Dell"/>
    <property name="stdout.encoding" value="Cp1252"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="C:\Users\<USER>\.m2\repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="21.0.7"/>
    <property name="user.dir" value="D:\RestAssuredApiTesting\BasicFramework_RFilings - Copy"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="native.encoding" value="Cp1252"/>
    <property name="java.library.path" value="C:\Program Files\Java\jdk-21\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Java\jdk-21\bin;C:\Users\<USER>\Downloads\apache-maven-3.9.9-bin;C:\Program Files\Git\cmd;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.1.1.1\bin;;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\JetBrains\PyCharm Community Edition 2025.1.1.1\bin;;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\Downloads\apache-maven-3.9.9-bin\apache-maven-3.9.9\bin;;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="Cp1252"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="21.0.7+8-LTS-245"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="65.0"/>
  </properties>
  <testcase name="TC_01" classname="testCases.OrderService" time="8.846">
    <system-out><![CDATA[INFO  utils.TestConfiguration - Loaded test configuration from test-config.properties
INFO  testCases.OrderService - === Order Service Test Setup with Faker Processing ===
INFO  testCases.OrderService - Loading dynamic configuration for Order Service...
INFO  testCases.OrderService - Order Service configuration loaded successfully
INFO  utils.DynamicDataGenerator - DynamicDataGenerator initialized with universal constraints
INFO  utils.DefectConfig - Loaded defect configuration from defect-config.properties
WARN  utils.ComprehensiveApiTestEngine - config.properties file not found, using default database properties
INFO  utils.ComprehensiveApiTestEngine - Using default database properties: URL=******************************************, User=postgres, Timeout=30
INFO  utils.DynamicDataGenerator - DynamicDataGenerator initialized with universal constraints
WARN  testCases.OrderService - config.properties file not found, using default database properties
INFO  testCases.OrderService - Using default database properties: URL=******************************************, User=postgres, Timeout=30
WARN  testCases.OrderService - config.properties file not found, using default database properties
INFO  testCases.OrderService - Using default database properties: URL=******************************************, User=postgres, Timeout=30
WARN  testCases.OrderService - config.properties file not found, using default database properties
INFO  testCases.OrderService - Using default database properties: URL=******************************************, User=postgres, Timeout=30
WARN  testCases.OrderService - config.properties file not found, using default database properties
INFO  testCases.OrderService - Using default database properties: URL=******************************************, User=postgres, Timeout=30
Standard Commons Logging discovery in action with spring-jcl: please remove commons-logging.jar from classpath in order to avoid potential conflicts
Headers:		Accept=*/*
				Content-Type=application/json
INFO  testCases.OrderService - Request: Vary=Origin
Vary=Access-Control-Request-Method
Vary=Access-Control-Request-Headers
Content-Type=text/plain;charset=UTF-8
Content-Length=361
Date=Mon, 26 May 2025 11:10:56 GMT
Keep-Alive=timeout=60
Connection=keep-alive
INFO  testCases.OrderService - ResponseBody: {"token":"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJFTkM6THBDRllFYVQrbVFwSHgzZWg4dEN3NzlQVDR0RG9Pd2VJR1oxU3FpU3Rtaz0iLCJpYXQiOjE3NDgyNTc4NTYsInBlcm1pc3Npb25zIjpbIkNPIl0sImV4cCI6MTc0ODMwMTA1Nn0.Oj9qBCUD9fumGer9ihv4cHdVBYIeIEJh3ICyMvwTqTOISHDCo8Aq_c4_OqKA1gy0lmig---z_byVaBa4iAX62g","type":"Bearer","id":10001,"username":"<EMAIL>","permissions":["CO"]}
INFO  testCases.OrderService - Response: 
INFO  testCases.OrderService - Request: 201
INFO  testCases.OrderService - text/plain;charset=UTF-8
INFO  testCases.OrderService - New Access Token: eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJFTkM6THBDRllFYVQrbVFwSHgzZWg4dEN3NzlQVDR0RG9Pd2VJR1oxU3FpU3Rtaz0iLCJpYXQiOjE3NDgyNTc4NTYsInBlcm1pc3Npb25zIjpbIkNPIl0sImV4cCI6MTc0ODMwMTA1Nn0.Oj9qBCUD9fumGer9ihv4cHdVBYIeIEJh3ICyMvwTqTOISHDCo8Aq_c4_OqKA1gy0lmig---z_byVaBa4iAX62g
INFO  testCases.OrderService - Sign-in response status: 201
INFO  testCases.OrderService - Sign-in response body: {"token":"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJFTkM6THBDRllFYVQrbVFwSHgzZWg4dEN3NzlQVDR0RG9Pd2VJR1oxU3FpU3Rtaz0iLCJpYXQiOjE3NDgyNTc4NTYsInBlcm1pc3Npb25zIjpbIkNPIl0sImV4cCI6MTc0ODMwMTA1Nn0.Oj9qBCUD9fumGer9ihv4cHdVBYIeIEJh3ICyMvwTqTOISHDCo8Aq_c4_OqKA1gy0lmig---z_byVaBa4iAX62g","type":"Bearer","id":10001,"username":"<EMAIL>","permissions":["CO"]}
INFO  testCases.OrderService - Authentication token obtained for Order Service testing
WARN  testCases.OrderService - config.properties file not found, using default database properties
INFO  testCases.OrderService - Using default database properties: URL=******************************************, User=postgres, Timeout=30
WARN  testCases.OrderService - config.properties file not found, using default database properties
INFO  testCases.OrderService - Using default database properties: URL=******************************************, User=postgres, Timeout=30
WARN  testCases.OrderService - config.properties file not found, using default database properties
INFO  testCases.OrderService - Using default database properties: URL=******************************************, User=postgres, Timeout=30
WARN  testCases.OrderService - config.properties file not found, using default database properties
INFO  testCases.OrderService - Using default database properties: URL=******************************************, User=postgres, Timeout=30
INFO  testCases.OrderService - BasicTestCase1 initialized with proper configuration
INFO  testCases.OrderService - === Executing Order Service TC_01 with Faker Processing ===
INFO  utils.ComprehensiveApiTestEngine - === Executing Comprehensive API Test - Sheet: Order Service, Row: 14 ===
INFO  utils.ComprehensiveApiTestEngine - Processing POST operation for endpoint: /order/api/BundleProduct/save
INFO  utils.ComprehensiveApiTestEngine - === Executing POST Operation ===
INFO  utils.DynamicDataGenerator - Processing Excel JSON structure (preserving exact structure): {"endpoint":"/order/api/BundleProduct/save","payload":{"sku":"{{faker}}","name":"Premium Office Package","isActive":true,"bundleType":"null","priceCalculation":"Add"},"type":"post","tenantId":"redberyl_redberyltech_com","auth":"{{auth}}"}
INFO  utils.DynamicDataGenerator - Detected table context: unknown
DEBUG utils.DynamicDataGenerator - Generating data for field 'sku' in table context 'unknown'
INFO  utils.DynamicDataGenerator - Processed result (exact structure preserved): {"endpoint":"/order/api/BundleProduct/save","payload":{"sku":"ha57","name":"Premium Office Package","isActive":true,"bundleType":"null","priceCalculation":"Add"},"type":"post","tenantId":"redberyl_redberyltech_com","auth":"{{auth}}"}
INFO  utils.UniversalApiHandler - Processing universal API request: {"endpoint":"/order/api/BundleProduct/save","payload":{"sku":"ha57","name":"Premium Office Package","isActive":true,"bundleType":"null","priceCalculation":"Add"},"type":"post","tenantId":"redberyl_redberyltech_com","auth":"{{auth}}"}
INFO  utils.UniversalApiHandler - Extracted tenant ID: 'redberyl_redberyltech_com'
INFO  utils.UniversalApiHandler - Full URL: http://localhost:9762/order/api/BundleProduct/save, Method: POST
INFO  utils.DynamicDataGenerator - Processing Excel JSON structure (preserving exact structure): {"sku":"ha57","name":"Premium Office Package","isActive":true,"bundleType":"null","priceCalculation":"Add"}
INFO  utils.DynamicDataGenerator - Detected table context: unknown
INFO  utils.DynamicDataGenerator - Processed result (exact structure preserved): {"sku":"ha57","name":"Premium Office Package","isActive":true,"bundleType":"null","priceCalculation":"Add"}
INFO  utils.UniversalApiHandler - Adding X-TenantID header: 'redberyl_redberyltech_com'
INFO  utils.UniversalApiHandler - POST Response - Status: 201, Body: {"priceCalculation":"Add","name":"Premium Office Package","id":5016,"bundleType":"null","sku":"ha57","isActive":true}
INFO  utils.ComprehensiveApiTestEngine - Status Code Validation - Expected: 201, Actual: 201
INFO  utils.ComprehensiveApiTestEngine - ✅ Status Code Validation - Expected: 201, Actual: 201, Status: Passed (GREEN)
INFO  testCases.OrderService - ✅ Order Service TC_01 completed with faker processing
]]></system-out>
  </testcase>
</testsuite>