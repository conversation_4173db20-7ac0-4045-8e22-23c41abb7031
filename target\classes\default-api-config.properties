# Default API Configuration Properties

# Default API patterns for different services
api.pattern.default=/api/
api.pattern.core=/api/
api.pattern.auth=/api/
api.pattern.contact=/api/
api.pattern.order=/api/
api.pattern.user=/api/
api.pattern.product=/api/
api.pattern.payment=/api/

# Operation suffixes for different request types
operation.suffix.post=/save
operation.suffix.put=/update
operation.suffix.delete=/delete
operation.suffix.get=
operation.suffix.getAll=/list

# Operation to request type mapping
operation.type.save=post
operation.type.update=put
operation.type.delete=delete
operation.type.list=get
operation.type.getall=get
operation.type.findall=get

# Common operation names (comma-separated)
common.operations=save,update,delete,get,find,search,list,all,count,exists,findBy,getBy,searchBy,findAll,getAll,findById,getById

# Common services (comma-separated)
common.services=core,auth,authentication,contact,order,user,product,payment

# Special endpoints (comma-separated)
special.endpoints=/decrypt,/oauth/,/token,/login,/auth/

# Entity type to service mapping
entity.service.country=core
entity.service.state=core
entity.service.master=core
entity.service.user=auth
entity.service.login=auth
entity.service.contact=contact
entity.service.address=contact
entity.service.order=order
entity.service.product=order
