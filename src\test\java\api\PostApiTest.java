package api;

import io.restassured.response.Response;
import org.slf4j.Logger;
import org.testng.Assert;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import api.helpers.PostWithDynamicRequestBody;
import basic.Post.PostWithAccessToken;

/**
 * Class for testing POST API operations
 */
public class PostApiTest extends ApiTestBase {

    /**
     * Constructor
     * @param logger Logger instance
     * @param filePath Excel file path
     * @param sheetName Excel sheet name
     * @param url URL column index
     * @param body Request body column index
     * @param Status Status column index
     * @param ActualResult Actual result column index
     * @param ExpectedResult Expected result column index
     * @param tableName Table name column index
     */
    public PostApiTest(Logger logger, String filePath, String sheetName, int url, int body, int Status, int ActualResult, int ExpectedResult, int tableName) {
        super(logger, filePath, sheetName, url, body, Status, ActualResult, ExpectedResult, tableName);
    }

    /**
     * Get the POST request status code
     * @param rowNum Excel row number
     * @param accessToken Authentication token
     * @return Status code from the response
     * @throws Exception If any error occurs during the process
     */
    public int getPostRequestStatusCode(int rowNum, String accessToken) throws Exception {
        // Create the POST request with the token in the request body
        PostWithAccessToken post = new PostWithAccessToken(logger, filePath, sheetName, url, body, accessToken);

        // Log the request details for debugging
        logger.info("Making POST request with token to row: {}", rowNum);

        Response response = post.post(rowNum);

        // Log the response for debugging
        String responseBody = response.getBody().asString();
        logger.info("Response status code: {}", response.getStatusCode());
        logger.info("Response body: {}", responseBody);

        // Return the actual status code from the response
        return response.getStatusCode();
    }

    /**
     * Get the POST request error message
     * @param rowNum Excel row number
     * @param accessToken Authentication token
     * @return Error message from the response
     * @throws Exception If any error occurs during the process
     */
    public String getPostRequestErrorMessage(int rowNum, String accessToken) throws Exception {
        // Create the POST request with the token in the request body
        PostWithAccessToken post = new PostWithAccessToken(logger, filePath, sheetName, url, body, accessToken);

        // Log the request details for debugging
        logger.info("Making POST request for error message with token to row: {}", rowNum);

        Response response = post.post(rowNum);

        // Log the response for debugging
        logger.info("Error message response status code: {}", response.getStatusCode());

        // Return the error message from the response body (assuming it's in plain text or JSON)
        String errorMessage = response.getBody().asString();
        logger.info("Error message response body: {}", errorMessage);
        return errorMessage;
    }

    /**
     * Compare and update status code result
     * @param rowNum Excel row number
     * @param accessToken Authentication token
     */
    public void compareAndUpdateStatusCodeResult(int rowNum, String accessToken) {
        try {
            logger.info("Starting test case for row {} with token: {}", rowNum, accessToken);

            // Get the request body from Excel for debugging
            String requestBody = getRequestBodyFromExcel(rowNum);
            logger.info("Request body for row {}: {}", rowNum, requestBody);

            // Get the actual status code from the response
            logger.info("Getting status code for row {}", rowNum);
            int actualResult = getPostRequestStatusCode(rowNum, accessToken);
            logger.info("Received status code: {}", actualResult);

            // Get the expected status code from the Excel sheet
            String expectedResult = getExpectedResultFromExcel(rowNum);
            logger.info("Expected result from Excel: {}", expectedResult);
            int expectedResultInt = Integer.parseInt(expectedResult.trim());
            logger.info("Parsed expected status code: {}", expectedResultInt);

            // Compare the actual status code with the expected status code
            if (actualResult == expectedResultInt) {
                // If the status codes match, update the Excel sheet with 'Passed' status
                updateExcelSheet(rowNum, "Passed", String.valueOf(actualResult));
                logger.info("Test passed: Status code {} matches the expected status code {}", actualResult, expectedResultInt);
            } else {
                // If the status codes don't match, update the Excel sheet with 'Failed' status
                updateExcelSheet(rowNum, "Failed", String.valueOf(actualResult));
                logger.error("Test failed: Actual status code {} does not match expected {}", actualResult, expectedResultInt);
            }
        } catch (Exception e) {
            logger.error("Error comparing results or updating Excel sheet: {}", e.getMessage());
            e.printStackTrace(); // Print stack trace for more detailed error information
        }
    }

    /**
     * Compare and update error message result
     * @param rowNum Excel row number
     * @param accessToken Authentication token
     */
    public void compareAndUpdateErrorMessageResult(int rowNum, String accessToken) {
        try {
            String actualResult = getPostRequestErrorMessage(rowNum, accessToken);
            String expectedResult = getExpectedResultFromExcel(rowNum);

            ObjectMapper mapper = new ObjectMapper();

            boolean isJsonMatch = false;

            try {
                // Try parsing both as JSON
                JsonNode actualJson = mapper.readTree(actualResult);
                JsonNode expectedJson = mapper.readTree(expectedResult);
                isJsonMatch = actualJson.equals(expectedJson);
            } catch (Exception e) {
                // Not JSON, fall back to plain text comparison (ignoring whitespace)
                isJsonMatch = actualResult.trim().equalsIgnoreCase(expectedResult.trim());
            }

            if (isJsonMatch) {
                updateExcelSheet(rowNum, "Passed", actualResult);
                logger.info("Test passed: Actual error message matches the expected result.");
            } else {
                updateExcelSheet(rowNum, "Failed", actualResult);
                logger.error("Test failed: Actual error message does not match expected. \nActual: {} \nExpected: {}", actualResult, expectedResult);
            }

        } catch (Exception e) {
            logger.error("Error comparing results or updating Excel sheet: {}", e.getMessage());
        }
    }

    /**
     * Get the POST request response and set it in Excel
     * @param rowNum Excel row number
     * @param accessToken Authentication token
     * @return Response from the POST request
     * @throws Exception If any error occurs during the process
     */
    public Response getPostRequestResponseAndSetInExcel(int rowNum, String accessToken) throws Exception {
        // Create the POST request with the token in the request body
        PostWithAccessToken post = new PostWithAccessToken(logger, filePath, sheetName, url, body, accessToken);

        // Log the request details for debugging
        logger.info("Making POST request and setting Excel with token to row: {}", rowNum);

        Response response = post.post(rowNum);

        // Log the response for debugging
        logger.info("Excel update response status code: {}", response.getStatusCode());

        // Convert response body to pretty JSON string
        String responseJson = response.getBody().asPrettyString();
        logger.info("Excel update response body: {}", responseJson);

        // Write the JSON string into the "Expected Result" column in Excel
        setExpectedResultInExcel(rowNum, responseJson);

        // Return the full response
        return response;
    }

    /**
     * Set the expected result in Excel
     * @param rowNum Excel row number
     * @param result Expected result
     * @throws Exception If any error occurs during the process
     */
    private void setExpectedResultInExcel(int rowNum, String result) throws Exception {
        try {
            excelUtils.setCellData(filePath, sheetName, rowNum, ExpectedResult, result);
            logger.info("Set expected result in Excel for row {}", rowNum);
        } catch (Exception e) {
            logger.error("Error setting expected result in Excel: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * Get the expected result from Excel
     * @param rowNum Excel row number
     * @return Expected result
     */
    private String getExpectedResultFromExcel(int rowNum) {
        try {
            return excelUtils.getCellData(filePath, sheetName, rowNum, ExpectedResult);
        } catch (Exception e) {
            logger.error("Error getting expected result from Excel: {}", e.getMessage());
            return "";
        }
    }
}
