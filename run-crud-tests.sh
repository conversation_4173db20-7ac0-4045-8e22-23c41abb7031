#!/bin/bash

echo "========================================"
echo "CRUD Operations API Testing Framework"
echo "========================================"
echo

# Set Java options
export JAVA_OPTS="-Xmx2g -Dfile.encoding=UTF-8"

# Set test configuration
export TEST_CONFIG="-Dconfig.file=crud-config.properties"

# Set logging configuration
export LOG_CONFIG="-Dlogback.configurationFile=logback.xml"

# Check if argument is provided
if [ -z "$1" ]; then
    echo "No test type specified. Running complete suite..."
    TEST_TYPE="suite"
else
    TEST_TYPE="$1"
fi

echo "Starting CRUD Operations Tests [Type: $TEST_TYPE]..."
echo

# Function to check exit status (non-fatal)
check_status_warning() {
    if [ $? -ne 0 ]; then
        echo "WARNING: $1 failed! Continuing with other tests..."
    fi
}

# Function to check exit status (fatal)
check_status_fatal() {
    if [ $? -ne 0 ]; then
        echo "ERROR: $1 failed!"
        exit 1
    fi
}

# Run tests based on type
case "$TEST_TYPE" in
    "photos")
        echo "Running Photos API Tests Only..."
        mvn test -Dtest=PhotosApiTest $JAVA_OPTS $TEST_CONFIG $LOG_CONFIG
        check_status_fatal "Photos API Tests"
        ;;
    "products")
        echo "Running Products API Tests Only..."
        mvn test -Dtest=ProductsApiTest $JAVA_OPTS $TEST_CONFIG $LOG_CONFIG
        check_status_fatal "Products API Tests"
        ;;
    "auth")
        echo "Running Authentication Tests Only..."
        mvn test -Dtest=AuthenticationTest $JAVA_OPTS $TEST_CONFIG $LOG_CONFIG
        check_status_fatal "Authentication Tests"
        ;;
    "xml")
        echo "Running Tests from XML Configuration..."
        mvn test -DsuiteXmlFile=testng-crud-operations.xml $JAVA_OPTS $TEST_CONFIG $LOG_CONFIG
        check_status_fatal "XML Configuration Tests"
        ;;
    "runner")
        echo "Running Tests using CrudTestRunner..."
        mvn exec:java -Dexec.mainClass="runner.CrudTestRunner" -Dexec.args="suite" $JAVA_OPTS $TEST_CONFIG
        check_status_fatal "CrudTestRunner"
        ;;
    *)
        # Default: Run complete suite step by step
        echo "[1/4] Running Authentication Tests..."
        mvn test -Dtest=AuthenticationTest $JAVA_OPTS $TEST_CONFIG $LOG_CONFIG
        check_status_warning "Authentication Tests"

        echo
        echo "[2/4] Running Photos API Tests..."
        mvn test -Dtest=PhotosApiTest $JAVA_OPTS $TEST_CONFIG $LOG_CONFIG
        check_status_warning "Photos API Tests"

        echo
        echo "[3/4] Running Products API Tests..."
        mvn test -Dtest=ProductsApiTest $JAVA_OPTS $TEST_CONFIG $LOG_CONFIG
        check_status_warning "Products API Tests"

        echo
        echo "[4/4] Running Complete CRUD Test Suite..."
        mvn test -DsuiteXmlFile=testng-crud-operations.xml $JAVA_OPTS $TEST_CONFIG $LOG_CONFIG
        ;;
esac

# Check final result
if [ $? -eq 0 ]; then
    echo
    echo "========================================"
    echo "All Tests Completed Successfully!"
    echo "========================================"
else
    echo
    echo "========================================"
    echo "Some Tests Failed!"
    echo "========================================"
    echo "Check the reports for details."
fi

echo
echo "Check the following for results:"
echo "- Excel file: data/SnackHack.xlsx"
echo "- HTML Report: test-reports/crud-operations-report.html"
echo "- TestNG Reports: target/surefire-reports/"
echo "- Logs: logs/crud-operations.log"
echo
echo "Usage: ./run-crud-tests.sh [photos|products|auth|xml|runner|suite]"
echo "  photos   - Run Photos API tests only"
echo "  products - Run Products API tests only"
echo "  auth     - Run Authentication tests only"
echo "  xml      - Run from XML configuration"
echo "  runner   - Use CrudTestRunner class"
echo "  suite    - Run complete suite (default)"
echo

# Make the script executable
chmod +x run-crud-tests.sh
