#!/bin/bash

echo "========================================"
echo "CRUD Operations API Testing Framework"
echo "========================================"
echo

# Set Java options
export JAVA_OPTS="-Xmx2g -Dfile.encoding=UTF-8"

# Set test configuration
export TEST_CONFIG="-Dconfig.file=crud-config.properties"

# Set logging configuration
export LOG_CONFIG="-Dlogback.configurationFile=logback.xml"

echo "Starting CRUD Operations Tests..."
echo

# Function to check exit status
check_status() {
    if [ $? -ne 0 ]; then
        echo "ERROR: $1 failed!"
        exit 1
    fi
}

# Run Photos API Tests
echo "[1/3] Running Photos API Tests..."
mvn test -Dtest=PhotosApiTest $JAVA_OPTS $TEST_CONFIG $LOG_CONFIG
check_status "Photos API Tests"

echo
echo "[2/3] Running Products API Tests..."
mvn test -Dtest=ProductsApiTest $JAVA_OPTS $TEST_CONFIG $LOG_CONFIG
check_status "Products API Tests"

echo
echo "[3/3] Running Complete CRUD Test Suite..."
mvn test -DsuiteXmlFile=testng-crud-operations.xml $JAVA_OPTS $TEST_CONFIG $LOG_CONFIG
check_status "CRUD Test Suite"

echo
echo "========================================"
echo "All CRUD Operations Tests Completed!"
echo "========================================"
echo
echo "Check the following for results:"
echo "- Excel file: data/SnackHack.xlsx"
echo "- Test reports: target/surefire-reports/"
echo "- Logs: logs/crud-operations.log"
echo

# Make the script executable
chmod +x run-crud-tests.sh
