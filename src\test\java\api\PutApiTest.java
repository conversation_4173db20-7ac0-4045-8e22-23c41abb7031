package api;

import io.restassured.response.Response;
import org.slf4j.Logger;
import org.testng.Assert;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import api.helpers.PostWithDynamicRequestBody;

/**
 * Class for testing PUT API operations
 */
public class PutApiTest extends ApiTestBase {

    /**
     * Constructor
     * @param logger Logger instance
     * @param filePath Excel file path
     * @param sheetName Excel sheet name
     * @param url URL column index
     * @param body Request body column index
     * @param Status Status column index
     * @param ActualResult Actual result column index
     * @param ExpectedResult Expected result column index
     * @param tableName Table name column index
     */
    public PutApiTest(Logger logger, String filePath, String sheetName, int url, int body, int Status, int ActualResult, int ExpectedResult, int tableName) {
        super(logger, filePath, sheetName, url, body, Status, ActualResult, ExpectedResult, tableName);
    }

    /**
     * Update a record using PUT
     * @param rowNum Excel row number
     * @param accessToken Authentication token
     * @param id ID of the record to update
     * @return Response from the PUT request
     * @throws Exception If any error occurs during the process
     */
    public Response updateRecord(int rowNum, String accessToken, String id) throws Exception {
        // Step 1: Get the original request body from Excel
        String originalRequestBody = getRequestBodyFromExcel(rowNum);

        // Step 2: Create a proper request body for the PUT request
        String putRequestBody = createPutRequestBody(originalRequestBody, accessToken, id);

        // Step 3: Make the PUT request
        Response putResponse = makePutRequest(rowNum, putRequestBody);

        if (putResponse == null) {
            logger.error("PUT response is null");
            updateExcelSheet(rowNum, "Failed", "PUT response is null");
            return null;
        }

        // Step 4: Store the PUT response in the Actual Result column
        String putResponseBody = putResponse.getBody().asPrettyString();
        excelUtils.setCellData(filePath, sheetName, rowNum, ActualResult, putResponseBody);
        logger.info("Stored PUT response in Actual Result column");

        return putResponse;
    }

    /**
     * Update a record using PUT and compare with expected result
     * @param rowNum Excel row number
     * @param accessToken Authentication token
     * @param id ID of the record to update
     * @param expectedResult Expected result to compare with
     * @return Response from the PUT request
     * @throws Exception If any error occurs during the process
     */
    public Response updateRecordAndCompare(int rowNum, String accessToken, String id, String expectedResult) throws Exception {
        // Step 1: Update the record
        Response putResponse = updateRecord(rowNum, accessToken, id);

        if (putResponse == null) {
            return null;
        }

        // Step 2: Compare the response with the expected result
        String putResponseBody = putResponse.getBody().asPrettyString();
        boolean isMatch = compareResponses(putResponseBody, expectedResult);

        // Step 3: Update Excel with the result
        if (isMatch) {
            updateExcelSheet(rowNum, "Passed", "PUT response matches expected result");
        } else {
            updateExcelSheet(rowNum, "Failed", "PUT response does not match expected result");
        }

        return putResponse;
    }

    /**
     * Create a request body for the PUT API
     * @param originalRequestBody The original request body
     * @param accessToken Authentication token
     * @param id ID of the record to update
     * @return The request body for the PUT API
     * @throws Exception If any error occurs during the process
     */
    private String createPutRequestBody(String originalRequestBody, String accessToken, String id) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode requestNode;

        try {
            // Try to parse the original request body as JSON
            JsonNode root = mapper.readTree(originalRequestBody);

            // Check if it already has the required structure
            if (root.has("endpoint") && root.has("type") && root.has("payload")) {
                // It already has the required structure, use it as is
                requestNode = root.deepCopy();

                // Update the endpoint to use the ID
                String originalEndpoint = requestNode.get("endpoint").asText();
                String basePath = originalEndpoint;

                // Extract the base path without /save or /update
                if (basePath.contains("/save")) {
                    basePath = basePath.substring(0, basePath.indexOf("/save"));
                } else if (basePath.contains("/update")) {
                    basePath = basePath.substring(0, basePath.indexOf("/update"));
                } else if (basePath.contains("/list")) {
                    basePath = basePath.substring(0, basePath.indexOf("/list"));
                }

                // Create the PUT endpoint
                String putEndpoint = basePath + "/update";

                // Update the request node
                requestNode.put("endpoint", putEndpoint);
                requestNode.put("type", "put");

                // Update the payload to include the ID
                JsonNode payload = requestNode.get("payload");
                if (payload != null && payload.isObject()) {
                    ((ObjectNode) payload).put("id", id);
                }
            } else {
                // It doesn't have the required structure, create a new one
                requestNode = mapper.createObjectNode();
                requestNode.put("endpoint", "/core/api/CountryMaster/update");
                requestNode.put("type", "put");

                // Create a payload with the ID
                ObjectNode payload = mapper.createObjectNode();
                payload.put("id", id);
                requestNode.set("payload", payload);
            }
        } catch (Exception e) {
            // If parsing fails, create a simple PUT request
            requestNode = mapper.createObjectNode();
            requestNode.put("endpoint", "/core/api/CountryMaster/update");
            requestNode.put("type", "put");

            // Create a payload with the ID
            ObjectNode payload = mapper.createObjectNode();
            payload.put("id", id);
            requestNode.set("payload", payload);
        }

        // Ensure the auth token is set
        requestNode.put("auth", accessToken);

        // Convert to JSON string
        String putRequestBody = mapper.writeValueAsString(requestNode);
        logger.info("PUT request body: {}", putRequestBody);

        return putRequestBody;
    }

    /**
     * Make a PUT request
     * @param rowNum Excel row number
     * @param requestBody Request body
     * @return Response from the PUT request
     * @throws Exception If any error occurs during the process
     */
    private Response makePutRequest(int rowNum, String requestBody) throws Exception {
        PostWithDynamicRequestBody requestHandler = new PostWithDynamicRequestBody(logger, filePath, sheetName, url, body);
        Response putResponse = requestHandler.post(rowNum, requestBody);

        if (putResponse == null) {
            logger.error("PUT response is null");
            updateExcelSheet(rowNum, "Failed", "PUT response is null");
            return null;
        }

        int putStatusCode = putResponse.getStatusCode();
        String putResponseBody = putResponse.getBody().asPrettyString();

        logger.info("PUT response status code: {}", putStatusCode);
        logger.info("PUT response body: {}", putResponseBody);

        return putResponse;
    }
}
